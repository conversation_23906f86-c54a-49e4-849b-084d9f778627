import React, { useState, useContext } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { InsuranceContext } from "../../context/InsuranceContext";
import { FormField } from "../../components/FormFields";
import { SuccessMessage } from "../../components/SuccessMessage";

const ProductCreate = () => {
  const { companies = [], addProduct } = useContext(InsuranceContext);
  const navigate = useNavigate();
  const location = useLocation();
  const [companyId, setCompanyId] = useState(location.state?.companyId || "");
  const [formData, setFormData] = useState({
    productName: "",
    productType: "motor",
    description: "",
    isActive: true,
  });
  const [errors, setErrors] = useState({});
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const productTypes = [
    { value: "motor", label: "Motor Insurance" },
    { value: "life", label: "Life Insurance" },
    { value: "health", label: "Health Insurance" },
    { value: "property", label: "Property Insurance" },
    { value: "travel", label: "Travel Insurance" },
  ];
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
    if (errors[name]) setErrors((prev) => ({ ...prev, [name]: "" }));
  };
  const handleCompanyChange = (e) => {
    setCompanyId(e.target.value);
  };
  const validateForm = () => {
    const newErrors = {};
    if (!companyId) newErrors.company = "Insurance company is required";
    if (!formData.productName.trim())
      newErrors.productName = "Product name is required";
    if (!formData.description.trim())
      newErrors.description = "Description is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    setFormSubmitted(true);
    if (validateForm()) {
      const productData = {
        ...formData,
        id: Date.now().toString(),
        companyId,
        categories: [],
      };
      addProduct(productData);
      setSuccessMessage(
        `${formData.productName} product created successfully!`
      );
      setTimeout(() => {
        resetForm();
        navigate("/admin/cover-types/create", {
          state: {
            companyId,
            productId: productData.id,
          },
        });
      }, 2000);
    }
  };
  const resetForm = () => {
    setFormData({
      productName: "",
      productType: "motor",
      description: "",
      isActive: true,
    });
    setErrors({});
    setFormSubmitted(false);
  };
  const companyOptions =
    companies?.length > 0
      ? companies.map((company) => ({
          value: company.id,
          label: company.companyName,
        }))
      : [{ value: "", label: "No companies available", disabled: true }];
  return (
    <div className="min-h-screen flex bg-gray-50">
      <div className="flex-1 overflow-auto p-6">
        <div className="max-w-4xl mx-auto">
          <SuccessMessage
            message={successMessage}
            onClose={() => setSuccessMessage("")}
          />
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-800">
              Insurance Product Registration
            </h1>
            <p className="text-gray-600 mt-2">
              {companyId
                ? "Add product to selected company"
                : "Select company and register new insurance product"}
            </p>
          </div>
          {companies?.length === 0 && (
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg
                    className="h-5 w-5 text-yellow-400"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    No insurance companies found. Please{" "}
                    <a
                      href="/admin/insurers/create"
                      className="font-medium underline text-yellow-700 hover:text-yellow-600"
                    >
                      register a company
                    </a>{" "}
                    first.
                  </p>
                </div>
              </div>
            </div>
          )}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <form onSubmit={handleSubmit} className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <FormField
                    label="Insurance Company"
                    name="company"
                    type="select"
                    value={companyId}
                    onChange={handleCompanyChange}
                    options={companyOptions}
                    error={formSubmitted && errors.company}
                    required
                    disabled={
                      !!location.state?.companyId || companies?.length === 0
                    }
                  />
                  <FormField
                    label="Product Name"
                    name="productName"
                    value={formData.productName}
                    onChange={handleChange}
                    placeholder="e.g., Comprehensive Motor Cover"
                    error={formSubmitted && errors.productName}
                    required
                  />
                </div>
                <div className="space-y-6">
                  <FormField
                    label="Product Type"
                    name="productType"
                    type="select"
                    value={formData.productType}
                    onChange={handleChange}
                    options={productTypes}
                    required
                  />
                  <FormField
                    label="Description"
                    name="description"
                    type="textarea"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Describe the product coverage and benefits..."
                    error={formSubmitted && errors.description}
                    required
                    rows={4}
                  />
                </div>
              </div>
              <div className="mt-6 flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleChange}
                  className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                />
                <label
                  htmlFor="isActive"
                  className="ml-2 block text-sm text-gray-700"
                >
                  Active Product
                </label>
              </div>
              <div className="mt-8 pt-6 border-t border-gray-200 flex justify-between">
                <button
                  type="button"
                  onClick={resetForm}
                  className="px-6 py-2 text-gray-700 rounded-md font-medium hover:bg-gray-100 transition-colors"
                >
                  Reset
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-blue-600 text-white rounded-md font-medium shadow-sm hover:bg-blue-700 transition-colors"
                >
                  Register Product
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};
export default ProductCreate;
