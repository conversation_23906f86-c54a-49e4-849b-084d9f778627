<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\CategoryProduct;
use App\Models\Client;
use App\Models\Insurance;
use App\Models\InsuranceCategoryProduct;
use App\Models\Quotation;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class QuotationApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Setup: create broker and log in
        $this->broker = User::factory()->broker()->create();
        $this->actingAs($this->broker);

        // Related records
        $this->client = Client::factory()->create(['broker_id' => $this->broker->user_id]);
        $this->vehicle = Vehicle::factory()->create(['client_id' => $this->client->client_id]);
        $this->insurer = Insurance::factory()->create();
        $this->category = Category::factory()->create();
        $this->cover = CategoryProduct::factory()->create([
            'category_id' => $this->category->category_id,
        ]);

        $this->product = InsuranceCategoryProduct::factory()->create([
            'insurance_id' => $this->insurer->insurance_id,
            'category_id' => $this->category->category_id,
            'category_product_id' => $this->cover->category_product_id,
            'is_active' => true,
            'price' => 7500,
        ]);
    }

    public function test_can_create_quotation_from_recommendation()
    {

        // Build request payload
        $payload = [
            'client_id' => $this->client->client_id,
            'broker_id' => $this->broker->user_id,
            'recommendation' => [
                'vehicle_id' => $this->vehicle->vehicle_id,
                'product_id' => $this->product->insurance_category_product_id,
                'price' => 7500,
                'cover_type' => $this->cover->category_product_name,
                'cover_description' => $this->cover->category_product_description ?? 'N/A',
                'fitness_score' => 0.87,
            ],
        ];

        $response = $this->postJson('/api/v1/quote/from-recommendation', $payload);

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Quotation created from recommendation.',
            ]);

        $this->assertDatabaseHas('IBMS_QUOTATIONS', [
            'client_id' => $this->client->client_id,
            'vehicle_id' => $this->vehicle->vehicle_id,
            'insurance_category_product_id' => $this->product->insurance_category_product_id,
            'total_price' => 7500,
            'status' => 'pending',
        ]);
    }

    public function test_store_creates_quotation_successfully()
    {
        $payload = [
            'client_id' => $this->client->client_id,
            'vehicle_id' => $this->vehicle->vehicle_id,
            'insurance_category_product_id' => $this->product->insurance_category_product_id,
            'status' => 'pending',
            'total_price' => 9800,
            'coverage_details' => [
                'cover_type' => 'Comprehensive',
                'cover_description' => 'Full vehicle insurance coverage',
                'fitness_score' => 0.88,
            ],
            'valid_until' => now()->addDays(14)->toDateString(),
        ];

        $response = $this->postJson('/api/v1/quote/create', $payload);

        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Quotation created successfully',
            ]);

        $this->assertDatabaseHas('IBMS_QUOTATIONS', [
            'client_id' => $payload['client_id'],
            'vehicle_id' => $payload['vehicle_id'],
            'insurance_category_product_id' => $payload['insurance_category_product_id'],
            'total_price' => $payload['total_price'],
            'status' => 'pending',
        ]);
    }

    public function test_index()
    {
        $this->test_store_creates_quotation_successfully();
        $this->test_can_create_quotation_from_recommendation();

        $response = $this->getJson('/api/v1/quote');

        $data = $response->json();

        // var_dump($data);
        $response->assertStatus(200);
    }

    public function test_show()
    {
        $this->test_store_creates_quotation_successfully();
        $quote = Quotation::first();

        $response = $this->getJson("/api/v1/quote/{$quote->quotation_id}");

        $response->assertStatus(200);
        $data = $response->json(); // Extract the response JSON

        $this->assertEquals($quote->quotation_id, $data['quotation_id']);
    }
}
