<?php

namespace Database\Factories;

use App\Models\IBMSPermissions;
use Illuminate\Database\Eloquent\Factories\Factory; // ✅ Fix: Import the class here

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Role>
 */
class RoleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $brokerPermissions = [
            IBMSPermissions::IBMS_ADD_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_EDIT_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_ADD_CLIENT,
            IBMSPermissions::IBMS_EDIT_CLIENT,
            IBMSPermissions::IBMS_VIEW_CLIENT,
            IBMSPermissions::IBMS_CREATE_QUOTATION,
            IBMSPermissions::IBMS_EDIT_QUOTATION,
            IBMSPermissions::IBMS_VIEW_QUOTATION,
            IBMSPermissions::IBMS_VIEW_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_ENABLE_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_DISABLE_INSURANCE_COMPANY,
        ];

        return [
            'role_name' => 'SampleRole',
            'role_permissions' => array_sum($brokerPermissions),
            //
        ];
    }
}
