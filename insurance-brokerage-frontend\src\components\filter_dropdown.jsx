import React from 'react';

const FilterDropdown = ({ filterStatus, onFilterChange }) => {
    return (
        <div className="relative w-full">
            <label htmlFor="status-filter" className="sr-only">Filter by Status</label> {/* Accessibility */}
            <select
                id="status-filter"
                value={filterStatus}
                onChange={(e) => onFilterChange(e.target.value)}
                className="block w-full pl-3 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
                <option value="All">All Statuses</option>
                <option value="Active">Active</option>
                <option value="Inactive">Inactive</option>
                <option value="Pending">Pending</option>
                {/* Add more statuses as needed */}
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95a1 1 0 001.414 0l4-4a1 1 0 00-1.414-1.414L10 10.586 6.707 7.293a1 1 0 00-1.414 1.414l4 4z"/></svg>
            </div>
        </div>
    );
};

export default FilterDropdown;