<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('IBMS_CLIENT_PREFERENCES', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('vehicle_id')->unique(); // 1-to-1 with Vehicle
            $table->json('preferred_cover_types')->nullable(); // JSON array of cover types
            $table->decimal('budget_min', 10, 2)->nullable();
            $table->decimal('budget_max', 10, 2)->nullable();
            $table->json('preferred_insurers')->nullable();
            $table->json('blacklisted_insurers')->nullable();
            $table->enum('preferred_channel', ['email', 'phone', 'whatsapp', 'sms'])->default('email');
            $table->enum('preferred_contact_time', ['morning', 'afternoon', 'evening'])->nullable();
            $table->boolean('allow_marketing_emails')->default(false);
            $table->boolean('auto_renewal_enabled')->default(false);
            $table->boolean('renewal_reminder_enabled')->default(true);
            $table->integer('days_before_renewal_reminder')->default(14);
            $table->json('vehicle_age_range')->nullable();
            $table->enum('vehicle_usage', ['personal', 'commercial', 'public'])->default('personal');
            $table->json('preferred_workshops')->nullable();
            $table->boolean('digital_document_delivery')->default(true);
            $table->boolean('physical_document_delivery')->default(false);
            $table->boolean('require_detailed_explanation')->default(false);
            $table->integer('profile_score')->default(0);
            $table->timestamp('last_updated_by_broker')->nullable();

            $table->foreign('vehicle_id')->references('vehicle_id')->on('IBMS_VEHICLES')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IBMS_CLIENT_PREFERENCES');
    }
};
