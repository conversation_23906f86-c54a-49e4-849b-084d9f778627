import React, { useState, useEffect } from "react";
import { <PERSON>, PageHeader, <PERSON>ton, Alert } from "../../components";
import FormField from "../../components/FormFields";
import { benefitService, policyService } from "../../services";
import { useNavigate } from "react-router-dom";

export default function BenefitCreate() {
  const [form, setForm] = useState({
    name: "",
    description: "",
    status: "Active",
    policies: ""
  });
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [alertMessage, setAlertMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [validation, setValidation] = useState({});
  const [policies, setPolicies] = useState([]);
  const [optionsLoading, setOptionsLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    async function fetchPolicies() {
      setOptionsLoading(true);
      try {
        const policiesData = await policyService.getPolicies();
        setPolicies(policiesData);
      } catch (err) {
        // Optionally handle error
      } finally {
        setOptionsLoading(false);
      }
    }
    fetchPolicies();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    setValidation((prev) => ({ ...prev, [name]: "" }));
  };

  const validate = () => {
    const errors = {};
    if (!form.name) errors.name = "Benefit name is required";
    if (!form.description) errors.description = "Description is required";
    if (!form.status) errors.status = "Status is required";
    if (!form.policies) errors.policies = "Policy is required";
    setValidation(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validate()) return;
    setLoading(true);
    try {
      await benefitService.createBenefit({
        name: form.name,
        description: form.description,
        status: form.status,
        policies: [Number(form.policies)],
      });
      setShowAlert(true);
      setAlertType("success");
      setAlertMessage("Benefit created successfully!");
      setForm({ name: "", description: "", status: "Active", policies: "" });
    } catch (err) {
      setShowAlert(true);
      setAlertType("error");
      setAlertMessage("Failed to create benefit");
    } finally {
      setLoading(false);
    }
  };

  if (optionsLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading options...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
        <PageHeader
          title="Add Benefit"
          subtitle="Create a new insurance benefit"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Benefits", href: "/admin/benefits" },
            { label: "Add Benefit" },
          ]}
        />
        <Card title="New Benefit">
          {showAlert && (
            <Alert
              type={alertType}
              title={alertType === "success" ? "Success" : "Error"}
              message={alertMessage}
              onClose={() => setShowAlert(false)}
            />
          )}
          <form className="space-y-6" onSubmit={handleSubmit}>
            <FormField
              label="Benefit Name"
              name="name"
              value={form.name}
              onChange={handleChange}
              placeholder="Enter benefit name"
              isRequired={true}
              validationError={validation.name}
            />
            <FormField
              label="Description"
              name="description"
              type="textarea"
              value={form.description}
              onChange={handleChange}
              placeholder="Enter description"
              isRequired={true}
              validationError={validation.description}
            />
            <FormField
              label="Status"
              name="status"
              type="select"
              value={form.status}
              onChange={handleChange}
              options={[
                { value: "Active", label: "Active" },
                { value: "Inactive", label: "Inactive" },
              ]}
              isRequired={true}
              validationError={validation.status}
            />
            <FormField
              label="Policy"
              name="policies"
              type="select"
              value={form.policies}
              onChange={handleChange}
              options={policies.map((policy) => ({ value: policy.id, label: policy.name }))}
              isRequired={true}
              validationError={validation.policies}
            />
            <div className="flex justify-end">
              <Button type="submit" variant="primary" loading={loading}>
                Create Benefit
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
}
