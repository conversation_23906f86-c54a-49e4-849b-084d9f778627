<?php

namespace App\Models;

use ReflectionClass;

class RoleSetupService
{
    public static function createRoles(): void
    {
        $service = new self;

        $allPermissions = (new ReflectionClass(IBMSPermissions::class))->getConstants();

        $brokerPermissions = [
            IBMSPermissions::IBMS_ADD_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_EDIT_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_ADD_CLIENT,
            IBMSPermissions::IBMS_EDIT_CLIENT,
            IBMSPermissions::IBMS_VIEW_CLIENT,
            IBMSPermissions::IBMS_CREATE_QUOTATION,
            IBMSPermissions::IBMS_EDIT_QUOTATION,
            IBMSPermissions::IBMS_VIEW_QUOTATION,
            IBMSPermissions::IBMS_VIEW_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_ENABLE_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_DISABLE_INSURANCE_COMPANY,
        ];

        $adminCreated = $service->setupRole('ADMIN', $allPermissions);
        $brokerCreated = $service->setupRole('BROKER', $brokerPermissions);

        if ($adminCreated && $brokerCreated) {
            echo "All roles created successfully.\n";
        } else {
            echo "Some roles could not be created.\n";
        }
    }

    public function setupRole(string $roleName, array $permissions): bool
    {
        foreach ($permissions as $perm_key => $perm_value) {
            if (! in_array($perm_value, (new ReflectionClass(IBMSPermissions::class))->getConstants(), true)) {
                echo "Undefined permission $perm_value\n";

                return false;
            }
        }
        $role = new Role([
            'role_name' => $roleName,
            'role_permissions' => array_sum($permissions),
        ]);

        if ($role->save()) {
            echo "Added Role: $roleName\n";

            return true;
        }

        echo "Error adding Role: $roleName\n";

        return false;
    }
}
