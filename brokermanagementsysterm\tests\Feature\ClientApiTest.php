<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\RoleSetupService;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ClientApiTest extends TestCase
{
    /**
     * A basic feature test example.
     */
    use RefreshDatabase,WithFaker;

    public function test_example(): void
    {
        $response = $this->get('/');

        $response->assertStatus(200);
    }

    public function test_example_noauth(): void
    {
        $response = $this->getJson('/api/v1/clients/1');

        $response->assertStatus(401);
    }

    public function test_can_create_client(): void
    {
        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();

        $this->assertNotNull($testBroker->user_id);
        $this->actingAs($testBroker);

        $fakeEmail = $this->faker->email;
        $data = [
            'first_name' => $this->faker->name,
            'last_name' => $this->faker->name,
            'contact_phone' => $this->faker->phoneNumber,
            'id_number' => random_int(00000000, 99999999),
            'contact_email' => $fakeEmail,
        ];

        $this->actingAs($testBroker);
        $response = $this->postJson('/api/v1/clients', $data);

        $response->assertStatus(201);

        $this->assertDatabaseHas('IBMS_CLIENTS', [
            'contact_email' => $fakeEmail,
        ]);
    }

    public function test_can_list_clients(): void
    {

        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();

        $this->assertNotNull($testBroker->user_id);
        $this->actingAs($testBroker);

        $this->test_can_create_client();
        // $this->test_can_create_client();
        // $this->test_can_create_client();
        // $this->test_can_create_client();

        $response = $this->getJson('/api/v1/clients');

        $response->assertStatus(200);
        $response->assertJsonCount(1);
    }

    public function test_can_show_client(): void
    {
        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();

        $this->assertNotNull($testBroker->user_id);
        $this->actingAs($testBroker);

        $this->test_can_create_client();

        $client = Client::first();

        $response = $this->getJson("/api/v1/clients/{$client->client_id}");

        $response->assertStatus(200);

        // $response->assertJsonCount(1);

        $response->assertJsonFragment([
            'client_id' => $client->client_id, ]);
    }

    public function test_client_not_found()
    {
        $this->test_can_create_client();
        $response = $this->getJson('/api/v1/clients/999');

        $response->assertStatus(404)
            ->assertJson(['message' => 'Client not found']);
    }

    public function test_can_delete_client(): void
    {
        // $this->test_can_create_client();
        // // $this->test_can_create_client();
        // // $this->test_can_create_client();

        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();

        $this->assertNotNull($testBroker->user_id);

        $fakeEmail = $this->faker->email;
        // $data = [
        //     'first_name' => $this->faker->name,
        //     'last_name' => $this->faker->name,
        //     'contact_phone' => $this->faker->phoneNumber,
        //     'id_number' => random_int(00000000, 99999999),
        //     'broker_id' => $testBroker->user_id,
        //     'contact_email' => $fakeEmail,
        // ];

        $this->actingAs($testBroker);
        for ($i = 0; $i < 3; $i++) {
            $data = [
                'first_name' => $this->faker->firstName,
                'last_name' => $this->faker->lastName,
                'contact_phone' => $this->faker->unique()->phoneNumber,
                'id_number' => $this->faker->unique()->numberBetween(10000000, 99999999),
                'contact_email' => $this->faker->unique()->safeEmail,
            ];

            $this->postJson('/api/v1/clients', $data);
        }
        // $response = $this->postJson('/api/v1/clients', $data);
        // $response = $this->postJson('/api/v1/clients', $data);
        // $response = $this->postJson('/api/v1/clients', $data);

        $__response = $this->getJson('/api/v1/clients');

        $__response->assertStatus(200)
            ->assertJsonCount(3);

        $client = Client::first();

        $response = $this->deleteJson("/api/v1/clients/{$client->client_id}");

        $response->assertStatus(200);

        $anotherResponse = $this->getJson("/api/v1/client/{$client->client_id}");

        $anotherResponse->assertStatus(404);

        $_response = $this->getJson('/api/v1/clients');
        $_response->assertStatus(200)
            ->assertJsonCount(2);

    }

    public function test_required_values(): void
    {

        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();

        $this->actingAs($testBroker);

        $response = $this->postJson('/api/v1/clients', []); // Send empty data

        $response->assertStatus(422); // Laravel responds with 422 Unprocessable Entity

        $response->assertJsonValidationErrors([
            'first_name',
            'last_name',
            'contact_phone',
            'id_number',
            'contact_email',
        ]);
    }

    public function test_can_update_client(): void
    {
        $this->test_can_create_client();

        $update = [
            'first_name' => 'Jane',
        ];

        $client = Client::first();

        $this->assertNotNull($client);

        $response = $this->putJson("/api/v1/clients/{$client->client_id}", $update);

        $response->assertStatus(200)
            ->assertJsonFragment(['first_name' => 'Jane']);

        $this->assertDatabaseHas('IBMS_CLIENTS', [
            'client_id' => $client->client_id,
            'first_name' => 'Jane',
        ]);
    }
}
