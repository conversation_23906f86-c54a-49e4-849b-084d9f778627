import axios from '../utils/axios';
import { 
  mockInsuranceCompanies, 
  mockCompanyStats
} from './mockData';

// Insurance Companies API
export const insuranceService = {
  // Get all insurance companies
  getCompanies: async () => {
    try {
      const response = await axios.get('/api/v1/insurance/companies');
      return response.data;
    } catch (error) {
      console.error('Error fetching insurance companies:', error);
      throw error;
    }
  },

  // Get single insurance company
  getCompany: async (id) => {
    try {
      // For now, return mock data
      // const response = await axios.get(`/api/insurance-companies/${id}`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      const company = mockInsuranceCompanies.find(c => c.id === parseInt(id));
      if (!company) {
        throw new Error('Company not found');
      }
      console.log(company);
      return company;
    } catch (error) {
      throw error;
    }
  },

  // Create new insurance company
  createCompany: async (companyData) => {
    try {
      // For now, simulate creation
      // const response = await axios.post('/api/insurance-companies', companyData);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newCompany = {
        ...companyData,
        id: Date.now(),
        productsCount: 0,
        policiesCount: 0,
        createdAt: new Date().toISOString()
      };
      
      // In a real app, this would be added to the database
      mockInsuranceCompanies.push(newCompany);
      
      return newCompany;
    } catch (error) {
      throw error;
    }
  },

  // Update insurance company
  updateCompany: async (id, companyData) => {
    try {
      // For now, simulate update
      // const response = await axios.put(`/api/insurance-companies/${id}`, companyData);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = mockInsuranceCompanies.findIndex(c => c.id === parseInt(id));
      if (index === -1) {
        throw new Error('Company not found');
      }
      
      mockInsuranceCompanies[index] = { ...mockInsuranceCompanies[index], ...companyData };
      return mockInsuranceCompanies[index];
    } catch (error) {
      throw error;
    }
  },

  // Delete insurance company
  deleteCompany: async (id) => {
    try {
      // For now, simulate deletion
      // const response = await axios.delete(`/api/insurance-companies/${id}`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const index = mockInsuranceCompanies.findIndex(c => c.id === parseInt(id));
      if (index === -1) {
        throw new Error('Company not found');
      }
      
      mockInsuranceCompanies.splice(index, 1);
      return { success: true };
    } catch (error) {
      throw error;
    }
  },

  // Get company statistics
  getCompanyStats: async (id) => {
    try {
      // For now, return mock data
      // const response = await axios.get(`/api/insurance-companies/${id}/stats`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 200));
      return mockCompanyStats;
    } catch (error) {
      throw error;
    }
  }
}; 