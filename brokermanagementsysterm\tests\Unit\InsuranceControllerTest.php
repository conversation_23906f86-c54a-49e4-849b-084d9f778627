<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\CategoryProduct;
use App\Models\Insurance;
use App\Models\InsuranceCategoryProduct;
use App\Models\InsuranceProductPolicy;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InsuranceControllersTest extends TestCase
{
    use RefreshDatabase;

    /*****************************************************************
     * InsuranceController Tests
     *****************************************************************/
    public function test_example(): void
    {
        $this->assertTrue(true);
    }

    /** @test */
    // public function it_can_create_an_insurance_company()
    // {
    //     $data = [
    //         'name' => 'Test Insurance',
    //         'email' => '<EMAIL>',
    //         'phone_number' => '1234567890',
    //         'is_active' => true,
    //     ];

    //     $response = $this->postJson('/api/insurances', $data);

    //     $response->assertStatus(201)
    //         ->assertJsonFragment($data);
    // }

    // /** @test */
    // public function it_can_list_insurances()
    // {
    //     Insurance::factory()->count(3)->create();
    //     $response = $this->getJson('/api/insurances');
    //     $response->assertStatus(200)->assertJsonCount(3);
    // }

    // /*****************************************************************
    //  * InsuranceProductController Tests
    //  *****************************************************************/

    // /** @test */
    // public function it_can_link_insurance_to_category_product()
    // {
    //     $insurance = Insurance::factory()->create();
    //     $category = Category::factory()->create();
    //     $product = CategoryProduct::factory()->create(['category_id' => $category->category_id]);

    //     $data = [
    //         'insurance_id' => $insurance->insurance_id,
    //         'category_id' => $category->category_id,
    //         'category_product_id' => $product->category_product_id,
    //     ];

    //     $response = $this->postJson('/api/insurance-products', $data);
    //     $response->assertStatus(201);
    // }

    // /*****************************************************************
    //  * InsurancePolicyController Tests
    //  *****************************************************************/

    // /** @test */
    // public function it_can_create_an_insurance_policy()
    // {
    //     $insuranceProduct = InsuranceCategoryProduct::factory()->create();

    //     $data = [
    //         'insurance_category_product_id' => $insuranceProduct->insurance_category_product_id,
    //         'policy_code' => 'POL-001',
    //         'base_premium' => 1000.00,
    //         'sum_insured' => 100000.00,
    //     ];

    //     $response = $this->postJson('/api/policies', $data);
    //     $response->assertStatus(201);
    // }

    // /*****************************************************************
    //  * PolicyBenefitController Tests
    //  *****************************************************************/

    // /** @test */
    // public function it_can_add_benefits_to_a_policy()
    // {
    //     $policy = InsuranceProductPolicy::factory()->create();

    //     $data = [
    //         'insurance_product_policy_id' => $policy->insurance_product_policy_id,
    //         'deescription' => 'Test Benefit',
    //     ];

    //     $response = $this->postJson('/api/policy-benefits', $data);
    //     $response->assertStatus(201);
    // }

    // /** @test */
    // public function it_can_soft_delete_an_insurance_company()
    // {
    //     $insurance = Insurance::factory()->create();
    //     $response = $this->deleteJson("/api/insurances/{$insurance->insurance_id}");
    //     $response->assertStatus(204);
    //     $this->assertSoftDeleted($insurance);
    // }

    // Add more test methods for other controller actions...
}
