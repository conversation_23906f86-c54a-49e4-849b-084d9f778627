<?php

namespace Database\Factories;

use App\Models\Role;
use App\Models\RoleSetupService;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {

        $roleSetup = new RoleSetupService;
        $roleSetup::createRoles();

        return [
            'first_name' => fake()->name(),
            'last_name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'verified_at' => now(),
            'password' => 'password',
            'remember_token' => Str::random(10),
            'email_verified' => false,
            'phone_number' => fake()->phoneNumber(),
            'role_id' => fake()->uuid(),
            'id_number' => fake()->unique()->numerify('###########'),
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    public function broker(): static
    {
        return $this->state(function (array $attributes) {
            $brokerRole = Role::where('role_name', 'BROKER')->firstOrFail();

            return [
                'role_id' => $brokerRole->role_id,
            ];
        });
    }

    public function admin(): static
    {
        return $this->state(function (array $attributes) {
            $adminRole = Role::where('role_name', 'ADMIN')->firstOrFail();

            return [
                'role_id' => $adminRole->role_id,
            ];
        });
    }
}
