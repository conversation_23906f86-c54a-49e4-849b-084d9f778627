import React, { useState, useEffect } from "react";
import { Card, PageHeader, Button, Alert } from "../../components";
import FormField from "../../components/FormFields";
import { coverTypeService, insuranceService } from "../../services";

export default function CoverTypeCreate() {
  const [form, setForm] = useState({
    name: "",
    description: "",
    insurerId: ""
  });
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [alertMessage, setAlertMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [validation, setValidation] = useState({});
  const [insurers, setInsurers] = useState([]);
  const [optionsLoading, setOptionsLoading] = useState(true);

  useEffect(() => {
    async function fetchInsurers() {
      setOptionsLoading(true);
      try {
        const insurersData = await insuranceService.getCompanies();
        setInsurers(insurersData);
      } catch (err) {
        // Optionally handle error
      } finally {
        setOptionsLoading(false);
      }
    }
    fetchInsurers();
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    setValidation((prev) => ({ ...prev, [name]: "" }));
  };

  const validate = () => {
    const errors = {};
    if (!form.name) errors.name = "Cover type name is required";
    if (!form.description) errors.description = "Description is required";
    if (!form.insurerId) errors.insurerId = "Insurer is required";
    setValidation(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validate()) return;
    setLoading(true);
    try {
      await coverTypeService.createCoverType({
        name: form.name,
        description: form.description,
        category: "Motor",
        insurerId: form.insurerId,
        status: "Active"
      });
      setShowAlert(true);
      setAlertType("success");
      setAlertMessage("Cover type created successfully!");
      setForm({ name: "", description: "", insurerId: "" });
    } catch (err) {
      setShowAlert(true);
      setAlertType("error");
      setAlertMessage("Failed to create cover type");
    } finally {
      setLoading(false);
    }
  };

  if (optionsLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading options...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
        <PageHeader
          title="Add Cover Type"
          subtitle="Create a new motor insurance cover type for an insurer"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Cover Types", href: "/admin/cover-types" },
            { label: "Add Cover Type" },
          ]}
        />
        <Card title="New Cover Type">
          {showAlert && (
            <Alert
              type={alertType}
              title={alertType === "success" ? "Success" : "Error"}
              message={alertMessage}
              onClose={() => setShowAlert(false)}
            />
          )}
          <form className="space-y-6" onSubmit={handleSubmit}>
            <FormField
              label="Cover Type Name"
              name="name"
              value={form.name}
              onChange={handleChange}
              placeholder="Enter cover type name"
              isRequired={true}
              validationError={validation.name}
            />
            <FormField
              label="Description"
              name="description"
              type="textarea"
              value={form.description}
              onChange={handleChange}
              placeholder="Enter description"
              isRequired={true}
              validationError={validation.description}
            />
            <FormField
              label="Insurer"
              name="insurerId"
              type="select"
              value={form.insurerId}
              onChange={handleChange}
              options={insurers.map((insurer) => ({ value: insurer.id, label: insurer.name }))}
              isRequired={true}
              validationError={validation.insurerId}
            />
            <div className="flex justify-end">
              <Button type="submit" variant="primary" loading={loading}>
                Create Cover Type
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
}
