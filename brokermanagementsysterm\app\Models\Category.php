<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory, HasUuids;

    protected $primaryKey = 'category_id';

    protected $keyType = 'string';

    public $incrementing = false;

    protected $table = 'IBMS_CATEGORIES';

    protected $fillable = [
        'category_name', 'category_description',
    ];

    public function categoryProducts()
    {
        return $this->hasMany(CategoryProduct::class, 'category_id');
    }

    public function insuranceCategoryProducts()
    {
        return $this->hasMany(InsuranceCategoryProduct::class, 'category_id');
    }
}
