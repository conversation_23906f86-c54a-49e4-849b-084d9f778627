import React, { useState, useEffect } from "react";

import { useNavigate } from "react-router-dom";
import Card from "../../components/Card";
import Button from "../../components/Button";
import Alert from "../../components/Alert";
import DataTable from "../../components/DataTable";
import PageHeader from "../../components/PageHeader";
import { clientService } from "../../services/clientService";
import Modal from "../../components/Modal";

// const mockClients = [
//   { id: 1, firstName: "John", lastName: "Doe", idNumber: "123456", phone: "0700000001", email: "<EMAIL>" },
//   { id: 2, firstName: "Jane", lastName: "Smith", idNumber: "654321", phone: "0700000002", email: "<EMAIL>" },
//   { id: 3, firstName: "Alice", lastName: "<PERSON>", idNumber: "789012", phone: "0700000003", email: "<EMAIL>" },
// ];

export default function ClientList() {
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);

  const [alert, setAlert] = useState({ show: false, type: "info", title: "", message: "" });
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [clientToDelete, setClientToDelete] = useState(null);
  const navigate = useNavigate();

   useEffect(() => {
      const fetchClients = async () => {
        try {
          const clients_res = await clientService.getClients();
          setClients(clients_res);
          console.log('clients data', clients_res);
        } catch (error) {
          console.error('Failed to fetch clients', error);
        }
      };

      fetchClients();
    }, []);
  
     useEffect(() => {
      // if (userRes) {
       if (Object.keys(clients).length === 0 && clients.constructor === Object) {
         setLoading(true);
       }
       else {
         setLoading(false);
  
       }
     }, [clients]);
  // Action handlers
  const handleView = (client) => {
    navigate(`/broker/clients/${client.client_id}`, { state: { client } });
  };

  const handleEdit = (client) => {
    navigate(`/broker/clients/${client.client_id}/edit`, { state: { client } });
  };

  const handleDelete = (client) => {
    setClientToDelete(client);
    setShowDeleteModal(true);
  };

  const confirmDelete = async () => {
    if (!clientToDelete || deleting) return;

    setDeleting(true);

    try {
      await clientService.deleteClient(clientToDelete);

      // Remove the deleted client from the local state
      setClients(clients.filter(client => client.client_id !== clientToDelete.client_id));

      // Show success alert
      setAlert({
        show: true,
        type: "success",
        title: "Client Deleted",
        message: `Client ${clientToDelete.first_name} ${clientToDelete.last_name} has been successfully deleted.`,
      });

      // Auto-hide success alert after 5 seconds
      setTimeout(() => {
        setAlert(prev => ({ ...prev, show: false }));
      }, 5000);

      // Close the modal and reset state
      setShowDeleteModal(false);
      setClientToDelete(null);

    } catch (error) {
      console.error("Error deleting client:", error);

      // Show error alert
      setAlert({
        show: true,
        type: "error",
        title: "Delete Failed",
        message: error?.response?.data?.message || error.message || "Failed to delete client. Please try again.",
      });

      // Close the modal but keep the client data for potential retry
      setShowDeleteModal(false);
    } finally {
      setDeleting(false);
    }
  };

  const cancelDelete = () => {
    setShowDeleteModal(false);
    setClientToDelete(null);
  };

  const handleVehicle = (client) => {
    navigate("/broker/vehicles/details", { state: { client } });
  };

  

  const columns = [
    { key: "first_name", label: "First Name", sortable: true },
    { key: "last_name", label: "Last Name", sortable: true },
    { key: "id_number", label: "ID Number", sortable: true },
    { key: "contact_phone", label: "Phone Number", sortable: true },
    {
      key: "actions",
      label: "Actions",
      render: (_, client) => (
        <div className="flex gap-2">
          <Button size="small" variant="outline" onClick={() => handleView(client)}>
            View
          </Button>
          <Button size="small" variant="primary" onClick={() => handleEdit(client)}>
            Edit
          </Button>
          <Button size="small" variant="danger" onClick={() => handleDelete(client)}>
            Delete
          </Button>
          <Button size="small" variant="secondary" onClick={() => handleVehicle(client)}>
            Vehicles
          </Button>
        </div>
      ),
    },
  ];


  return (
    <div className="min-h-screen bg-gray-50 flex flex-col ">
      {/* <div className="flex-1 flex flex-col items-center justify-center px-2 w-full"> */}
        <div className="w-full mx-auto">
          <PageHeader
            title="Clients"
            subtitle="Manage and view all registered clients in the system."
            breadcrumbs={[
              { label: "Dashboard", href: "/broker/dashboard" },
              { label: "Clients" },
            ]}
            actions={[
              {
                label: (
                  <span className="flex items-center gap-2">
                    Add New Client
                  </span>
                ),
                variant: "primary",
                onClick: () => navigate("/broker/clients/create"),
              },
            ]}
          />
          <div className="mt-8 w-full">
            <Card
              title="DataTable Component"
              subtitle="Advanced table with sorting, searching, and pagination"
              className="w-full max-auto"
            >
              {alert.show && (
                <div className="mb-4 w-full mx-auto">
                  <Alert
                    type={alert.type}
                    title={alert.title}
                    message={alert.message}
                    onClose={() => setAlert({ ...alert, show: false })}
                  />
                </div>
              )}
              <div className="relative" style={{ zIndex: 10 }}>
                <div className="overflow-y-auto" style={{ maxHeight: 500 }}>
                  <DataTable
                    data={clients}
                    columns={columns}
                    searchable={true}
                    pagination={true}
                    itemsPerPage={5}
                  />
                  <Modal
                    isOpen={showDeleteModal}
                    onClose={cancelDelete}
                    title="Delete Client"
                    size="small"
                  >
                    <div className="space-y-4">
                      <p>Are you sure you want to delete <strong>Client {clientToDelete?.first_name} {clientToDelete?.last_name}</strong>?</p>
                      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                        <p className="text-sm text-yellow-800">
                          <strong>Warning:</strong> This action cannot be undone. All associated data including vehicles, quotes, and policies will be permanently removed.
                        </p>
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={cancelDelete} disabled={deleting}>
                          Cancel
                        </Button>
                        <Button variant="danger" onClick={confirmDelete} disabled={deleting}>
                          {deleting ? "Deleting..." : "Delete"}
                        </Button>
                      </div>
                    </div>
                  </Modal>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    // </div>
  );
}
