import React, { useState, useEffect } from "react";

import { useNavigate } from "react-router-dom";
import Card from "../../components/Card";
import Button from "../../components/Button";
import Alert from "../../components/Alert";
import DataTable from "../../components/DataTable";
import PageHeader from "../../components/PageHeader";
import { clientService } from "../../services/clientService";
import Modal from "../../components/Modal";

// const mockClients = [
//   { id: 1, firstName: "John", lastName: "Doe", idNumber: "123456", phone: "0700000001", email: "<EMAIL>" },
//   { id: 2, firstName: "Jane", lastName: "Smith", idNumber: "654321", phone: "0700000002", email: "<EMAIL>" },
//   { id: 3, firstName: "Alice", lastName: "<PERSON>", idNumber: "789012", phone: "0700000003", email: "<EMAIL>" },
// ];

export default function ClientList() {
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);

  
  const [alert, setAlert] = useState({ show: false, type: "info", title: "", message: "" });
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [clientToDelete, setClientToDelete] = useState(null);
  const navigate = useNavigate();

   useEffect(() => {
      const fetchClients = async () => {
        try {
          const clients_res = await clientService.getClients();
          setClients(clients_res);
          console.log('clients data', clients_res);
        } catch (error) {
          console.error('Failed to fetch clients', error);
        }
      };

      fetchClients();
    }, []);
  
     useEffect(() => {
      // if (userRes) {
       if (Object.keys(clients).length === 0 && clients.constructor === Object) {
         setLoading(true);
       }
       else {
         setLoading(false);
  
       }
     }, [clients]);
  // Action handlers
  const handleView = (client) => {
    navigate(`/broker/clients/${client.client_id}`, { state: { client } });
  };

  const handleEdit = (client) => {
    navigate(`/broker/clients/${client.client_id}/edit`, { state: { client } });
  };

  const handleDelete = (client) => {
    setClientToDelete(client);
    setShowDeleteModal(true);
  };

  const handleVehicle = (client) => {
    navigate("/broker/vehicles/details", { state: { client } });
  };

  

  const columns = [
    { key: "first_name", label: "First Name", sortable: true },
    { key: "last_name", label: "Last Name", sortable: true },
    { key: "id_number", label: "ID Number", sortable: true },
    { key: "contact_phone", label: "Phone Number", sortable: true },
    {
      key: "actions",
      label: "Actions",
      render: (value, client) => (
        <div className="flex gap-2">
          <Button size="small" variant="outline" onClick={() => handleView(client)}>
            View
          </Button>
          <Button size="small" variant="primary" onClick={() => handleEdit(client)}>
            Edit
          </Button>
          <Button size="small" variant="danger" onClick={() => handleDelete(client)}>
            Delete
          </Button>
          <Button size="small" variant="secondary" onClick={() => handleVehicle(client)}>
            Vehicles
          </Button>
        </div>
      ),
    },
  ];


  return (
    <div className="min-h-screen bg-gray-50 flex flex-col ">
      {/* <div className="flex-1 flex flex-col items-center justify-center px-2 w-full"> */}
        <div className="w-full mx-auto">
          <PageHeader
            title="Clients"
            subtitle="Manage and view all registered clients in the system."
            breadcrumbs={[
              { label: "Dashboard", href: "/broker/dashboard" },
              { label: "Clients" },
            ]}
            actions={[
              {
                label: (
                  <span className="flex items-center gap-2">
                    Add New Client
                  </span>
                ),
                variant: "primary",
                onClick: () => navigate("/broker/clients/create"),
              },
            ]}
          />
          <div className="mt-8 w-full">
            <Card
              title="DataTable Component"
              subtitle="Advanced table with sorting, searching, and pagination"
              className="w-full max-auto"
            >
              {alert.show && (
                <div className="mb-4 w-full max-w-2xl mx-auto">
                  <Alert
                    type={alert.type}
                    title={alert.title}
                    message={alert.message}
                    onClose={() => setAlert({ ...alert, show: false })}
                  />
                </div>
              )}
              <div className="relative" style={{ zIndex: 10 }}>
                <div className="overflow-y-auto" style={{ maxHeight: 500 }}>
                  <DataTable
                    data={clients}
                    columns={columns}
                    searchable={true}
                    pagination={true}
                    itemsPerPage={5}
                  />
                  <Modal
                    isOpen={showDeleteModal}
                    onClose={() => setShowDeleteModal(false)}
                    title="Delete Client"
                    size="small"
                  >
                    <div className="space-y-4">
                      <p>Are you sure you want to delete <strong>Client {clientToDelete?.first_name} {clientToDelete?.last_name}</strong>?</p>
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setShowDeleteModal(false)}>Cancel</Button>
                        <Button variant="danger" onClick={() => {/* TODO: implement delete logic */ setShowDeleteModal(false);}}>Delete</Button>
                      </div>
                    </div>
                  </Modal>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    // </div>
  );
}
