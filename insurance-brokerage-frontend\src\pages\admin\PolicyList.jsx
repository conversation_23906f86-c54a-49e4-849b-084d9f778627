import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Header, <PERSON>ton, DataTable, SearchBar, Modal, Alert } from "../../components";
import { useNavigate } from "react-router-dom";
import { policyService, coverTypeService, insuranceService } from "../../services";

export default function PolicyList() {
  const [policies, setPolicies] = useState([]);
  const [filteredPolicies, setFilteredPolicies] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedInsurer, setSelectedInsurer] = useState("all");
  const [selectedCoverType, setSelectedCoverType] = useState("all");
  const [insurers, setInsurers] = useState([]);
  const [coverTypes, setCoverTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [policyToDelete, setPolicyToDelete] = useState(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [alertMessage, setAlertMessage] = useState("");
  const navigate = useNavigate();

  const showSuccessAlert = (message) => {
    setAlertMessage(message);
    setAlertType("success");
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  // Fetch all data on mount
  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        const [policiesData, coverTypesData, insurersData] = await Promise.all([
          policyService.getPolicies(),
          coverTypeService.getCoverTypes(),
          insuranceService.getCompanies(),
        ]);
        // Join for display
        const joined = policiesData.map((policy) => {
          const coverType = coverTypesData.find((ct) => String(ct.id) === String(policy.coverTypeId));
          const insurer = insurersData.find((ins) => String(ins.id) === String(policy.insurerId));
          return {
            ...policy,
            coverTypeName: coverType ? coverType.name : "Unknown",
            insurerName: insurer ? insurer.name : "Unknown",
          };
        });
        setPolicies(joined);
        setFilteredPolicies(joined);
        setCoverTypes(coverTypesData);
        setInsurers(insurersData);
      } catch (error) {
        // Optionally handle error
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  // Filter and search
  useEffect(() => {
    let filtered = policies;
    if (selectedInsurer !== "all") {
      filtered = filtered.filter((p) => String(p.insurerId) === String(selectedInsurer));
    }
    if (selectedCoverType !== "all") {
      filtered = filtered.filter((p) => String(p.coverTypeId) === String(selectedCoverType));
    }
    if (searchTerm) {
      filtered = filtered.filter(
        (p) =>
          p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          p.coverTypeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          p.insurerName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    setFilteredPolicies(filtered);
  }, [searchTerm, policies, selectedInsurer, selectedCoverType]);

  const columns = [
    {
      key: "name",
      label: "Policy Name",
      sortable: true,
      render: (value) => <span className="font-semibold text-gray-800">{value}</span>,
    },
    {
      key: "coverTypeName",
      label: "Cover Type",
      sortable: true,
      render: (value) => <span className="text-blue-700 font-medium">{value}</span>,
    },
    {
      key: "insurerName",
      label: "Insurer",
      sortable: true,
      render: (value) => <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">{value}</span>,
    },
    {
      key: "basePremium",
      label: "Base Premium",
      sortable: true,
      render: (value) => <span className="text-green-700 font-semibold">{value ? value.toLocaleString() : "-"}</span>,
    },
    {
      key: "sumInsured",
      label: "Sum Insured",
      sortable: true,
      render: (value) => <span className="text-blue-700 font-semibold">{value ? value.toLocaleString() : "-"}</span>,
    },
    {
      key: "commissionRate",
      label: "Commission Rate (%)",
      sortable: true,
      render: (value) => <span className="text-purple-700 font-semibold">{value ? value + "%" : "-"}</span>,
    },
    {
      key: "vehicleTypes",
      label: "Vehicle Types",
      sortable: false,
      render: (value) => Array.isArray(value) ? value.join(", ") : "-",
    },
    {
      key: "status",
      label: "Status",
      sortable: true,
      render: (value) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${value === "Active" ? "bg-green-100 text-green-800" : "bg-gray-200 text-gray-600"}`}>{value}</span>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      render: (_, item) => (
        <div className="flex gap-2">
          <Button size="small" variant="outline" onClick={() => navigate(`/admin/policies/${item.id}`)}>View</Button>
          <Button size="small" variant="primary" onClick={() => navigate(`/admin/policies/${item.id}/edit`)}>Edit</Button>
          <Button size="small" variant="danger" onClick={() => { setPolicyToDelete(item); setShowDeleteModal(true); }}>Delete</Button>
        </div>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full mx-auto p-6 space-y-6">
        <PageHeader
          title="Policies"
          subtitle="Manage all insurance policies offered by insurers"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Policies" },
          ]}
          actions={[
            {
              label: "Add Policy",
              variant: "primary",
              onClick: () => navigate("/admin/policies/create"),
              icon: "+",
            },
          ]}
        />
        {showAlert && (
          <Alert
            type={alertType}
            title={alertType === "success" ? "Success" : "Error"}
            message={alertMessage}
            onClose={() => setShowAlert(false)}
          />
        )}
        <Card title="Policies" subtitle={`${filteredPolicies.length} found`}>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Insurer</label>
              <select
                className="w-56 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-700"
                value={selectedInsurer}
                onChange={(e) => setSelectedInsurer(e.target.value)}
              >
                <option value="all">All Insurers</option>
                {insurers.map((insurer) => (
                  <option key={insurer.id} value={insurer.id}>{insurer.name}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Cover Type</label>
              <select
                className="w-56 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-700"
                value={selectedCoverType}
                onChange={(e) => setSelectedCoverType(e.target.value)}
              >
                <option value="all">All Cover Types</option>
                {coverTypes.filter(ct => ct.category === "Motor").map((ct) => (
                  <option key={ct.id} value={ct.id}>{ct.name}</option>
                ))}
              </select>
            </div>
            <div className="flex-1 mt-5">
              <SearchBar
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
                placeholder="Search by Policy, Cover Type, or Insurer..."
              />
            </div>
          </div>
          {loading ? (
            <div className="text-center py-10 text-gray-500">Loading policies...</div>
          ) : (
            <DataTable
              data={filteredPolicies}
              columns={columns}
              searchable={false}
              pagination={true}
              itemsPerPage={10}
            />
          )}
        </Card>
        {/* Delete Confirmation Modal */}
        <Modal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          title="Delete Policy"
          size="small"
        >
          <div className="space-y-4">
            <p>Are you sure you want to delete the <strong>Policy {policyToDelete?.name}</strong>?</p>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowDeleteModal(false)}>Cancel</Button>
              <Button variant="danger" onClick={() => { setShowDeleteModal(false); showSuccessAlert(`Policy ${policyToDelete?.name} deleted successfully!`); }}>Delete</Button>
            </div>
          </div>
        </Modal>
      </div>
    </div>
  );
}
