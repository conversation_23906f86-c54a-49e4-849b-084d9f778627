<?php

// database/factories/InsuranceCategoryProductFactory.php

namespace Database\Factories;

use App\Models\InsuranceCategoryProduct;
use Illuminate\Database\Eloquent\Factories\Factory;

class InsuranceCategoryProductFactory extends Factory
{
    protected $model = InsuranceCategoryProduct::class;

    public function definition()
    {
        return [
            'insurance_category_product_id' => fake()->uuid(),
            'insurance_id' => \App\Models\Insurance::factory(),
            'category_id' => \App\Models\Category::factory(),
            'category_product_id' => \App\Models\CategoryProduct::factory(),
            'is_active' => true,
            'price' => random_int(5000, 10000),
            'vehicle_usage' => 'Commercial',
        ];
    }
}
