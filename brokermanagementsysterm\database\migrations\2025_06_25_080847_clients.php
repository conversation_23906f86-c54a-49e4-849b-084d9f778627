<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('IBMS_CLIENTS', function (Blueprint $table) {
            $table->uuid('client_id')->primary();

            $table->string('first_name')->nullable(false);
            $table->string('last_name')->nullable(false);
            $table->string('contact_email')->nullable(false)->unique();
            $table->string('contact_phone')->nullable(false)->unique();
            $table->integer('id_number')->nullable(false)->unique();
            $table->string('broker_id')->nullable(false);
            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IBMS_CLIENTS');
    }
};
