import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import Card from "../../components/Card";
import PageHeader from "../../components/PageHeader";
import Button from "../../components/Button";
import Alert from "../../components/Alert";
import { mockCoverTypes } from "../../services/mockData";
import { recommendationService } from "../../services/recommendationService";
import { insuranceService } from "../../services/insuranceService";

export default function VehiclePreferencesForm() {
  const location = useLocation();
  const navigate = useNavigate();
  const vehicle = location.state?.vehicle;
  const client = location.state?.client;

  const [preferredCoverTypes, setPreferredCoverTypes] = useState([]);
  const [budgetMin, setBudgetMin] = useState(10000);
  const [budgetMax, setBudgetMax] = useState(20000);
  const [preferredInsurers, setPreferredInsurers] = useState([]);
  const [alert, setAlert] = useState({ show: false, type: "info", title: "", message: "" });
  const [preferredChannel, setPreferredChannel] = useState("email");
  const [renewalReminderEnabled, setRenewalReminderEnabled] = useState(true);
  const [insuranceCompanies, setInsuranceCompanies] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchInsuranceCompanies = async () => {
      try {
        setLoading(true);
        const companies = await insuranceService.getCompanies();
        setInsuranceCompanies(companies);
        console.log(companies);
      } catch (error) {
        setAlert({ 
          show: true, 
          type: "error", 
          title: "Error", 
          message: "Failed to load insurance companies. Please try again." 
        });
      } finally {
        setLoading(false);
      }
    };

    fetchInsuranceCompanies();
  }, []);

  if (!vehicle || !client) {
    return <Alert type="error" title="Missing Data" message="Vehicle or client information is missing." />;
  }

  const handleSubmit = async (e) => {
    e.preventDefault();
    const prefs = {
      vehicle_id: vehicle.vehicle_id,
      preferred_cover_types: preferredCoverTypes,
      budget_min: budgetMin,
      budget_max: budgetMax,
      preferred_insurers: preferredInsurers,
      preferred_channel: preferredChannel,
      renewal_reminder_enabled: renewalReminderEnabled,
    };
    try {
      await recommendationService.createPreferences(client.client_id, prefs);
      setAlert({ show: true, type: "success", title: "Preferences Saved", message: "Preferences have been saved for this vehicle." });
      setTimeout(() => navigate("/broker/vehicles/recommendations", { state: { vehicle, client } }), 1200);
    } catch (error) {
      setAlert({ show: true, type: "error", title: "Error", message: error?.response?.data?.message || "Failed to save preferences." });
    }
  };

  return (
    <div className="w-full mx-auto mt-8">
      <PageHeader
        title="Set Client Preferences"
        subtitle={`Enter preferences for ${client.first_name} ${client.last_name}'s vehicle: ${vehicle.make} ${vehicle.model}`}
        breadcrumbs={[
          { label: "Broker Dashboard", href: "/broker/dashboard" },
          { label: "Clients", href: "/broker/clients" },
          { label: `Preferences for ${vehicle.make} ${vehicle.model}` },
        ]}
        actions={[
            {
              label: "Go Back to Vehicle",
              variant: "primary",
              onClick: () => navigate(`/broker/vehicles/${vehicle.vehicle_id}`, { state: { client, vehicle } }),
            },
          ]}
      />
      <form onSubmit={handleSubmit}>
        <Card title="Client Preferences" subtitle="These will be used to recommend the best policies for this vehicle.">
          <div className="flex flex-col gap-6">
            <div>
              <label className="block font-medium mb-1">Preferred Cover Types</label>
              <div className="flex flex-wrap gap-2">
                {mockCoverTypes.map((cover) => (
                  <label key={cover.id} className="flex items-center gap-1">
                    <input
                      type="checkbox"
                      value={cover.name}
                      checked={preferredCoverTypes.includes(cover.name)}
                      onChange={e => {
                        if (e.target.checked) setPreferredCoverTypes([...preferredCoverTypes, cover.name]);
                        else setPreferredCoverTypes(preferredCoverTypes.filter(c => c !== cover.name));
                      }}
                    />
                    {cover.name}
                  </label>
                ))}
              </div>
            </div>
            <div className="flex gap-4">
              <div className="flex-1">
                <label className="block font-medium mb-1">Budget Min (KES)</label>
                <input
                  type="number"
                  className="w-full border rounded px-2 py-1"
                  value={budgetMin}
                  min={0}
                  onChange={e => setBudgetMin(Number(e.target.value))}
                  required
                />
              </div>
              <div className="flex-1">
                <label className="block font-medium mb-1">Budget Max (KES)</label>
                <input
                  type="number"
                  className="w-full border rounded px-2 py-1"
                  value={budgetMax}
                  min={budgetMin}
                  onChange={e => setBudgetMax(Number(e.target.value))}
                  required
                />
              </div>
            </div>
            <div>
              <label className="block font-medium mb-1">Preferred Insurers</label>
              <div className="flex flex-wrap gap-2">
                {loading ? (
                  <p>Loading insurers...</p>
                ) : insuranceCompanies.length === 0 ? (
                  <p>No insurers found. Please try again later.</p>
                ) : (
                  insuranceCompanies.map((ins) => (
                    <label key={ins.insurance_id} className="flex items-center gap-1">
                      <input
                        type="checkbox"
                        value={ins.insurance_id}
                        checked={preferredInsurers.includes(ins.insurance_id)}
                        onChange={e => {
                          const id = ins.insurance_id;
                          if (e.target.checked) setPreferredInsurers([...preferredInsurers, id]);
                          else setPreferredInsurers(preferredInsurers.filter(i => i !== id));
                        }}
                      />
                      {ins.name}
                    </label>
                  ))
                )}
              </div>
            </div>
            <div>
              <label className="block font-medium mb-1">Preferred Channel</label>
              <select
                className="w-full border rounded px-2 py-1"
                value={preferredChannel}
                onChange={e => setPreferredChannel(e.target.value)}
              >
                <option value="email">Email</option>
                <option value="sms">SMS</option>
                <option value="phone">Phone Call</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="renewalReminder"
                checked={renewalReminderEnabled}
                onChange={e => setRenewalReminderEnabled(e.target.checked)}
              />
              <label htmlFor="renewalReminder" className="font-medium">Enable Renewal Reminders</label>
            </div>
            <div className="flex justify-end">
              <Button type="submit" variant="primary">Save Preferences and See Recommendations</Button>
            </div>
          </div>
        </Card>
        {alert.show && (
          <div className="mt-4">
            <Alert type={alert.type} title={alert.title} message={alert.message} onClose={() => setAlert({ ...alert, show: false })} />
          </div>
        )}
      </form>
    </div>
  );
} 