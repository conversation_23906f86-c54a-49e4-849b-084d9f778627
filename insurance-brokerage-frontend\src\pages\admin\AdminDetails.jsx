import React, {useEffect, useState} from "react";
import { useParams, useNavigate } from "react-router-dom";
import PageHeader from "../../components/PageHeader";
import Card from "../../components/Card";
import {userService} from "../../services/index.js";

const mockAdmins = [
  {
    id: 1,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    id_number: "A001",
    phone_number: "0700000001",
    email: "<EMAIL>",
    role: "admin",
  },
  {
    id: 2,
    first_name: "<PERSON>",
    last_name: "<PERSON><PERSON><PERSON>",
    id_number: "A002",
    phone_number: "0700000002",
    email: "<EMAIL>",
    role: "admin",
  },
  {
    id: 3,
    first_name: "<PERSON>",
    last_name: "<PERSON><PERSON>",
    id_number: "A003",
    phone_number: "0700000003",
    email: "jim.hal<PERSON>@dundermifflin.com",
    role: "admin",
  },
];

const AdminDetailsPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  //const admin = mockAdmins.find((a) => String(a.id) === String(id));
  const [loading, setLoading] = useState(false);
  const [admin, setAdmin ] = useState({});
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const user = await userService.getUser(id); // 'me'
        setAdmin(user);
        console.log('user data', user); // Logs 'me'
      } catch (error) {
        console.error('Failed to fetch user', error);
      }
    };

    fetchUser();
  }, []);

  useEffect(() => {
    // if (userRes) {
    if (Object.keys(admin).length === 0 && admin.constructor === Object) {
      setLoading(true);
    }
    else {
      setLoading(false);

    }
  }, [admin]);
  if (loading) {
    return <div>Loading...</div>;
  }
  if (!admin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-gray-500 text-lg">Admin not found.</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center py-8 px-2">
      <div className="w-full max-w-4xl mx-auto">
        <PageHeader
          title="Admin Details"
          subtitle={`Viewing ${admin.first_name} ${admin.last_name}`}
          breadcrumbs={[
            { label: "User Registration", href: "/admin/user-registration" },
            { label: "Admin List", href: "/admin/admin-list" },
            { label: `View: ${admin.first_name} ${admin.last_name}` },
          ]}
          className="w-full max-w-4xl"
          actions={[
            {
              label: "Back to Admin List",
              variant: "primary",
              onClick: () => navigate("/admin/admin-list"),
            },
          ]}
        />
        <Card title="Admin Information" className="mt-8">
          <div className="space-y-4">
            <div>
              <span className="font-semibold">First Name: </span>
              <span>{admin.first_name}</span>
            </div>
            <div>
              <span className="font-semibold">Last Name: </span>
              <span>{admin.last_name}</span>
            </div>
            <div>
              <span className="font-semibold">ID Number: </span>
              <span>{admin.id_number}</span>
            </div>
            <div>
              <span className="font-semibold">Phone Number: </span>
              <span>{admin.phone_number}</span>
            </div>
            <div>
              <span className="font-semibold">Email: </span>
              <span>{admin.email}</span>
            </div>
            <div>
              <span className="font-semibold">Role: </span>
              <span>
                  {admin?.role?.role_name}
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AdminDetailsPage;
