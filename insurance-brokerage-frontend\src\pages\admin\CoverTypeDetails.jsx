import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { <PERSON>, PageHeader, Button } from "../../components";
import { coverTypeService, insuranceService } from "../../services";

export default function CoverTypeDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [coverType, setCoverType] = useState(null);
  const [insurer, setInsurer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        const coverTypeData = await coverTypeService.getCoverType(id);
        setCoverType(coverTypeData);
        const insurerData = await insuranceService.getCompany(coverTypeData.insurerId);
        setInsurer(insurerData);
      } catch (err) {
        setError("The requested cover type does not exist.");
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen w-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading cover type details...</p>
        </div>
      </div>
    );
  }

  if (error || !coverType) {
    return (
      <div className="min-h-screen w-full flex items-center justify-center bg-gray-50">
        <Card title="Cover Type Not Found">
          <p className="text-gray-600">{error}</p>
          <div className="mt-4 flex justify-end">
            <Button variant="outline" onClick={() => navigate("/admin/cover-types")}>Back to List</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
        <PageHeader
          title={coverType.name}
          subtitle="Cover Type Details"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Cover Types", href: "/admin/cover-types" },
            { label: coverType.name },
          ]}
          actions={[
            {
              label: "Edit",
              variant: "primary",
              onClick: () => navigate(`/admin/cover-types/${coverType.id}/edit`),
            },
          ]}
        />
        <Card title="Cover Type Information">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Cover Type Name</label>
              <p className="text-sm text-gray-900">{coverType.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Description</label>
              <p className="text-sm text-gray-900">{coverType.description}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Insurer</label>
              <p className="text-sm text-gray-900">{insurer ? insurer.name : "Unknown"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Status</label>
              <p className="text-sm text-green-700 font-semibold">{coverType.status}</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
