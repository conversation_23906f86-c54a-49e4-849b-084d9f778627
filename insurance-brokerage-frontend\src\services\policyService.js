import axios from '../utils/axios';
import { mockPolicies } from './mockData';

// Policies API
export const policyService = {
  // Get all policies
  getPolicies: async () => {
    try {
      // For now, return mock data
      // const response = await axios.get('/api/policies');
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 400));
      return mockPolicies;
    } catch (error) {
      throw error;
    }
  },

  // Get single policy
  getPolicy: async (id) => {
    try {
      // For now, return mock data
      // const response = await axios.get(`/api/policies/${id}`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      const policy = mockPolicies.find(p => p.id === parseInt(id));
      if (!policy) {
        throw new Error('Policy not found');
      }
      return policy;
    } catch (error) {
      throw error;
    }
  },

  // Create new policy
  createPolicy: async (policyData) => {
    try {
      // For now, simulate creation
      // const response = await axios.post('/api/policies', policyData);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 600));
      
      const newPolicy = {
        ...policyData,
        id: Date.now(),
        status: "Active",
        createdAt: new Date().toISOString()
      };
      
      // In a real app, this would be added to the database
      mockPolicies.push(newPolicy);
      
      return newPolicy;
    } catch (error) {
      throw error;
    }
  },

  // Update policy
  updatePolicy: async (id, policyData) => {
    try {
      // For now, simulate update
      // const response = await axios.put(`/api/policies/${id}`, policyData);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = mockPolicies.findIndex(p => p.id === parseInt(id));
      if (index === -1) {
        throw new Error('Policy not found');
      }
      
      mockPolicies[index] = { ...mockPolicies[index], ...policyData };
      return mockPolicies[index];
    } catch (error) {
      throw error;
    }
  },

  // Delete policy
  deletePolicy: async (id) => {
    try {
      // For now, simulate deletion
      // const response = await axios.delete(`/api/policies/${id}`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const index = mockPolicies.findIndex(p => p.id === parseInt(id));
      if (index === -1) {
        throw new Error('Policy not found');
      }
      
      mockPolicies.splice(index, 1);
      return { success: true };
    } catch (error) {
      throw error;
    }
  }
}; 