<?php

declare(strict_types=1);

namespace App\Models;

class IBMSPermissions
{
    public const IBMS_ADD_BROKER = 1 << 0;

    public const IBMS_EDIT_BROKER = 1 << 1;

    public const IBMS_VIEW_BROKER = 1 << 2;

    public const IBMS_DISABLE_BROKER = 1 << 3;

    public const IBMS_ENABLE_BROKER = 1 << 4;

    public const IBMS_ADD_INSURANCE_COMPANY = 1 << 5;

    public const IBMS_EDIT_INSURANCE_COMPANY = 1 << 6;

    public const IBMS_DISABLE_INSURANCE_COMPANY = 1 << 7;

    public const IBMS_ENABLE_INSURANCE_COMPANY = 1 << 8;

    public const IBMS_VIEW_INSURANCE_COMPANY = 1 << 9;

    public const IBMS_CREATE_QUOTATION = 1 << 10;

    public const IBMS_EDIT_QUOTATION = 1 << 11;

    public const IBMS_DISABLE_QUOTATION = 1 << 12;

    public const IBMS_VIEW_QUOTATION = 1 << 13;

    public const IBMS_ADD_CLIENT = 1 << 14;

    public const IBMS_EDIT_CLIENT = 1 << 15;

    public const IBMS_VIEW_CLIENT = 1 << 16;

    public const IBMS_ADD_VEHICLE = 1 << 17;

    public const IBMS_EDIT_VEHICLE = 1 << 18;

    public const IBMS_VIEW_VEHICLE = 1 << 19;

    public const IBMS_DISABLE_VEHICLE = 1 << 20;
}
