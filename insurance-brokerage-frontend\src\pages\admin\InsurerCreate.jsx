import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card } from "../../components";
import FormField from "../../components/FormFields";
import { insuranceService } from "../../services";

const InsurerCreate = () => {
  const navigate = useNavigate();
  const [form, setForm] = useState({
    name: "",
    phone: "",
    email: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [validation, setValidation] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    setValidation((prev) => ({ ...prev, [name]: "" }));
  };

  const validate = () => {
    const errors = {};
    if (!form.name) errors.name = "Company name is required";
    if (!form.phone) errors.phone = "Contact number is required";
    if (!form.email) errors.email = "Contact email is required";
    if (form.email && !/\S+@\S+\.\S+/.test(form.email)) {
      errors.email = "Please enter a valid email address";
    }
    setValidation(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validate()) return;
    try {
      setLoading(true);
      setError("");
      const companyData = {
        ...form,
        status: "Active",
      };
      await insuranceService.createCompany(companyData);
      showSuccessAlert("Insurance company created successfully!");
      setTimeout(() => {
        navigate("/admin/insurers");
      }, 1500);
    } catch (err) {
      setError(err.response?.data?.message || "Failed to create insurance company");
    } finally {
      setLoading(false);
    }
  };

  const showSuccessAlert = (message) => {
    setAlertMessage(message);
    setAlertType("success");
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  const handleCancel = () => {
    navigate("/admin/insurers");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex-1 overflow-x-hidden p-4">
        <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
          <PageHeader
            title="Create Insurance Company"
            subtitle="Add a new insurance company to the system"
            breadcrumbs={[
              { label: "Dashboard", href: "/admin/dashboard" },
              { label: "Insurance Companies", href: "/admin/insurers" },
              { label: "Create Company" }
            ]}
            actions={[
              {
                label: "Back to Companies",
                variant: "outline",
                onClick: handleCancel,
              }
            ]}
          />
          {showAlert && (
            <Alert
              type={alertType}
              title={alertType === "success" ? "Success" : "Error"}
              message={alertMessage}
              onClose={() => setShowAlert(false)}
            />
          )}
          {error && (
            <Alert
              type="error"
              title="Error"
              message={error}
              onClose={() => setError("")}
            />
          )}
          <Card title="New Insurance Company">
            <form className="space-y-6" onSubmit={handleSubmit}>
              <FormField
                label="Company Name"
                name="name"
                value={form.name}
                onChange={handleChange}
                placeholder="Enter company name"
                isRequired={true}
                validationError={validation.name}
              />
              <FormField
                label="Contact Number"
                name="phone"
                type="tel"
                value={form.phone}
                onChange={handleChange}
                placeholder="+254700000000"
                isRequired={true}
                validationError={validation.phone}
              />
              <FormField
                label="Contact Email"
                name="email"
                type="email"
                value={form.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
                isRequired={true}
                validationError={validation.email}
              />
              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  className="px-4 py-2 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 font-medium"
                  onClick={handleCancel}
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 rounded bg-blue-700 text-white hover:bg-blue-800 font-medium"
                  disabled={loading}
                >
                  {loading ? "Creating..." : "Create Company"}
                </button>
              </div>
            </form>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default InsurerCreate;
