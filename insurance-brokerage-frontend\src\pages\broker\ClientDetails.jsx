import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import PageHeader from "../../components/PageHeader";
import Card from "../../components/Card";

export default function ClientDetails() {
  const location = useLocation();
  const navigate = useNavigate();
  // Try to get client from navigation state, fallback to empty
  const client = location.state?.client || {
    first_name: "",
    last_name: "",
    id_number: "",
    contact_phone: "",
    email: "",
  };

  return (
    <div className="w-full max-w-4xl bg-white rounded-2xl shadow-lg p-0 mx-auto relative mt-8">
      <PageHeader
        title={`View: ${client.first_name} ${client.last_name}`}
        subtitle="Client Details"
        breadcrumbs={[
          { label: "Dashboard", href: "/broker/dashboard" },
          { label: "Clients", href: "/broker/clients" },
          { label: `View: ${client.first_name} ${client.last_name}` },
        ]}
        className="rounded-t-2xl w-full"
        actions={[]}
      />
      <div className="p-8">
        <Card title="Client Information" className="shadow-none border-none p-0">
          <div className="grid grid-cols-1 gap-y-4 text-base">
            <div>
              <span className="font-medium text-gray-700">First Name:</span> {client.first_name}
            </div>
            <div>
              <span className="font-medium text-gray-700">Last Name:</span> {client.last_name}
            </div>
            <div>
              <span className="font-medium text-gray-700">ID Number:</span> {client.id_number}
            </div>
            <div>
              <span className="font-medium text-gray-700">Phone Number:</span> {client.contact_phone}
            </div>
            <div>
              <span className="font-medium text-gray-700">Email:</span> {client.contact_email || "N/A"}
            </div>
          </div>
          <div className="flex justify-end mt-8">
            <button
              className="bg-gray-200 text-gray-700 px-6 py-2 rounded-lg font-semibold"
              onClick={() => navigate(-1)}
            >
              Close
            </button>
          </div>
        </Card>
      </div>
    </div>
  );
}
