import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { PageHeader, Button, Alert, DataTable } from "../../components";
import { insuranceService, coverTypeService } from "../../services";
import { policyService } from "../../services/policyService";
import Modal from "../../components/Modal";

const InsurerDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [company, setCompany] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [policies, setPolicies] = useState([]);
  const [coverTypes, setCoverTypes] = useState([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [policyToDelete, setPolicyToDelete] = useState(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [alertMessage, setAlertMessage] = useState("");

  useEffect(() => {
    fetchCompanyData();
    fetchPoliciesAndCoverTypes();
  }, [id]);

  const fetchCompanyData = async () => {
    try {
      setLoading(true);
      setError("");
      const companyData = await insuranceService.getCompany(id);
      setCompany(companyData);
    } catch (err) {
      setError("Failed to fetch company information");
    } finally {
      setLoading(false);
    }
  };

  const fetchPoliciesAndCoverTypes = async () => {
    try {
      const [allPolicies, allCoverTypes] = await Promise.all([
        policyService.getPolicies(),
        coverTypeService.getCoverTypes()
      ]);
      setCoverTypes(allCoverTypes);
      const filtered = allPolicies
        .filter(p => p.insurerId === parseInt(id))
        .map(p => ({
          ...p,
          coverTypeName: (allCoverTypes.find(ct => String(ct.id) === String(p.coverTypeId)) || {}).name || "-"
        }));
      setPolicies(filtered);
    } catch (err) {
      // Optionally handle error
    }
  };

  const showSuccessAlert = (message) => {
    setAlertMessage(message);
    setAlertType("success");
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading company information...</p>
        </div>
      </div>
    );
  }

  if (error || !company) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <PageHeader
          title="Company Not Found"
          subtitle="The requested company could not be found"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Insurance Companies", href: "/admin/insurers" },
            { label: "Company Details" }
          ]}
          actions={[
            {
              label: "Back to Companies",
              variant: "outline",
              onClick: () => navigate("/admin/insurers"),
            }
          ]}
        />
        <div className="p-6">
          <Alert
            type="error"
            title="Error"
            message={error || "Company not found"}
            onClose={() => navigate("/admin/insurers")}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex-1 overflow-x-hidden p-4">
        <div className="w-full mx-auto p-6 space-y-6">
          <PageHeader
            title={company.name}
            subtitle="Insurance Company Details"
            breadcrumbs={[
              { label: "Dashboard", href: "/admin/dashboard" },
              { label: "Insurance Companies", href: "/admin/insurers" },
              { label: company.name }
            ]}
            actions={[
              {
                label: "Edit Company",
                variant: "primary",
                onClick: () => navigate(`/admin/insurers/${id}/edit`),
              }
            ]}
          />
          <div className="space-y-4 bg-white rounded-lg shadow p-6">
            <div>
              <label className="text-sm font-medium text-gray-500">Company Name</label>
              <p className="text-sm text-gray-900">{company.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Contact Number</label>
              <p className="text-sm text-gray-900">{company.phone}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Contact Email</label>
              <p className="text-sm text-gray-900">
                <a href={`mailto:${company.email}`} className="text-blue-600 hover:text-blue-800">
                  {company.email}
                </a>
              </p>
            </div>
          </div>
          {error && (
            <Alert
              type="error"
              title="Error"
              message={error}
              onClose={() => setError("")}
            />
          )}
          {showAlert && (
            <Alert
              type={alertType}
              title={alertType === "success" ? "Success" : "Error"}
              message={alertMessage}
              onClose={() => setShowAlert(false)}
            />
          )}
          <div className="space-y-2 bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-lg font-semibold">Policies for {company.name}</h2>
              <Button
                variant="primary"
                onClick={() => navigate(`/admin/policies/create?insurer=${id}`)}
              >
                Add Policy
              </Button>
            </div>
            <DataTable
              data={policies}
              columns={[
                { key: "name", label: "Policy Name", sortable: true },
                { key: "coverTypeName", label: "Cover Type", sortable: true },
                { key: "basePremium", label: "Base Premium", sortable: true, render: (value) => value ? value.toLocaleString() : "-" },
                { key: "sumInsured", label: "Sum Insured", sortable: true, render: (value) => value ? value.toLocaleString() : "-" },
                { key: "commissionRate", label: "Commission Rate (%)", sortable: true, render: (value) => value ? value + "%" : "-" },
                { key: "vehicleTypes", label: "Vehicle Types", sortable: false, render: (value) => Array.isArray(value) ? value.join(", ") : "-" },
                { key: "status", label: "Status", type: "status", sortable: true },
                {
                  key: "actions",
                  label: "Actions",
                  sortable: false,
                  render: (value, row) => (
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => navigate(`/admin/policies/${row.id}`)}
                      >
                        View
                      </Button>
                      <Button
                        variant="primary"
                        onClick={() => navigate(`/admin/policies/${row.id}/edit`)}
                      >
                        Edit
                      </Button>
                      <Button
                        variant="danger"
                        onClick={() => { setPolicyToDelete(row); setShowDeleteModal(true); }}
                      >
                        Delete
                      </Button>
                    </div>
                  )
                }
              ]}
              searchable={true}
              pagination={true}
              itemsPerPage={10}
            />
          </div>
          <Modal
            isOpen={showDeleteModal}
            onClose={() => setShowDeleteModal(false)}
            title="Delete Policy"
            size="small"
          >
            <div className="space-y-4">
              <p>Are you sure you want to delete the <strong>Policy {policyToDelete?.name}</strong>?</p>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowDeleteModal(false)}>Cancel</Button>
                <Button
                  variant="danger"
                  onClick={() => {
                    // TODO: implement delete logic
                    setShowDeleteModal(false);
                    showSuccessAlert(`Policy ${policyToDelete?.name} deleted successfully!`);
                  }}
                >
                  Delete
                </Button>
              </div>
            </div>
          </Modal>
        </div>
      </div>
    </div>
  );
};

export default InsurerDetails;
