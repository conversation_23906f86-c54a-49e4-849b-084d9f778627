import axios from '../utils/axios';
import { mockQuotes } from './mockData';

// Quotes API
export const quoteService = {
  // Get all quotes
  getQuotes: async () => {
    try {
      // For now, return mock data
      // const response = await axios.get('/api/quotes');
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 400));
      return mockQuotes;
    } catch (error) {
      throw error;
    }
  },

  // Get single quote
  getQuote: async (id) => {
    try {
      // For now, return mock data
      // const response = await axios.get(`/api/quotes/${id}`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      const quote = mockQuotes.find(q => q.id === parseInt(id));
      if (!quote) {
        throw new Error('Quote not found');
      }
      return quote;
    } catch (error) {
      throw error;
    }
  },

  // Create new quote
  createQuote: async (quoteData) => {
    try {
      // For now, simulate creation
      // const response = await axios.post('/api/quotes', quoteData);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 600));
      
      const newQuote = {
        ...quoteData,
        id: Date.now(),
        status: "Draft",
        createdAt: new Date().toISOString()
      };
      
      // In a real app, this would be added to the database
      mockQuotes.push(newQuote);
      
      return newQuote;
    } catch (error) {
      throw error;
    }
  },

  // Update quote
  updateQuote: async (id, quoteData) => {
    try {
      // For now, simulate update
      // const response = await axios.put(`/api/quotes/${id}`, quoteData);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = mockQuotes.findIndex(q => q.id === parseInt(id));
      if (index === -1) {
        throw new Error('Quote not found');
      }
      
      mockQuotes[index] = { ...mockQuotes[index], ...quoteData };
      return mockQuotes[index];
    } catch (error) {
      throw error;
    }
  },

  // Delete quote
  deleteQuote: async (id) => {
    try {
      // For now, simulate deletion
      // const response = await axios.delete(`/api/quotes/${id}`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const index = mockQuotes.findIndex(q => q.id === parseInt(id));
      if (index === -1) {
        throw new Error('Quote not found');
      }
      
      mockQuotes.splice(index, 1);
      return { success: true };
    } catch (error) {
      throw error;
    }
  },

  // Get quotes by broker
  getQuotesByBroker: async (brokerId) => {
    try {
      // For now, return mock data
      // const response = await axios.get(`/api/brokers/${brokerId}/quotes`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      return mockQuotes.filter(q => q.brokerId === parseInt(brokerId));
    } catch (error) {
      throw error;
    }
  },

  // Get quotes by client
  getQuotesByClient: async (clientId) => {
    try {
      // For now, return mock data
      // const response = await axios.get(`/api/clients/${clientId}/quotes`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      return mockQuotes.filter(q => q.clientId === parseInt(clientId));
    } catch (error) {
      throw error;
    }
  },

  // Submit quote for approval
  submitQuote: async (id) => {
    try {
      // For now, simulate submission
      // const response = await axios.post(`/api/quotes/${id}/submit`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = mockQuotes.findIndex(q => q.id === parseInt(id));
      if (index === -1) {
        throw new Error('Quote not found');
      }
      
      mockQuotes[index].status = "Submitted";
      mockQuotes[index].submittedAt = new Date().toISOString();
      
      return mockQuotes[index];
    } catch (error) {
      throw error;
    }
  },

  // Approve quote
  approveQuote: async (id) => {
    try {
      // For now, simulate approval
      // const response = await axios.post(`/api/quotes/${id}/approve`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = mockQuotes.findIndex(q => q.id === parseInt(id));
      if (index === -1) {
        throw new Error('Quote not found');
      }
      
      mockQuotes[index].status = "Approved";
      mockQuotes[index].approvedAt = new Date().toISOString();
      
      return mockQuotes[index];
    } catch (error) {
      throw error;
    }
  },

  // Reject quote
  rejectQuote: async (id, reason) => {
    try {
      // For now, simulate rejection
      // const response = await axios.post(`/api/quotes/${id}/reject`, { reason });
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = mockQuotes.findIndex(q => q.id === parseInt(id));
      if (index === -1) {
        throw new Error('Quote not found');
      }
      
      mockQuotes[index].status = "Rejected";
      mockQuotes[index].rejectedAt = new Date().toISOString();
      mockQuotes[index].rejectionReason = reason;
      
      return mockQuotes[index];
    } catch (error) {
      throw error;
    }
  }
}; 