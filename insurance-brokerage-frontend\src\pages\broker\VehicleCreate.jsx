import React, { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON>, Card, FormWizard, Alert } from "../../components";
import { vehicleService } from "../../services/vehicleService";

export default function VehicleCreate() {
  const navigate = useNavigate();
  const location = useLocation();
  const client = location.state?.client || null;
  const [alert, setAlert] = useState({ show: false, type: "info", title: "", message: "" });

  // FormWizard steps
  const wizardSteps = [
    {
      title: "Vehicle Details",
      description: "Enter the vehicle's information",
      fields: [
        {
          name: "client_id",
          label: "Client ID",
          type: "text",
          placeholder: "",
          required: true,
          value: client && client.client_id ? client.client_id : "",
          readOnly: true,
        },
        {
          name: "make",
          label: "Make",
          type: "text",
          placeholder: "e.g. Toyota",
          required: true,
        },
        {
          name: "model",
          label: "Model",
          type: "text",
          placeholder: "e.g. Corolla",
          required: true,
        },
        {
          name: "registration_number",
          label: "Registration Number",
          type: "text",
          placeholder: "e.g. KDA 123A",
          required: true,
        },
        {
          name: "vehicle_purpose",
          label: "Vehicle Purpose",
          type: "select",
          options: [
            { value: "Personal", label: "Personal" },
            { value: "Commercial", label: "Commercial" },
          ],
          required: true,
        },
        {
          name: "mileage",
          label: "Mileage",
          type: "number",
          placeholder: "e.g. 50000",
          required: true,
        },
        {
          name: "value",
          label: "Value",
          type: "number",
          placeholder: "e.g. 1200000",
          required: true,
        },
        {
          name: "cover_type",
          label: "Desired Cover Type",
          type: "select",
          options: [
            { value: "Comprehensive", label: "Comprehensive" },
            { value: "Third Party", label: "Third Party" },
          ],
        },
      ],
      validation: (data, errors) => {
        if (!data.make) errors.make = "Make is required";
        if (!data.model) errors.model = "Model is required";
        if (!data.registration_number) errors.registration_number = "Registration number is required";
        if (!data.vehicle_purpose) errors.vehicle_purpose = "Vehicle purpose is required";
        if (!data.value || isNaN(data.value)) errors.value = "Value is required and must be a number";
      },
    },
  ];

  // Handle form completion
  const handleWizardComplete = async (formData) => {
    await vehicleService.createVehicle(formData)
      .then(() => {
        setAlert({
          show: true,
          type: "success",
          title: "Vehicle Onboarded",
          message: `Vehicle ${formData.make} ${formData.model} onboarded successfully!`,
        });

      })
      .catch((error) => {
        console.error("Failed to onboard vehicle", error);
        setAlert({
          show: true,
          type: "error",
          title: "Error",
          message: "Failed to onboard vehicle",
        });
      });
    // Optionally, send data to backend here
    setTimeout(() => navigate("/broker/vehicles/details", { state: { client } }), 1500);
  };

  // Handle cancel
  const handleCancel = () => {
    setAlert({
      show: true,
      type: "info",
      title: "Cancelled",
      message: "Vehicle onboarding cancelled.",
    });
    setTimeout(() => navigate("/broker/vehicles/details"), 1000);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center py-6 px-2 sm:py-10 sm:px-4 w-full">
      <div className="w-full max-auto">
        <PageHeader
          title="Onboard Vehicle"
          subtitle="Add a new vehicle for to the system"
          breadcrumbs={[
            { label: "Clients", href: "/broker/clients" },
            client && { label: `Onboard Vehicle: ${client.first_name} ${client.last_name}` },
          ].filter(Boolean)}
          actions={[
            client && {
              label: "View Vehicles",
              variant: "primary",
              onClick: () => navigate(`/broker/vehicles/${vehicle.vehicle_id}`, { state: { client, vehicle } }),
            },
          ].filter(Boolean)}
        />
        <div className="mt-8">
          <Card title="FormWizard Component" subtitle="Step-by-step form wizard">
            <FormWizard
              steps={wizardSteps}
              onComplete={handleWizardComplete}
              onCancel={handleCancel}
              initialData={client ? { ...client } : {}}
            />
          </Card>
        </div>
        {alert.show && (
          <div className="fixed top-5 right-2 sm:right-5 z-[2000] min-w-[250px] max-w-xs w-auto">
            <Alert
              type={alert.type}
              title={alert.title}
              message={alert.message}
              onClose={() => setAlert({ ...alert, show: false })}
            />
          </div>
        )}
      </div>
    </div>
  );
}
