import React, { useState, useEffect } from "react"; // Ensure useEffect is imported if not already
import Alert from "../../components/Alert";
import Button from "../../components/Button"; // Assuming this is your custom Button component
import PageHeader from "../../components/PageHeader";
import Card from "../../components/Card";
import DataTable from "../../components/DataTable"; // Assuming this is your custom DataTable component
import { useNavigate } from "react-router-dom";
import BrokerEdit from "./BrokerEdit";
import {userService} from "../../services/index.js";
import Modal from "../../components/Modal";

/*const mockBrokers = [
  {
    id: 1,
    first_name: "<PERSON> oooo",
    last_name: "<PERSON><PERSON>",
    idNumber: "123456",
    phone: "0712345678",
    email: "<EMAIL>",
    role: "broker",
  },
  {
    id: 2,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    idNumber: "654321",
    phone: "0723456789",
    email: "<EMAIL>",
    role: "broker",
  },
  {
    id: 3,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    idNumber: "789012",
    phone: "0734567890",
    email: "<EMAIL>",
    role: "broker",
  },
];*/

const BrokerList = () => {
  const [brokers, setBrokers] = useState([]);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const brokers = await userService.getUsers('brokers'); // 'me'
        setBrokers(brokers);
        console.log('user data', brokers); // Logs 'me'
      } catch (error) {
        console.error('Failed to fetch user', error);
      }
    };

    fetchUser();
  }, []);

  useEffect(() => {
    // if (userRes) {
    if (Object.keys(brokers).length === 0 && brokers.constructor === Object) {
      setLoading(true);
    }
    else {
      setLoading(false);

    }
  }, [brokers]);


  const [alert, setAlert] = useState({ show: false, type: "info", title: "", message: "" });
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [brokerToDelete, setBrokerToDelete] = useState(null);
  const [actionOpen, setActionOpen] = useState(null); // State to track which dropdown is open
  const navigate = useNavigate();

  // Show alert if coming from navigation state (e.g., after edit or view)
  useEffect(() => {
    if (window.history.state && window.history.state.usr && window.history.state.usr.alert) {
      setAlert(window.history.state.usr.alert);
      // Remove alert from history so it doesn't show again on refresh
      window.history.replaceState({ ...window.history.state, usr: { ...window.history.state.usr, alert: undefined } }, "");
    }
  }, []);

  const handleEdit = (id) => {
    navigate(`/admin/brokers/${id}/edit`);
    setActionOpen(null); // Close dropdown after action
  };

  const handleDelete =async (id) => {
    const user = await userService.deleteUser(id);
    setAlert({ show: true, type: "success", title: "Deleted", message: "Broker deleted successfully!" });
    setActionOpen(null); // Close dropdown after action
  };

  const handleView = (id) => {
    navigate(`/admin/brokers/${id}`, {
      state: {
        alert: {
          show: true,
          type: "info",
          title: "Viewing Broker",
          message: `You are viewing broker details.`,
        },
      },
    });
  };

  // Close dropdown on click outside
  useEffect(() => {
    if (actionOpen === null) return;
    const close = (e) => {
      // Check if the click is outside the dropdown container
      if (!e.target.closest(`#dropdown-actions-${actionOpen}`)) {
        setActionOpen(null);
      }
    };
    window.addEventListener("mousedown", close, true);
    return () => window.removeEventListener("mousedown", close, true);
  }, [actionOpen]); // Re-run effect when actionOpen changes

  const columns = [
    { key: "first_name", label: "First Name", sortable: true },
    { key: "last_name", label: "Last Name", sortable: true },
    { key: "id_number", label: "ID Number", sortable: true },
    // Remove the custom actions column for now
  ];

  const handleTableAction = (action, item) => {
    if (action === "view") handleView(item?.user_id);
    if (action === "edit") handleEdit(item?.user_id);
    if (action === "delete") { setBrokerToDelete(item); setShowDeleteModal(true); }
  };
  if (loading) {
    return <div>Loading...</div>;
  }
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col sm:flex-row">
      <div className="flex-1 flex flex-col items-center py-8 px-2">
        <div className="w-full max-auto mx-auto flex flex-col items-center">
          <PageHeader
            title="Broker List"
            subtitle="View and manage all brokers"
            breadcrumbs={[
              { label: "User Registration", href: "/admin/user-registration" },
              { label: "Broker List" },
            ]}
            className="w-full"
            actions={[
              {
                label: "Back to User Registration",
                variant: "primary",
                onClick: () => navigate("/admin/user-registration"),
              },
            ]}
          />
          <div className="h-8" />
          {alert.show && (
            <div className="mb-4 w-full max-w-2xl mx-auto">
              <Alert
                type={alert.type}
                title={alert.title}
                message={alert.message}
                onClose={() => setAlert({ ...alert, show: false })}
              />
            </div>
          )}
          <div className="w-full max-w-7xl flex justify-center">
            <Card
              title="DataTable Component"
              subtitle="Advanced table with sorting, searching, and pagination"
              className="w-full"
            >
              <DataTable
                data={brokers}
                columns={columns}
                onView={(item) => handleView(item?.user_id)}
                onEdit={(item) => handleTableAction("edit", item)}
                onDelete={(item) => handleTableAction("delete", item)}
                searchable={true}
                pagination={true}
                itemsPerPage={5}
              />
            </Card>
          </div>
          <Modal
            isOpen={showDeleteModal}
            onClose={() => setShowDeleteModal(false)}
            title="Delete Broker"
            size="small"
          >
            <div className="space-y-4">
              <p>Are you sure you want to delete <strong>Broker {brokerToDelete?.first_name} {brokerToDelete?.last_name}</strong>?</p>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowDeleteModal(false)}>Cancel</Button>
                <Button variant="danger" onClick={() => {/* TODO: implement delete logic */ setShowDeleteModal(false);}}>Delete</Button>
              </div>
            </div>
          </Modal>
        </div>
      </div>
      {/* Edit Modal */}
      {/* {editBroker && (
        <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-lg p-10 w-full max-w-lg relative">
            <button
              onClick={() => setEditBroker(null)}
              className="absolute top-3 right-4 text-2xl text-gray-500 hover:text-gray-700"
            >
              ×
            </button>
            <h2 className="text-2xl font-bold mb-6">Edit Broker</h2>
            <div className="space-y-4 mb-8">
              <div>
                <label className="block font-semibold mb-1">First Name</label>
                <input
                  type="text"
                  name="first_name"
                  value={editForm.first_name}
                  onChange={handleEditChange}
                  className="w-full border border-gray-300 rounded-md p-2"
                />
              </div>
              <div>
                <label className="block font-semibold mb-1">Last Name</label>
                <input
                  type="text"
                  name="last_name"
                  value={editForm.last_name}
                  onChange={handleEditChange}
                  className="w-full border border-gray-300 rounded-md p-2"
                />
              </div>
              <div>
                <label className="block font-semibold mb-1">ID Number</label>
                <input
                  type="text"
                  name="idNumber"
                  value={editForm.idNumber}
                  onChange={handleEditChange}
                  className="w-full border border-gray-300 rounded-md p-2"
                />
              </div>
              <div>
                <label className="block font-semibold mb-1">Phone Number</label>
                <input
                  type="text"
                  name="phone"
                  value={editForm.phone}
                  onChange={handleEditChange}
                  className="w-full border border-gray-300 rounded-md p-2"
                />
              </div>
              <div>
                <label className="block font-semibold mb-1">Email</label>
                <input
                  type="email"
                  name="email"
                  value={editForm.email}
                  onChange={handleEditChange}
                  className="w-full border border-gray-300 rounded-md p-2"
                />
              </div>
              <div>
                <label className="block font-semibold mb-1">Role</label>
                <select
                  name="role"
                  value={editForm.role}
                  onChange={handleEditChange}
                  className="w-full border border-gray-300 rounded-md p-2"
                >
                  <option value="broker">Broker</option>
                  <option value="admin">Admin</option>
                </select>
              </div>
            </div>
            <Button
              onClick={handleEditSave}
              variant="primary"
              size="md"
            >
              Save Changes
            </Button>
          </div>
        </div>
      )} */}
    </div>
  );
};

export default BrokerList;