<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use ReflectionClass;

/**
 * @OA\Schema(
 *   schema="IBMSRole",
 *   type="object",
 *   title="IBMS Role",
 *   description="Role definition for the IBMS system",
 *   required={"role_id", "role_name"},
 *
 *   @OA\Property(
 *     property="role_id",
 *     type="integer",
 *     format="int64",
 *     description="Unique identifier for the role"
 *   ),
 *   @OA\Property(
 *     property="role_name",
 *     type="string",
 *     description="Human-readable name of the role"
 *   ),
 *   @OA\Property(
 *     property="role_permissions",
 *     type="integer",
 *     default=0,
 *     description="Bitmask representing assigned permissions"
 *   ),
 *   @OA\Property(
 *     property="created_at",
 *     type="string",
 *     format="date-time",
 *     description="Timestamp of creation"
 *   ),
 *   @OA\Property(
 *     property="updated_at",
 *     type="string",
 *     format="date-time",
 *     description="Timestamp of last update"
 *   )
 * )
 */
class Role extends Model
{
    use HasFactory;

    protected $table = 'IBMS_ROLES';

    protected $primaryKey = 'role_id';

    public $incrementing = true;

    protected $keyType = 'int';

    protected $fillable = [
        'role_name',
        'role_permissions',
    ];

    protected $casts = [
        'role_permissions' => 'int',
    ];

    public function myPermissions(): array
    {
        $reflection = new ReflectionClass(IBMSPermissions::class);
        $permissions = $reflection->getConstants();
        $permArray = [];

        foreach ($permissions as $name => $value) {
            if ($this->hasPermission($value)) {
                $permArray[] = $name;
            }
        }

        return $permArray;

    }

    public function users()
    {
        return $this->hasMany(User::class); // links to 'users.role_id'
    }
}
?>

