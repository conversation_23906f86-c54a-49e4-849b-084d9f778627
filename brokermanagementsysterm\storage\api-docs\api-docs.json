{"openapi": "3.0.0", "info": {"title": "Broker Management System API", "description": "API documentation for Broker Management System", "contact": {"email": "<EMAIL>"}, "version": "0.0.1"}, "servers": [{"url": "http://localhost:8000", "description": "Local development server"}], "paths": {"/api/v1/clients": {"post": {"tags": ["Clients"], "summary": "Create a new client", "description": "Creates a new IBMS client record. The authenticated user is set as the broker.", "operationId": "storeClient", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["first_name", "last_name", "contact_phone", "contact_email", "id_number"], "properties": {"first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "contact_phone": {"type": "string", "example": "+254712345678"}, "contact_email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "id_number": {"type": "integer", "example": 12345678}}, "type": "object"}}}}, "responses": {"201": {"description": "Client successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "The given data was invalid."}, "errors": {"type": "object"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/clients/{id}": {"get": {"tags": ["Clients"], "summary": "Retrieve a specific client", "description": "Returns a single client owned by the authenticated broker by ID.", "operationId": "getClientById", "parameters": [{"name": "id", "in": "path", "description": "The client ID", "required": true, "schema": {"type": "integer", "example": 102}}], "responses": {"200": {"description": "Client retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "404": {"description": "Client not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Client not found"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Clients"], "summary": "Update an existing client", "description": "Updates a client owned by the authenticated broker. Only provided fields will be updated.", "operationId": "updateClient", "parameters": [{"name": "id", "in": "path", "description": "The ID of the client to update", "required": true, "schema": {"type": "integer", "example": 102}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "contact_phone": {"type": "string", "example": "+254712345678"}, "contact_email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "id_number": {"type": "integer", "example": 12345678}}, "type": "object"}}}}, "responses": {"200": {"description": "Client successfully updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Client"}}}}, "404": {"description": "Client not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Client not found"}}, "type": "object"}}}}, "422": {"description": "Validation error"}, "500": {"description": "Error updating client"}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Clients"], "summary": "Delete a client", "description": "Deletes a client belonging to the authenticated broker.", "operationId": "destroyClient", "parameters": [{"name": "id", "in": "path", "description": "ID of the client to delete", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Client deleted successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Client deleted successfully"}}, "type": "object"}}}}, "404": {"description": "Client not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Client not found"}}, "type": "object"}}}}, "500": {"description": "Error deleting client", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Error deleting client"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/preferences": {"get": {"tags": ["Client Preferences"], "summary": "List all client preferences", "operationId": "a4c26ad169df0639fffe7fc652b904b2", "responses": {"200": {"description": "Successful operation"}}}}, "/api/v1/clients/{id}/preferences": {"get": {"tags": ["Client Preferences"], "summary": "Get a client's preferences", "operationId": "8a2c4b24b28446105282adcb1d4bd151", "parameters": [{"name": "id", "in": "path", "description": "Client Preferences ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Client preferences returned"}}}, "put": {"tags": ["Client Preferences"], "summary": "Update client preferences", "operationId": "3573be7944888063eae971989feaab03", "parameters": [{"name": "id", "in": "path", "description": "Client Preferences ID", "required": true, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"preferred_insurers": {"type": "array", "items": {"type": "string"}}, "preferred_cover_types": {"type": "array", "items": {"type": "string", "enum": ["comprehensive", "third_party"]}}, "preferred_channel": {"type": "string", "enum": ["email", "phone", "whatsapp"]}, "renewal_reminder_enabled": {"type": "boolean"}, "budget_min": {"type": "number", "format": "float"}, "budget_max": {"type": "number", "format": "float"}}, "type": "object"}}}}, "responses": {"200": {"description": "Preferences updated successfully"}, "422": {"description": "Validation failed"}}}, "post": {"tags": ["Client Preferences"], "summary": "Store client preferences", "operationId": "e9c22db1cdf6e3187d0fe7626496145a", "parameters": [{"name": "id", "in": "path", "description": "Client UUID", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["client_id"], "properties": {"client_id": {"type": "string", "example": "123e4567-e89b-12d3-a456-************"}, "preferred_insurers": {"type": "array", "items": {"type": "string"}}, "preferred_cover_types": {"type": "array", "items": {"type": "string", "enum": ["comprehensive", "third_party"]}}, "preferred_channel": {"type": "string", "enum": ["email", "phone", "whatsapp"]}, "renewal_reminder_enabled": {"type": "boolean"}, "budget_min": {"type": "number", "format": "float"}, "budget_max": {"type": "number", "format": "float"}}, "type": "object"}}}}, "responses": {"201": {"description": "Preferences saved successfully"}, "422": {"description": "Validation failed"}}}, "delete": {"tags": ["Client Preferences"], "summary": "Delete client preferences", "operationId": "19093cd40c7e44decaa9f3ee53fea235", "parameters": [{"name": "id", "in": "path", "description": "Client Preferences ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Preferences deleted"}}}}, "/api/v1/cover-types": {"post": {"tags": ["Cover Types"], "summary": "Create a new cover type", "operationId": "storeCoverType", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["cover_name", "cover_description", "cover_isactive", "cover_code", "product_id"], "properties": {"cover_name": {"type": "string", "example": "Third Party"}, "cover_description": {"type": "string", "example": "Third party motor insurance"}, "cover_isactive": {"type": "boolean", "example": true}, "cover_code": {"type": "integer", "example": 101}, "product_id": {"type": "integer", "example": 1}}, "type": "object"}}}}, "responses": {"201": {"description": "Cover created successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Cover created successfully"}, "cover": {"$ref": "#/components/schemas/ProductCoverType"}}, "type": "object"}}}}, "422": {"description": "Validation failed"}, "500": {"description": "Error creating cover"}}, "security": [{"sanctum": []}]}}, "/api/v1/cover-types/{id}": {"get": {"tags": ["Cover Types"], "summary": "Get a specific cover type by ID", "operationId": "getCoverTypeById", "parameters": [{"name": "id", "in": "path", "description": "ID of the cover type", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Cover type found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductCoverType"}}}}, "404": {"description": "Cover type not found"}}, "security": [{"sanctum": []}]}, "put": {"tags": ["Cover Types"], "summary": "Update an existing cover type", "operationId": "updateCoverType", "parameters": [{"name": "id", "in": "path", "description": "ID of the cover type to update", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"cover_name": {"type": "string", "example": "Comprehensive"}, "cover_description": {"type": "string", "example": "Full cover insurance"}, "cover_isactive": {"type": "boolean", "example": true}, "cover_code": {"type": "integer", "example": 102}, "product_id": {"type": "integer", "example": 1}}, "type": "object"}}}}, "responses": {"200": {"description": "Cover updated successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Cover updated successfully"}, "cover": {"$ref": "#/components/schemas/ProductCoverType"}}, "type": "object"}}}}, "404": {"description": "Cover type not found"}, "422": {"description": "Validation error"}}, "security": [{"sanctum": []}]}, "delete": {"tags": ["Cover Types"], "summary": "Delete a cover type", "operationId": "deleteCoverType", "parameters": [{"name": "id", "in": "path", "description": "ID of the cover type to delete", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Cover deleted successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Cover deleted successfully"}}, "type": "object"}}}}, "404": {"description": "Cover type not found"}}, "security": [{"sanctum": []}]}}, "/api/v1/vehicles": {"get": {"tags": ["Vehicles"], "summary": "List all vehicles", "operationId": "560ce11d7dce684a8ef9eee8ac1c7991", "responses": {"200": {"description": "Successful response"}, "401": {"description": "Unauthenticated"}}, "security": [{"sanctum": []}]}}, "/api/v1/vehicles/client/{clientid}": {"get": {"tags": ["Vehicles"], "summary": "Get vehicles by client", "description": "Returns a list of vehicles for a specific client, with optional filters for registration number, make, and model.", "operationId": "getVehiclesByClient", "parameters": [{"name": "clientId", "in": "path", "description": "ID of the client", "required": true, "schema": {"type": "integer"}}, {"name": "registration_number", "in": "query", "description": "Filter by registration number", "required": false, "schema": {"type": "string"}}, {"name": "make", "in": "query", "description": "Filter by vehicle make", "required": false, "schema": {"type": "string"}}, {"name": "model", "in": "query", "description": "Filter by vehicle model", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "List of vehicles", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Vehicle"}}}}}, "403": {"description": "Unauthorized or Client not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Unauthorized or Client not found"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "api/v1/vehicles": {"post": {"tags": ["Vehicles"], "summary": "Create a new vehicle", "description": "Stores a new vehicle in the system.", "operationId": "createVehicle", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["client_id", "make", "model", "registration_number", "mileage", "value", "cover_type"], "properties": {"client_id": {"type": "integer", "example": 101}, "make": {"type": "string", "example": "Toyota"}, "model": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "registration_number": {"type": "string", "example": "KDA123A"}, "mileage": {"type": "integer", "example": 45000}, "value": {"type": "number", "format": "float", "example": 1200000.5}, "cover_type": {"type": "string", "example": "Comprehensive"}}, "type": "object"}}}}, "responses": {"201": {"description": "Vehicle created successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "vehicle create success"}, "vehicle": {"$ref": "#/components/schemas/Vehicle"}}, "type": "object"}}}}, "500": {"description": "Error creating vehicle", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Error creating vehicle"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "api/v1/vehicles/{id}": {"get": {"tags": ["Vehicles"], "summary": "Get a vehicle by ID", "description": "Returns a single vehicle by its ID.", "operationId": "getVehicleById", "parameters": [{"name": "id", "in": "path", "description": "ID of the vehicle", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Vehicle found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Vehicle"}}}}, "404": {"description": "Vehicle not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Vehicle not found"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/api/v1/vehicles/{id}": {"put": {"tags": ["Vehicles"], "summary": "Update a vehicle", "description": "Updates the details of a vehicle by its ID. Only the provided fields will be updated.", "operationId": "updateVehicle", "parameters": [{"name": "id", "in": "path", "description": "ID of the vehicle to update", "required": true, "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"make": {"type": "string", "example": "Toyota"}, "model": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "IBMS_VEHICLE_REG_NUMBER": {"type": "string", "example": "KDA123A"}, "mileage": {"type": "integer", "example": 45000}, "value": {"type": "number", "format": "float", "example": 1200000.5}, "cover_type": {"type": "string", "example": "Comprehensive"}}, "type": "object"}}}}, "responses": {"200": {"description": "Vehicle updated successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Vehicle updated successfully"}, "vehicle": {"$ref": "#/components/schemas/Vehicle"}}, "type": "object"}}}}, "404": {"description": "Vehicle not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Vehicle not found"}}, "type": "object"}}}}, "500": {"description": "Error updating vehicle", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Error updating vehicle"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}, "delete": {"tags": ["Vehicles"], "summary": "Delete a vehicle", "description": "Deletes a vehicle by its ID.", "operationId": "deleteVehicle", "parameters": [{"name": "id", "in": "path", "description": "ID of the vehicle to delete", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "Vehicle deleted successfully", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Vehicle deleted successfully"}}, "type": "object"}}}}, "404": {"description": "Vehicle not found", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Vehicle not found"}}, "type": "object"}}}}, "500": {"description": "Error deleting vehicle", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Error deleting vehicle"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"Client": {"required": ["first_name", "last_name", "contact_email", "contact_phone", "id_number", "broker_id"], "properties": {"client_id": {"type": "integer", "example": 1}, "first_name": {"type": "string", "example": "<PERSON>"}, "last_name": {"type": "string", "example": "<PERSON><PERSON>"}, "contact_email": {"type": "string", "format": "email", "example": "<EMAIL>"}, "contact_phone": {"type": "string", "example": "+254712345678"}, "id_number": {"type": "integer", "example": 12345678}, "broker_id": {"type": "integer", "example": 2}, "created_at": {"type": "string", "format": "date-time", "example": "2025-06-27T10:00:00Z"}, "updated_at": {"type": "string", "format": "date-time", "example": "2025-06-27T10:00:00Z"}}, "type": "object"}, "ClientPreferences": {"title": "Client Preferences", "description": "Insurance preferences associated with a client", "required": ["client_id"], "properties": {"client_id": {"description": "Client UUID reference", "type": "string", "format": "uuid"}, "preferred_insurers": {"description": "List of preferred insurance companies", "type": "array", "items": {"type": "string"}}, "preferred_cover_types": {"description": "Preferred cover types", "type": "array", "items": {"type": "string", "enum": ["comprehensive", "third_party"]}}, "preferred_channel": {"description": "Preferred communication channel", "type": "string", "enum": ["email", "phone", "whatsapp"]}, "renewal_reminder_enabled": {"description": "Whether renewal reminders are enabled", "type": "boolean"}, "budget_min": {"description": "Minimum budget for cover", "type": "number", "format": "float"}, "budget_max": {"description": "Maximum budget for cover", "type": "number", "format": "float"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "ProductCoverType": {"title": "Product Cover Type", "required": ["cover_name", "cover_description", "cover_code", "product_id"], "properties": {"id": {"type": "integer", "example": 1}, "cover_name": {"type": "string", "example": "Third Party"}, "cover_description": {"type": "string", "example": "Third party motor insurance"}, "cover_isactive": {"type": "boolean", "example": true}, "cover_code": {"type": "integer", "example": 101}, "product_id": {"type": "integer", "example": 1}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}, "type": "object"}, "IBMSRole": {"title": "IBMS Role", "description": "Role definition for the IBMS system", "required": ["role_id", "role_name"], "properties": {"role_id": {"description": "Unique identifier for the role", "type": "integer", "format": "int64"}, "role_name": {"description": "Human-readable name of the role", "type": "string"}, "role_permissions": {"description": "Bitmask representing assigned permissions", "type": "integer", "default": 0}, "created_at": {"description": "Timestamp of creation", "type": "string", "format": "date-time"}, "updated_at": {"description": "Timestamp of last update", "type": "string", "format": "date-time"}}, "type": "object"}, "IBMSUser": {"required": ["id", "first_name", "last_name", "email", "phone", "password", "role_id"], "properties": {"id": {"type": "integer", "format": "int64"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "phone": {"type": "string"}, "verified_at": {"type": "string", "format": "date-time", "nullable": true}, "email_verified": {"type": "boolean", "default": false}, "password": {"type": "string", "format": "password"}, "last_seen": {"type": "string", "format": "date-time"}, "role_id": {"type": "integer"}, "remember_token": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true}}, "type": "object"}, "Vehicle": {"title": "IBMS Vehicle", "description": "Vehicle registered in the IBMS system", "required": ["vehicle_id", "mileage", "make", "model", "registration_number", "value", "cover_type", "client_id"], "properties": {"vehicle_id": {"description": "Unique identifier of the vehicle", "type": "integer", "format": "int64"}, "mileage": {"description": "Current mileage reading of the vehicle", "type": "integer"}, "make": {"description": "Manufacturer of the vehicle (e.g. Toyota)", "type": "string"}, "model": {"description": "Vehicle model (e.g. Corolla)", "type": "string"}, "registration_number": {"description": "Unique registration plate number", "type": "string"}, "value": {"description": "Monetary value of the vehicle", "type": "number", "format": "float"}, "cover_type": {"description": "Associated insurance cover type ID", "type": "integer"}, "client_id": {"description": "Client who owns this vehicle", "type": "integer"}, "created_at": {"description": "Timestamp the vehicle record was created", "type": "string", "format": "date-time"}, "updated_at": {"description": "Timestamp the vehicle record was last updated", "type": "string", "format": "date-time"}}, "type": "object"}}}, "tags": [{"name": "Client Preferences", "description": "Manage insurance preferences for a client"}, {"name": "Clients", "description": "Clients"}, {"name": "Cover Types", "description": "Cover Types"}, {"name": "Vehicles", "description": "Vehicles"}]}