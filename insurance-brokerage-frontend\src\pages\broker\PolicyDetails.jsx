import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { <PERSON>, PageHeader, Button } from "../../components";
import { policyService, coverTypeService, insuranceService, benefitService } from "../../services";

export default function PolicyDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [policy, setPolicy] = useState(null);
  const [coverType, setCoverType] = useState(null);
  const [insurer, setInsurer] = useState(null);
  const [benefits, setBenefits] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        const policyData = await policyService.getPolicy(id);
        setPolicy(policyData);
        const [coverTypeData, insurerData, allBenefits] = await Promise.all([
          coverTypeService.getCoverType(policyData.coverTypeId),
          insuranceService.getCompany(policyData.insurerId),
          benefitService.getBenefits(),
        ]);
        setCoverType(coverTypeData);
        setInsurer(insurerData);
        setBenefits(allBenefits.filter(b => b.policies.includes(policyData.id)));
      } catch (err) {
        setError("The requested policy does not exist.");
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading policy details...</p>
        </div>
      </div>
    );
  }

  if (error || !policy) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card title="Policy Not Found">
          <p className="text-gray-600">{error}</p>
          <div className="mt-4 flex justify-end">
            <Button variant="outline" onClick={() => navigate("/broker/policy-catalog")}>Back to Catalog</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
        <PageHeader
          title={policy.name}
          subtitle="Policy Details"
          breadcrumbs={[
            { label: "Dashboard", href: "/broker/dashboard" },
            { label: "Policy Catalog", href: "/broker/policy-catalog" },
            { label: policy.name },
          ]}
          actions={[
            {
              label: "Back to Catalog",
              variant: "outline",
              onClick: () => navigate("/broker/policy-catalog"),
            },
          ]}
        />
        <Card title="Policy Information">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Policy Name</label>
              <p className="text-sm text-gray-900">{policy.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Cover Type</label>
              <p className="text-sm text-gray-900">{coverType ? coverType.name : "Unknown"}</p>
              <p className="text-xs text-gray-600">{coverType ? coverType.description : ""}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Description</label>
              <p className="text-sm text-gray-900">{policy.description}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Premium Rate</label>
              <p className="text-sm text-green-700 font-semibold">{policy.premiumRate}%</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Status</label>
              <p className={`text-sm font-semibold ${policy.status === "Active" ? "text-green-700" : "text-gray-600"}`}>{policy.status}</p>
            </div>
          </div>
        </Card>
        <Card title="Insurance Company">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Company Name</label>
              <p className="text-sm text-gray-900 font-semibold">{insurer ? insurer.name : "Unknown"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Contact Email</label>
              <p className="text-sm text-gray-900">{insurer ? insurer.email : ""}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Contact Phone</label>
              <p className="text-sm text-gray-900">{insurer ? insurer.phone : ""}</p>
            </div>
          </div>
        </Card>
        <Card title="Policy Benefits">
          <div className="space-y-4">
            {benefits.length > 0 ? (
              <ul className="list-disc ml-6 text-sm text-gray-900">
                {benefits.map((benefit) => (
                  <li key={benefit.id}>
                    <span className="font-medium">{benefit.name}:</span> {benefit.description}
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-500 text-sm">No specific benefits listed for this policy.</p>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
} 