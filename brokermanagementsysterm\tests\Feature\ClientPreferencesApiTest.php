<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\ClientPreferences;
use App\Models\RoleSetupService;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ClientPreferencesApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        RoleSetupService::createRoles();
        // Assuming roles are handled with a `broker()` state
        $this->broker = User::factory()->broker()->create();
        $this->actingAs($this->broker, 'sanctum');

        $this->client = Client::factory()->create(['broker_id' => $this->broker->user_id]);
        $this->vehicle = Vehicle::factory()->create(['client_id' => $this->client->client_id]);

    }

    public function test_broker_can_store_preferences_for_own_client()
    {
        $payload = [
            'preferred_insurers' => ['CIC'],
            'preferred_cover_types' => ['comprehensive'],
            'preferred_channel' => 'email',
            'renewal_reminder_enabled' => true,
            'budget_min' => 5000,
            'budget_max' => 15000,
        ];

        $response = $this->postJson("/api/v1/preferences/{$this->client->client_id}/{$this->vehicle->vehicle_id}", $payload);

        $response->assertStatus(201)
            ->assertJsonFragment(['message' => 'Preferences saved successfully']);

        $this->assertDatabaseHas('IBMS_CLIENT_PREFERENCES', [
            'vehicle_id' => $this->vehicle->vehicle_id,
            'preferred_channel' => 'email',
        ]);
    }

    public function test_broker_cannot_store_preferences_for_other_clients()
    {

        $anotherbroker = User::factory()->broker()->create();
        $otherClient = Client::factory()->create(['broker_id' => $anotherbroker]); // Not this broker's client
        $otherVehicle = Vehicle::factory()->create(['client_id' => $otherClient->client_id]);

        $response = $this->postJson("/api/v1/preferences/{$otherClient->client_id}/{$otherVehicle->vehicle_id}", [
            'preferred_channel' => 'email',
        ]);

        $response->assertStatus(403);
    }

    public function test_broker_can_view_client_preferences()
    {
        $prefs = ClientPreferences::factory()->create(['vehicle_id' => $this->vehicle->vehicle_id]);

        $response = $this->getJson("/api/v1/preferences/client/{$this->client->client_id}");

        $response->assertStatus(200)
            ->assertJsonFragment(['id' => $prefs->id]);
    }

    public function test_broker_can_update_preferences()
    {
        $prefs = ClientPreferences::factory()->create(['vehicle_id' => $this->vehicle->vehicle_id]);

        $response = $this->putJson("/api/v1/preferences/{$prefs->id}", [
            'preferred_channel' => 'whatsapp',
        ]);

        $response->assertStatus(200)
            ->assertJsonFragment(['preferred_channel' => 'whatsapp']);
    }

    public function test_broker_can_delete_preferences()
    {
        $prefs = ClientPreferences::factory()->create(['vehicle_id' => $this->vehicle->vehicle_id]);

        $response = $this->deleteJson("/api/v1/preferences/{$prefs->id}");

        $response->assertStatus(200)
            ->assertJsonFragment(['message' => 'Preference deleted successfully']);

        $this->assertDatabaseMissing('IBMS_CLIENT_PREFERENCES', ['id' => $prefs->id]);
    }
}
