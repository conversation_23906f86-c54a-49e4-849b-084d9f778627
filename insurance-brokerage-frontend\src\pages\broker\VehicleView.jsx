import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import PageHeader from "../../components/PageHeader";
import Card from "../../components/Card";

export default function VehicleView() {
  const location = useLocation();
  const navigate = useNavigate();
  const vehicle = location.state?.vehicle || {
    make: "",
    model: "",
    registration_number: "",
    vehicle_purpose: "",
    value: "",
  };
  const client = location.state?.client || null;

  return (
    <div className="w-full max-w-4xl bg-white rounded-2xl shadow-lg p-0 mx-auto relative mt-8">
      <PageHeader
        title={`Vehicle: ${vehicle.make} ${vehicle.model} (${vehicle.registration_number})`}
        subtitle="Vehicle Details"
        breadcrumbs={[
          { label: "Broker Dashboard", href: "/broker/dashboard" },
          { label: "Clients", href: "/broker/clients" },
          { label: `Vehicle: ${vehicle.make} ${vehicle.model} (${vehicle.registration_number})` },
        ].filter(Boolean)}
        actions={[
          {
            label: "Go Back to Vehicle List",
            variant: "primary",
            onClick: () => navigate("/broker/vehicles/details", { state: { client } }),
          },
        ]}
      />
      <div className="h-full w-full ">
        <Card title="Vehicle Information" className="shadow-none border-none p-0">
          <div className="grid grid-cols-1 gap-y-4 text-base">
            {client && (
              <div>
                <span className="font-medium text-gray-700">Owner:</span> {client.first_name} {client.last_name}
              </div>
            )}
            <div>
              <span className="font-medium text-gray-700">Make:</span> {vehicle.make}
            </div>
            <div>
              <span className="font-medium text-gray-700">Model:</span> {vehicle.model}
            </div>
            <div>
              <span className="font-medium text-gray-700">Registration No.:</span> {vehicle.registration_number}
            </div>
            <div>
              <span className="font-medium text-gray-700">Usage:</span> {vehicle.vehicle_purpose}
            </div>
            <div>
              <span className="font-medium text-gray-700">Value:</span> {vehicle.value}
            </div>
          </div>
          <div className="flex justify-end mt-8 gap-4">
            <button
              className="bg-blue-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-blue-700 transition"
              onClick={() => navigate("/broker/vehicles/recommendations", { state: { vehicle, client } })}
            >
              Recommend Policy
            </button>
            <button
              className="bg-green-600 text-white px-6 py-2 rounded-lg font-semibold hover:bg-green-700 transition"
              onClick={() => navigate("/broker/vehicles/preferences", { state: { vehicle, client } })}
            >
              Set Preferences
            </button>
            {/* <button
              className="bg-gray-200 text-gray-700 px-6 py-2 rounded-lg font-semibold"
              onClick={() => navigate("/broker/vehicles/details", { state: { client } })}
            >
              Close
            </button> */}
          </div>
        </Card>
      </div>
    </div>
  );
}
