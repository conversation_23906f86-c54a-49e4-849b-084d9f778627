<?php

namespace Database\Factories;

use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Factories\Factory;

class VehicleFactory extends Factory
{
    protected $model = Vehicle::class;

    public function definition(): array
    {
        return [
            'make' => $this->faker->company(),                  // e.g. Toyota
            'model' => $this->faker->word(),                    // e.g. Corolla
            'registration_number' => strtoupper($this->faker->bothify('K?? ####')), // e.g. KCB 1234
            'mileage' => $this->faker->numberBetween(0, 9999999),
            'value' => $this->faker->numberBetween(300000, 7000000),
            'vehicle_purpose' => $this->faker->randomElement(['Commercial', 'Private', 'Public Transport']),
        ];
    }
}
