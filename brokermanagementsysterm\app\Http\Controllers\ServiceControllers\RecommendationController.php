<?php

namespace App\Http\Controllers\ServiceControllers;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\Vehicle;
use App\Services\RecommendationGAService;
use Illuminate\Http\Request;

class RecommendationController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/v1/recommend",
     *     operationId="recommendInsuranceProducts",
     *     summary="Generate personalized insurance product recommendations for a client",
     *     description="Returns a list of insurance category products ranked based on client preferences.",
     *     tags={"Recommendation"},
     *     security={{"bearerAuth":{}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(
     *             required={"client_id"},
     *
     *             @OA\Property(
     *                 property="client_id",
     *                 type="string",
     *                 format="uuid",
     *                 example="27d672b6-f7d0-4ffb-9255-904af4abd66a",
     *                 description="The UUID of the client"
     *             )
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful recommendation",
     *
     *         @OA\JsonContent(
     *             type="array",
     *
     *             @OA\Items(
     *
     *                 @OA\Property(property="vehicle_id", type="string"),
     *                 @OA\Property(property="product_id", type="string"),
     *                 @OA\Property(property="cover_type", type="string"),
     *                 @OA\Property(property="cover_description", type="string"),
     *                 @OA\Property(property="insurer_name", type="string"),
     *                 @OA\Property(property="price", type="number", format="float"),
     *                 @OA\Property(property="fitness_score", type="number", format="float")
     *             )
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=404,
     *         description="Client Not Found or No Preferences",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Client preferences not found")
     *         )
     *     )
     * )
     */
    public function recommend(Request $request)
    {
        $client_id = $request->client_id;
        $current_user = $request->user();

        $client = Client::where('client_id', $client_id)->first();

        if (! $client) {
            return response()->json(['message' => 'Client Not Found'], 404);
        }

        // Authorization: if broker, ensure they own the client
        if ($current_user->isBroker() && $client->broker_id !== $current_user->user_id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Ensure at least one vehicle has preferences
        $hasPreferences = Vehicle::where('client_id', $client->client_id)
            ->whereHas('preferences')
            ->exists();

        if (! $hasPreferences) {
            return response()->json(['message' => 'Client preferences not found'], 404);
        }

        $recommendationService = new RecommendationGAService;
        $recommendations = $recommendationService->recommendForClient($client);

        return response()->json($recommendations);
    }
}
