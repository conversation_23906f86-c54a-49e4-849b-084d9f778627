import React, { useState, useEffect } from 'react';
import {
  Users, Building, Briefcase, FileText, Activity, Clock, PlusCircle,
  BarChart, PieChart, TrendingUp, TrendingDown, CheckCircle, XCircle, Clock4,
  ShieldCheck, CalendarDays
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const AdminDashboard = () => {
  const navigate = useNavigate();
  // Mock Data for demonstration purposes
  const stats = [
    // { name: 'Total Users', value: '1,250', icon: Users, color: 'text-blue-500', bgColor: 'bg-blue-100' },
    { name: 'Active Brokers', value: '120', icon: Users, color: 'text-indigo-500', bgColor: 'bg-indigo-100' },
    { name: 'Registered Insurers', value: '15', icon: Building, color: 'text-purple-500', bgColor: 'bg-purple-100' },
    { name: 'Total Policies', value: '5,800', icon: Briefcase, color: 'text-green-500', bgColor: 'bg-green-100' },
    { name: 'Pending Quotes', value: '85', icon: FileText, color: 'text-yellow-500', bgColor: 'bg-yellow-100' },
    // { name: 'Policies Expiring Soon', value: '45', icon: Clock, color: 'text-red-500', bgColor: 'bg-red-100' },
  ];

  const quickActions = [
    { name: 'Add New User', icon: PlusCircle, link: '/admin/user-registration', color: 'bg-blue-600' },
    { name: 'Create Insurer', icon: Building, link: '/admin/insurers/create', color: 'bg-purple-600' },
    { name: 'Add Benefit', icon: ShieldCheck, link: '/admin/benefits/create', color: 'bg-green-600' },
  ];

  // Mock data for Policy Status bar graph
  const policyStatusData = [
    { status: 'Active', count: 4500, color: 'bg-green-500' },
    { status: 'Expired', count: 1000, color: 'bg-red-500' },
    { status: 'Pending', count: 300, color: 'bg-yellow-500' },
  ];
  const maxPolicyCount = Math.max(...policyStatusData.map(item => item.count));


  // Mock data for Clients Onboarded graph (full dataset - extended for more years)
  const allClientsOnboardedData = [
    { period: 'Jan 2020', count: 5 },
    { period: 'Feb 2020', count: 8 },
    { period: 'Mar 2020', count: 12 },
    { period: 'Apr 2020', count: 15 },
    { period: 'May 2020', count: 20 },
    { period: 'Jun 2020', count: 25 },
    { period: 'Jul 2020', count: 30 },
    { period: 'Aug 2020', count: 35 },
    { period: 'Sep 2020', count: 40 },
    { period: 'Oct 2020', count: 45 },
    { period: 'Nov 2020', count: 50 },
    { period: 'Dec 2020', count: 55 },
    { period: 'Jan 2021', count: 60 },
    { period: 'Feb 2021', count: 70 },
    { period: 'Mar 2021', count: 85 },
    { period: 'Apr 2021', count: 90 },
    { period: 'May 2021', count: 100 },
    { period: 'Jun 2021', count: 115 },
    { period: 'Jul 2021', count: 120 },
    { period: 'Aug 2021', count: 135 },
    { period: 'Sep 2021', count: 140 },
    { period: 'Oct 2021', count: 150 },
    { period: 'Nov 2021', count: 165 },
    { period: 'Dec 2021', count: 180 },
    { period: 'Jan 2022', count: 190 },
    { period: 'Feb 2022', count: 205 },
    { period: 'Mar 2022', count: 220 },
    { period: 'Apr 2022', count: 230 },
    { period: 'May 2022', count: 245 },
    { period: 'Jun 2022', count: 260 },
    { period: 'Jul 2022', count: 270 },
    { period: 'Aug 2022', count: 285 },
    { period: 'Sep 2022', count: 300 },
    { period: 'Oct 2022', count: 310 },
    { period: 'Nov 2022', count: 325 },
    { period: 'Dec 2022', count: 340 },
    { period: 'Jan 2023', count: 350 },
    { period: 'Feb 2023', count: 365 },
    { period: 'Mar 2023', count: 380 },
    { period: 'Apr 2023', count: 375 },
    { period: 'May 2023', count: 390 },
    { period: 'Jun 2023', count: 410 },
    { period: 'Jul 2023', count: 400 },
    { period: 'Aug 2023', count: 420 },
    { period: 'Sep 2023', count: 430 },
    { period: 'Oct 2023', count: 415 },
    { period: 'Nov 2023', count: 440 },
    { period: 'Dec 2023', count: 455 },
    { period: 'Jan 2024', count: 460 },
    { period: 'Feb 2024', count: 470 },
    { period: 'Mar 2024', count: 485 },
    { period: 'Apr 2024', count: 490 },
    { period: 'May 2024', count: 500 },
    { period: 'Jun 2024', count: 510 },
    { period: 'Jul 2024', count: 520 }, // Current month (example)
  ];

  // Get current date for max attribute of date input
  const today = new Date();
  const maxCurrentDate = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

  // Helper to get the date string for the first period in the data
  const getFirstPeriodDate = () => {
    const firstPeriod = allClientsOnboardedData[0]?.period;
    if (firstPeriod) {
      const [monthStr, yearStr] = firstPeriod.split(' ');
      const monthIndex = new Date(Date.parse(monthStr + " 1, 2000")).getMonth();
      const date = new Date(parseInt(yearStr), monthIndex, 1);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
    return '';
  };

  // State for the selected start and end dates in the input fields
  const [inputStartDate, setInputStartDate] = useState(getFirstPeriodDate());
  const [inputEndDate, setInputEndDate] = useState(maxCurrentDate);

  // State for the dates actually used to filter the graph data
  const [displayStartDate, setDisplayStartDate] = useState(getFirstPeriodDate());
  const [displayEndDate, setDisplayEndDate] = useState(maxCurrentDate);

  // Handler for the "Show" button
  const handleShowClick = () => {
    setDisplayStartDate(inputStartDate);
    setDisplayEndDate(inputEndDate);
  };

  // Filter clientsOnboardedData based on displayStartDate and displayEndDate
  const filteredClientsOnboardedData = allClientsOnboardedData.filter(item => {
    const [monthStr, yearStr] = item.period.split(' ');
    const itemDate = new Date(parseInt(yearStr), new Date(Date.parse(monthStr + " 1, 2000")).getMonth(), 1);

    const startDate = new Date(displayStartDate);
    const endDate = new Date(displayEndDate);

    return itemDate >= startDate && itemDate <= endDate;
  });

  // Recalculate max/min for filtered data
  const maxClientsCount = filteredClientsOnboardedData.length > 0
    ? Math.max(...filteredClientsOnboardedData.map(item => item.count))
    : 0;
  const minClientsCount = filteredClientsOnboardedData.length > 0
    ? Math.min(...filteredClientsOnboardedData.map(item => item.count))
    : 0;


  // Prepare data for line chart SVG
  const chartWidth = 500;
  const chartHeight = 200;
  const padding = 30;
  const innerWidth = chartWidth - 2 * padding;
  const innerHeight = chartHeight - 2 * padding;

  const xScale = (index) => padding + (index / (filteredClientsOnboardedData.length - 1 || 1)) * innerWidth;
  const yScale = (count) => {
    const range = maxClientsCount - minClientsCount;
    if (range === 0) return padding + innerHeight / 2;
    return padding + innerHeight - ((count - minClientsCount) / range) * innerHeight;
  };

  const linePath = filteredClientsOnboardedData.map((d, i) => {
    const x = xScale(i);
    const y = yScale(d.count);
    return `${i === 0 ? 'M' : 'L'} ${x} ${y}`;
  }).join(' ');

  const linePoints = filteredClientsOnboardedData.map((d, i) => ({
    x: xScale(i),
    y: yScale(d.count),
    count: d.count,
    period: d.period
  }));

  // Mock data for Quotes by Status Bar Chart
  const quotesByStatusData = [
    { status: 'Approved', count: 65, color: 'bg-green-500' },
    { status: 'Pending', count: 18, color: 'bg-yellow-500' },
    { status: 'Rejected', count: 7, color: 'bg-red-500' },
  ];
  const maxQuoteCount = Math.max(...quotesByStatusData.map(item => item.count));



  return (
    <div className="p-8 bg-gray-100 min-h-screen font-inter">
      <h1 className="text-4xl font-extrabold text-gray-900 mb-8">Admin Dashboard</h1>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-10">
        {stats.map((stat, index) => (
          <div
            key={index}
            className="bg-white rounded-xl shadow-lg p-6 flex items-center space-x-4 transform transition-transform duration-300 hover:scale-105 hover:shadow-xl"
          >
            <div className={`p-3 rounded-full ${stat.bgColor}`}>
              <stat.icon size={24} className={stat.color} />
            </div>
            <div>
              <p className="text-gray-500 text-sm font-medium">{stat.name}</p>
              <h2 className="text-2xl font-bold text-gray-900">{stat.value}</h2>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions - Three buttons on one row */}
      <div className="bg-white rounded-xl shadow-lg p-6 mb-10">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <PlusCircle size={24} className="mr-3 text-gray-600" /> Quick Actions
        </h2>
        <div className="flex flex-wrap justify-around gap-4">
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={() => navigate(action.link)} // Replace with navigate(action.link)
              className={`flex-1 min-w-[180px] max-w-[calc(33%-1rem)] flex items-center justify-center py-3 px-4 rounded-lg text-white font-semibold
                         ${action.color} hover:opacity-90 transition-opacity duration-200 shadow-md`}
            >
              <action.icon size={20} className="mr-3" />
              {action.name}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

        {/* Policy Status Horizontal Bar Graph */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <BarChart size={24} className="mr-3 text-gray-600" /> Quotes by Status
          </h2>
          <div className="space-y-4 py-4">
            {quotesByStatusData.map((item, index) => (
              <div key={index} className="flex items-center">
                <span className="w-24 text-right text-sm font-medium text-gray-700 mr-4">{item.status}</span>
                <div className="flex-grow bg-gray-200 rounded-full h-6 relative overflow-hidden">
                  <div
                    className={`h-full rounded-full ${item.color} transition-all duration-500 ease-out`}
                    style={{ width: `${(item.count / maxQuoteCount) * 100}%` }}
                  ></div>
                  <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs font-semibold text-white">
                    {item.count}
                  </span>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-6 text-center text-gray-600">
            <p className="text-lg font-semibold">Total Quotes: {quotesByStatusData.reduce((sum, item) => sum + item.count, 0)}</p>
          </div>
        </div>

        {/* Clients Onboarded Over Time Line Chart */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <CalendarDays size={24} className="mr-3 text-gray-600" /> Clients Onboarded Over Time
          </h2>
          {/* Date Inputs and Show button for filtering */}
          <div className="mb-4 flex flex-wrap gap-4 items-center">
            <div className="flex items-center gap-2">
              <label htmlFor="startDate" className="text-sm font-medium text-gray-700">From:</label>
              <input
                type="date"
                id="startDate"
                value={inputStartDate}
                onChange={(e) => setInputStartDate(e.target.value)}
                max={inputEndDate}
                className="p-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="flex items-center gap-2">
              <label htmlFor="endDate" className="text-sm font-medium text-gray-700">Up to:</label>
              <input
                type="date"
                id="endDate"
                value={inputEndDate}
                onChange={(e) => setInputEndDate(e.target.value)}
                max={maxCurrentDate}
                min={inputStartDate}
                className="p-2 border border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button
              onClick={handleShowClick}
              className="px-4 py-2 bg-blue-600 text-white font-semibold rounded-md shadow-sm
                         hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                         transition-colors duration-200"
            >
              Show
            </button>
          </div>

          {filteredClientsOnboardedData.length > 0 ? (
            <div className="relative overflow-x-auto pb-4">
              <svg viewBox={`0 0 ${chartWidth} ${chartHeight}`} className="w-full h-64">
                {/* X-Axis Line */}
                <line x1={padding} y1={padding + innerHeight} x2={padding + innerWidth} y2={padding + innerHeight} stroke="#e2e8f0" strokeWidth="2" />
                {/* Y-Axis Line */}
                <line x1={padding} y1={padding} x2={padding} y2={padding + innerHeight} stroke="#e2e8f0" strokeWidth="2" />

                {/* Y-Axis Labels (simplified: min, max, and a couple in between) */}
                {[minClientsCount, Math.round((minClientsCount + maxClientsCount) / 2), maxClientsCount].map((value, i) => (
                  <text
                    key={i}
                    x={padding - 10}
                    y={yScale(value) + 5}
                    textAnchor="end"
                    className="text-xs text-gray-600"
                    fill="#4a5568"
                  >
                    {value}
                  </text>
                ))}

                {/* X-Axis Labels (every few periods to avoid clutter) */}
                {filteredClientsOnboardedData.map((d, i) => (
                  (i % Math.ceil(filteredClientsOnboardedData.length / 6) === 0 || i === filteredClientsOnboardedData.length - 1) && (
                    <text
                      key={i}
                      x={xScale(i)}
                      y={padding + innerHeight + 15}
                      textAnchor="middle"
                      className="text-xs text-gray-600"
                      fill="#4a5568"
                    >
                      {d.period}
                    </text>
                  )
                ))}

                {/* Line Path */}
                <path d={linePath} fill="none" stroke="#3b82f6" strokeWidth="2" />

                {/* Data Points */}
                {linePoints.map((point, index) => (
                  <circle
                    key={index}
                    cx={point.x}
                    cy={point.y}
                    r="4"
                    fill="#3b82f6"
                    stroke="#ffffff"
                    strokeWidth="2"
                  >
                    {/* Tooltip on hover (using title for simple SVG tooltip) */}
                    <title>{`${point.period}: ${point.count} clients`}</title>
                  </circle>
                ))}
              </svg>
            </div>
          ) : (
            <p className="text-gray-500 text-center mt-8">No client data available for the selected period.</p>
          )}

          <div className="mt-6 text-center text-gray-600">
            <p className="text-lg font-semibold">Total Clients Onboarded: {filteredClientsOnboardedData.reduce((sum, item) => sum + item.count, 0)}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
