<?php

namespace Tests\Unit;

use App\Models\IBMSPermissions;
use App\Models\Role;
use Illuminate\Foundation\Testing\RefreshDatabase;
use ReflectionClass;
use Tests\TestCase;

class RoleModelTest extends TestCase
{
    use RefreshDatabase;

    public function test_role(): void
    {
        $allPermissions = (new ReflectionClass(IBMSPermissions::class))->getConstants();

        $role = Role::factory()->create([
            'role_name' => 'testRole',
            'role_permissions' => array_sum($allPermissions),

        ]);

        $this->assertEquals('testRole', $role->role_name);

        $this->assertEquals(array_sum($allPermissions), $role->role_permissions);
    }

    public function test_rolemy_permissions(): void
    {
        $brokerPermissions = [
            IBMSPermissions::IBMS_ADD_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_EDIT_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_ADD_CLIENT,
            IBMSPermissions::IBMS_EDIT_CLIENT,
            IBMSPermissions::IBMS_VIEW_CLIENT,
            IBMSPermissions::IBMS_CREATE_QUOTATION,
            IBMSPermissions::IBMS_EDIT_QUOTATION,
            IBMSPermissions::IBMS_VIEW_QUOTATION,
            IBMSPermissions::IBMS_VIEW_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_ENABLE_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_DISABLE_INSURANCE_COMPANY,
        ];
        $role = Role::factory()->create([
            'role_name' => 'testRole4',
            'role_permissions' => array_sum($brokerPermissions),
        ]);

        $this->assertEquals(array_sum($brokerPermissions), $role->role_permissions);
    }
}
