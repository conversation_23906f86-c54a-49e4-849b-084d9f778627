import axios from '../utils/axios';
import { mockUsers } from './mockData';

// Users API
export const userService = {
  // Get current user
  getCurrentUser: async () => {
    try {
      // For now, return mock data
      // const response = await axios.get('/api/user');
      // return response.data;
      
      // Simulate API delay
      //await new Promise(resolve => setTimeout(resolve, 300));
      const current_user = await axios.get('/api/v1/user');
      // Mock user data
     // const mockUser = mockUsers[0];
      
      return current_user.data;
    } catch (error) {
      throw error;
    }
  },

  // Get all users (admin only)
  getUsers: async (user_type) => {
    try {
      // For now, return mock data
      // const response = await axios.get('/api/user');
      // return response.data;

      // Simulate API delay
      //await new Promise(resolve => setTimeout(resolve, 300));
      const current_user = await axios.get(`/api/v1/users/list?user_type=${user_type}`);
      // Mock user data
      // const mockUser = mockUsers[0];

      return current_user.data;
    } catch (error) {
      throw error;
    }
  },

  // Get single user
  getUser: async (id) => {

      try {

        const user = await axios.get(`/api/v1/users/show/${id}`);


        return user.data;
      } catch (error) {
        throw error;
      }

  },

  // Create new user
  createUser: async ({userData, setExternalErrors, setLoading, setSuccess}) => {
    try {
      // For now, return mock data
      // const response = await axios.get('/api/user');
      // return response.data;

      // Simulate API delay
      //await new Promise(resolve => setTimeout(resolve, 300));
      const current_user = await axios.post('/api/v1/users/store', userData);
      // Mock user data
      // const mockUser = mockUsers[0];
      setSuccess(true)
      setLoading(false);
      return current_user.data;
    } catch (error) {
      setLoading(false);
      if (error.response.status === 422 && error.response.data?.errors) {
        const backendErrors = {};
        const apiErrors = error.response.data.errors;

        for (const key in apiErrors) {
          backendErrors[key] = apiErrors[key][0];
        }

        setExternalErrors(backendErrors);
      }
      else {
        throw error
      }
    }
  },

  // Update user
  updateUser: async (userData) => {
    try {
      const current_user = await axios.put('/api/v1/users/update', userData);
      // Mock user data
      // const mockUser = mockUsers[0];

      return current_user.data;
      

    } catch (error) {
      throw error;
    }
  },

  // Delete user
  deleteUser: async (id) => {
    try {
      const current_user = await axios.delete(`/api/v1/users/destroy/${id}`);
      // Mock user data
      // const mockUser = mockUsers[0];

      return current_user.data;


    } catch (error) {
      throw error;
    }
  },

  // Update profile
  updateProfile: async (profileData) => {
    try {
      // For now, simulate update
      // const response = await axios.put('/api/profile', profileData);
      // return response.data;

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const updatedProfile = {
        ...profileData,
        updatedAt: new Date().toISOString()
      };

      return updatedProfile;
    } catch (error) {
      throw error;
    }
  },

  changePassword: async ({form, setValidation, setSuccess, setLoading}) => {
    try {
//ssetLoading(true);
      setValidation({})

      axios
          .post('/api/v1/users/change-password', form)
          .then(() => {
            setSuccess(true);
            setLoading(false);
          })
          .catch(error => {
            setLoading(false);
            if (error.response.status === 422) {
              setValidation(error.response.data.errors)
            }
            else {

             throw error;

            }


          });
    } catch (error) {
      throw error;
    }
  },
};