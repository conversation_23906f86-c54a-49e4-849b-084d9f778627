import axios from '../utils/axios';
import { mockCoverTypes } from './mockData';

// Cover Types API
export const coverTypeService = {
  // Get all cover types
  getCoverTypes: async () => {
    try {
      // For now, return mock data
      // const response = await axios.get('/api/cover-types');
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 400));
      return mockCoverTypes;
    } catch (error) {
      throw error;
    }
  },

  // Get single cover type
  getCoverType: async (id) => {
    try {
      // For now, return mock data
      // const response = await axios.get(`/api/cover-types/${id}`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      const coverType = mockCoverTypes.find(ct => ct.id === parseInt(id));
      if (!coverType) {
        throw new Error('Cover type not found');
      }
      return coverType;
    } catch (error) {
      throw error;
    }
  },

  // Create new cover type
  createCoverType: async (coverTypeData) => {
    try {
      // For now, simulate creation
      // const response = await axios.post('/api/cover-types', coverTypeData);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 600));
      
      const newCoverType = {
        ...coverTypeData,
        id: Date.now(),
        status: "Active"
      };
      
      // In a real app, this would be added to the database
      mockCoverTypes.push(newCoverType);
      
      return newCoverType;
    } catch (error) {
      throw error;
    }
  },

  // Update cover type
  updateCoverType: async (id, coverTypeData) => {
    try {
      // For now, simulate update
      // const response = await axios.put(`/api/cover-types/${id}`, coverTypeData);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = mockCoverTypes.findIndex(ct => ct.id === parseInt(id));
      if (index === -1) {
        throw new Error('Cover type not found');
      }
      
      mockCoverTypes[index] = { ...mockCoverTypes[index], ...coverTypeData };
      return mockCoverTypes[index];
    } catch (error) {
      throw error;
    }
  },

  // Delete cover type
  deleteCoverType: async (id) => {
    try {
      // For now, simulate deletion
      // const response = await axios.delete(`/api/cover-types/${id}`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const index = mockCoverTypes.findIndex(ct => ct.id === parseInt(id));
      if (index === -1) {
        throw new Error('Cover type not found');
      }
      
      mockCoverTypes.splice(index, 1);
      return { success: true };
    } catch (error) {
      throw error;
    }
  }
}; 