<?php

namespace Database\Factories;

use App\Models\Insurance;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class InsuranceFactory extends Factory
{
    protected $model = Insurance::class;

    public function definition()
    {
        return [
            'insurance_id' => Str::uuid(),
            'name' => $this->faker->company.' Insurance',
            'company_code' => strtoupper(Str::random(3)),
            'email' => $this->faker->unique()->companyEmail,
            'phone_number' => $this->faker->phoneNumber,
            'address' => $this->faker->address,
            'logo_path' => 'logos/'.Str::random(10).'.png',
            'is_active' => $this->faker->boolean(90), // 90% chance of being active
        ];
    }
}
