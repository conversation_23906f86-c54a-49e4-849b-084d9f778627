import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import PageHeader from "../../components/PageHeader";
import Card from "../../components/Card";
import FormWizard from "../../components/FormWizard";
import Button from "../../components/Button";
import { clientService } from "../../services/clientService";

export default function ClientEdit() {
  const location = useLocation();
  const navigate = useNavigate();
  const client = location.state?.client || {
    firstName: "",
    lastName: "",
    idNumber: "",
    phone: "",
    email: "",
  };
 

  // Steps for FormWizard
  const wizardSteps = [
    {
      title: "Client Information",
      description: "Edit client details",
      fields: [
        {
          name: "first_name",
          label: "First Name",
          type: "text",
          placeholder: "Enter first name",
          required: true,
        },
        {
          name: "last_name",
          label: "Last Name",
          type: "text",
          placeholder: "Enter last name",
          required: true,
        },
        {
          name: "id_number",
          label: "ID Number",
          type: "text",
          placeholder: "Enter ID number",
          required: true,
        },
        {
          name: "contact_phone",
          label: "Phone Number",
          type: "tel",
          placeholder: "Enter phone number",
          required: true,
        },
        {
          name: "contact_email",
          label: "Email",
          type: "email",
          placeholder: "Enter email",
          required: true,
        },
      ],
      validation: (data, errors) => {
        if (!data.first_name) errors.first_name = "First name is required";
        if (!data.last_name) errors.last_name = "Last name is required";
        if (!data.id_number) errors.id_number = "ID number is required";
        if (!data.contact_phone) errors.contact_phone = "Phone number is required";
        if (!data.contact_email) errors.contact_email = "Email is required";
      },
    },
  ];

  // Pre-populate form data
  // const [initialFormData] = React.useState(client);

  const handleWizardComplete = async(data) => {
    console.log("Form data submitted:", data);
    // Here you would update the client in your backend or state
    // For now, just navigate back to the client list
    await clientService.updateClient(data)

    navigate("/broker/clients", { state: { updated: true } });
  };

  return (
    <div className="w-full max-w-4xl bg-white rounded-2xl shadow-lg p-0 mx-auto relative mt-8">
      <PageHeader
        title={`Edit: ${client.first_name} ${client.last_name}`}
        subtitle="Edit client information"
        breadcrumbs={[
          { label: "Clients", href: "/broker/clients" },
          { label: `Edit: ${client.first_name} ${client.last_name}` },
        ]}
        className="rounded-t-2xl w-full"
        actions={[
          {
            label: "Back to Client List",
            variant: "primary",
            onClick: () => navigate("/broker/clients"),
          },
        ]}
      />
      <div className="p-8">
        <Card title="FormWizard Component" subtitle="Step-by-step form wizard">
          <FormWizard
            steps={wizardSteps}
            onComplete={handleWizardComplete}
            onCancel={() => navigate("/broker/clients")}
            className=""
            initialData={client}
          />
        </Card>
      </div>
    </div>
  );
}
