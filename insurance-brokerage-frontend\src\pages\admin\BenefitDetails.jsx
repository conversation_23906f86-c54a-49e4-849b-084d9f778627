import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { <PERSON>, PageHeader, Button } from "../../components";
import { benefitService, policyService } from "../../services";

export default function BenefitDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [benefit, setBenefit] = useState(null);
  const [linkedPolicies, setLinkedPolicies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        const benefitData = await benefitService.getBenefit(id);
        setBenefit(benefitData);
        const allPolicies = await policyService.getPolicies();
        const linked = allPolicies.filter((p) => benefitData.policies.includes(p.id));
        setLinkedPolicies(linked);
      } catch (err) {
        setError("The requested benefit does not exist.");
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading benefit details...</p>
        </div>
      </div>
    );
  }

  if (error || !benefit) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card title="Benefit Not Found">
          <p className="text-gray-600">{error}</p>
          <div className="mt-4 flex justify-end">
            <Button variant="outline" onClick={() => navigate("/admin/benefits")}>Back to List</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
        <PageHeader
          title={benefit.name}
          subtitle="Benefit Details"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Benefits", href: "/admin/benefits" },
            { label: benefit.name },
          ]}
          actions={[
            {
              label: "Edit",
              variant: "primary",
              onClick: () => navigate(`/admin/benefits/${benefit.id}/edit`),
            },
          ]}
        />
        <Card title="Benefit Information">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Benefit Name</label>
              <p className="text-sm text-gray-900">{benefit.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Description</label>
              <p className="text-sm text-gray-900">{benefit.description}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Status</label>
              <p className={`text-sm font-semibold ${benefit.status === "Active" ? "text-green-700" : "text-gray-600"}`}>{benefit.status}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Linked Policies</label>
              {linkedPolicies.length > 0 ? (
                <ul className="list-disc ml-6 text-sm text-gray-900">
                  {linkedPolicies.map((policy) => (
                    <li key={policy.id}>{policy.name}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-500">No policies linked.</p>
              )}
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
