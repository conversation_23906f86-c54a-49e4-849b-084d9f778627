<?php

// app/Models/PolicyBenefit.php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PolicyBenefit extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'IBMS_POLICY_BENEFITS';

    protected $primaryKey = 'policy_benefit_id';

    protected $keyType = 'string';

    public $incrementing = false;

    protected $fillable = [
        'policy_benefit_id',
        'insurance_product_policy_id',
        'deescription',
    ];

    public function policy()
    {
        return $this->belongsTo(InsuranceProductPolicy::class, 'insurance_product_policy_id');
    }
}
