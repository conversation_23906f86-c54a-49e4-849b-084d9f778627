<?php

namespace App\Http\Controllers\Client;

use App\Http\Controllers\Controller;
use App\Models\Client;
use Illuminate\Http\Request;

class ClientController extends Controller
{
    /* @OA\Get(
     *     path="/api/v1/clients",
     *     operationId="getClients",
     *     tags={"Clients"},
     *     summary="List clients for the authenticated broker",
     *     description="Returns a list of clients associated with the authenticated broker",
     *     security={{"sanctum":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(ref="#/components/schemas/Client")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     )
     * )
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $brokerId = $user->user_id;

        $query = Client::where('broker_id', $brokerId);

        return response()->json($query->get());
    }

    /**
     * @OA\Post(
     *   path="/api/v1/clients",
     *   summary="Create a new client",
     *   description="Creates a new IBMS client record. The authenticated user is set as the broker.",
     *   operationId="storeClient",
     *   tags={"Clients"},
     *   security={{"bearerAuth":{}}},
     *
     *   @OA\RequestBody(
     *     required=true,
     *
     *     @OA\JsonContent(
     *       type="object",
     *       required={"first_name", "last_name", "contact_phone", "contact_email", "id_number"},
     *
     *       @OA\Property(property="first_name", type="string", example="Jane"),
     *       @OA\Property(property="last_name", type="string", example="Doe"),
     *       @OA\Property(property="contact_phone", type="string", example="+254712345678"),
     *       @OA\Property(property="contact_email", type="string", format="email", example="<EMAIL>"),
     *       @OA\Property(property="id_number", type="integer", example=12345678)
     *     )
     *   ),
     *
     *   @OA\Response(
     *     response=201,
     *     description="Client successfully created",
     *
     *     @OA\JsonContent(ref="#/components/schemas/Client")
     *   ),
     *
     *   @OA\Response(
     *     response=422,
     *     description="Validation error",
     *
     *     @OA\JsonContent(
     *
     *       @OA\Property(property="message", type="string", example="The given data was invalid."),
     *       @OA\Property(property="errors", type="object")
     *     )
     *   )
     * )
     */
    public function store(Request $request)
    {

        $current_user = $request->user();

        $validated = $request->validate([
            'first_name' => 'required|string',
            'last_name' => 'required|string',
            'contact_phone' => 'required|string|unique:IBMS_CLIENTS,contact_phone',
            'contact_email' => 'required|email|unique:IBMS_CLIENTS,contact_email',
            'id_number' => 'required|unique:IBMS_CLIENTS,id_number',
        ]);

        $validated['broker_id'] = $current_user->user_id;

        $client = Client::create($validated);

        return response()->json($client, 201);
    }

    /**
     * @OA\Get(
     *   path="/api/v1/clients/{id}",
     *   summary="Retrieve a specific client",
     *   description="Returns a single client owned by the authenticated broker by ID.",
     *   operationId="getClientById",
     *   tags={"Clients"},
     *   security={{"bearerAuth":{}}},
     *
     *   @OA\Parameter(
     *     name="id",
     *     in="path",
     *     description="The client ID",
     *     required=true,
     *
     *     @OA\Schema(type="integer", example=102)
     *   ),
     *
     *   @OA\Response(
     *     response=200,
     *     description="Client retrieved successfully",
     *
     *     @OA\JsonContent(ref="#/components/schemas/Client")
     *   ),
     *
     *   @OA\Response(
     *     response=404,
     *     description="Client not found",
     *
     *     @OA\JsonContent(
     *
     *       @OA\Property(property="message", type="string", example="Client not found")
     *     )
     *   )
     * )
     */
    public function show(Request $request, string $id)
    {

        $current_user = $request->user();

        $client = Client::where('client_id', $id)->where('broker_id', $current_user->user_id)->first();

        if (! $client) {
            return response()->json(['message' => 'Client not found'], 404);
        }

        return response()->json($client);
    }

    /**
     * @OA\Put(
     *   path="/api/v1/clients/{id}",
     *   summary="Update an existing client",
     *   description="Updates a client owned by the authenticated broker. Only provided fields will be updated.",
     *   operationId="updateClient",
     *   tags={"Clients"},
     *   security={{"bearerAuth":{}}},
     *
     *   @OA\Parameter(
     *     name="id",
     *     in="path",
     *     required=true,
     *     description="The ID of the client to update",
     *
     *     @OA\Schema(type="integer", example=102)
     *   ),
     *
     *   @OA\RequestBody(
     *     required=true,
     *
     *     @OA\JsonContent(
     *       type="object",
     *
     *       @OA\Property(property="first_name", type="string", example="Jane"),
     *       @OA\Property(property="last_name", type="string", example="Doe"),
     *       @OA\Property(property="contact_phone", type="string", example="+254712345678"),
     *       @OA\Property(property="contact_email", type="string", format="email", example="<EMAIL>"),
     *       @OA\Property(property="id_number", type="integer", example=12345678)
     *     )
     *   ),
     *
     *   @OA\Response(
     *     response=200,
     *     description="Client successfully updated",
     *
     *     @OA\JsonContent(ref="#/components/schemas/Client")
     *   ),
     *
     *   @OA\Response(
     *     response=404,
     *     description="Client not found",
     *
     *     @OA\JsonContent(
     *
     *       @OA\Property(property="message", type="string", example="Client not found")
     *     )
     *   ),
     *
     *   @OA\Response(
     *     response=422,
     *     description="Validation error"
     *   ),
     *   @OA\Response(
     *     response=500,
     *     description="Error updating client"
     *   )
     * )
     */
    public function update(Request $request, string $id)
    {

        $current_user = $request->user();

        $client = Client::where('client_id', $id)->where('broker_id', $current_user->user_id)->first();

        $validated = $request->validate([
            'first_name' => 'sometimes|required|string',
            'last_name' => 'sometimes|required|string',
            'contact_phone' => 'sometimes|required|string|unique:IBMS_CLIENTS,contact_phone,'.$client->client_id.',client_id',
            'id_number' => 'sometimes|required|integer|unique:IBMS_CLIENTS,id_number,'.$client->client_id.',client_id',
            'contact_email' => 'sometimes|required|email|unique:IBMS_CLIENTS,contact_email,'.$client->client_id.',client_id',
        ]);

        if ($client->update($validated)) {
            return response()->json($client);
        }

        return response()->json([
            'message' => 'Error updating client',
        ], 500);

    }

    /**
     * @OA\Delete(
     *     path="/api/v1/clients/{id}",
     *     summary="Delete a client",
     *     description="Deletes a client belonging to the authenticated broker.",
     *     operationId="destroyClient",
     *     tags={"Clients"},
     *     security={{"bearerAuth":{}}},
     *
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="ID of the client to delete",
     *         required=true,
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Client deleted successfully",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Client deleted successfully")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=404,
     *         description="Client not found",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Client not found")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=500,
     *         description="Error deleting client",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Error deleting client")
     *         )
     *     )
     * )
     */
    public function destroy(Request $request, string $id)
    {

        $current_user = $request->user();

        $client = Client::where('client_id', $id)->where('broker_id', $current_user->user_id)->first();

        if (! $client) {
            return response()->json(['message' => 'Client not found'], 404);
        }

        if ($client->delete()) {
            return response()->json(['message' => 'Client deleted successfully']);
        }

        return response()->json(['message' => ' Error deleting client deleted'], 500);
    }
}
