import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Card, PageHeader, Button, Alert } from "../../components";
import FormField from "../../components/FormFields";
import { benefitService, policyService } from "../../services";

export default function BenefitEdit() {
  const navigate = useNavigate();
  const { id } = useParams();
  const [form, setForm] = useState({
    name: "",
    description: "",
    policies: []
  });
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [alertMessage, setAlertMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [validation, setValidation] = useState({});
  const [error, setError] = useState("");
  const [policies, setPolicies] = useState([]);
  const [optionsLoading, setOptionsLoading] = useState(true);

  // Fetch benefit and policies on mount
  useEffect(() => {
    fetchBenefit();
    fetchPolicies();
  }, [id]);

  const fetchBenefit = async () => {
    try {
      setInitialLoading(true);
      const benefit = await benefitService.getBenefit(id);
      setForm({
        name: benefit.name || "",
        description: benefit.description || "",
        policies: benefit.policies || []
      });
      setError("");
    } catch (err) {
      setError("Failed to fetch benefit details");
      console.error("Error fetching benefit:", err);
    } finally {
      setInitialLoading(false);
    }
  };

  const fetchPolicies = async () => {
    setOptionsLoading(true);
    try {
      const policiesData = await policyService.getPolicies();
      setPolicies(policiesData);
    } catch (err) {
      // Optionally handle error
    } finally {
      setOptionsLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    setValidation((prev) => ({ ...prev, [name]: "" }));
  };

  const handlePolicyChange = (e) => {
    const selectedOptions = Array.from(e.target.selectedOptions, option => parseInt(option.value));
    setForm((prev) => ({ ...prev, policies: selectedOptions }));
    setValidation((prev) => ({ ...prev, policies: "" }));
  };

  const validate = () => {
    const errors = {};
    if (!form.name) errors.name = "Benefit name is required";
    if (!form.description) errors.description = "Description is required";
    if (!form.policies || form.policies.length === 0) errors.policies = "At least one policy is required";
    setValidation(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validate()) return;
    try {
      setLoading(true);
      setError("");
      
      const benefitData = {
        name: form.name,
        description: form.description,
        policies: form.policies
      };
      
      await benefitService.updateBenefit(id, benefitData);
      setShowAlert(true);
      setAlertType("success");
      setAlertMessage("Benefit updated successfully!");
      setTimeout(() => {
        navigate("/admin/benefits");
      }, 1500);
    } catch (err) {
      setError("Failed to update benefit");
      console.error("Error updating benefit:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate("/admin/benefits");
  };

  if (initialLoading || optionsLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading benefit details...</p>
        </div>
      </div>
    );
  }

  if (error && !initialLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
          <PageHeader
            title="Edit Benefit"
            subtitle="Update benefit information"
            breadcrumbs={[
              { label: "Dashboard", href: "/admin/dashboard" },
              { label: "Benefits", href: "/admin/benefits" },
              { label: "Edit Benefit" },
            ]}
            actions={[
              {
                label: "Back to Benefits",
                variant: "outline",
                onClick: handleCancel,
              }
            ]}
          />
          <Alert
            type="error"
            title="Error"
            message={error}
            onClose={() => setError("")}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
        <PageHeader
          title="Edit Benefit"
          subtitle="Update benefit information"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Benefits", href: "/admin/benefits" },
            { label: "Edit Benefit" },
          ]}
          actions={[
            {
              label: "Back to Benefits",
              variant: "outline",
              onClick: handleCancel,
            }
          ]}
        />
        <Card title="Edit Benefit">
          {showAlert && (
            <Alert
              type={alertType}
              title={alertType === "success" ? "Success" : "Error"}
              message={alertMessage}
              onClose={() => setShowAlert(false)}
            />
          )}
          {error && (
            <Alert
              type="error"
              title="Error"
              message={error}
              onClose={() => setError("")}
            />
          )}
          <form className="space-y-6" onSubmit={handleSubmit}>
            <FormField
              label="Benefit Name"
              name="name"
              value={form.name}
              onChange={handleChange}
              placeholder="Enter benefit name"
              isRequired={true}
              validationError={validation.name}
            />
            <FormField
              label="Description"
              name="description"
              type="textarea"
              value={form.description}
              onChange={handleChange}
              placeholder="Enter benefit description"
              isRequired={true}
              validationError={validation.description}
            />
            <div>
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Associated Policies <span className="text-red-500">*</span>
              </label>
              <select
                multiple
                value={form.policies}
                onChange={handlePolicyChange}
                className={`shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  validation.policies ? 'border-red-500' : 'border-gray-300'
                }`}
                size="4"
              >
                {policies.map((policy) => (
                  <option key={policy.id} value={policy.id}>
                    {policy.name}
                  </option>
                ))}
              </select>
              <p className="text-gray-600 text-xs italic mt-1">
                Hold Ctrl (or Cmd on Mac) to select multiple policies
              </p>
              {validation.policies && (
                <p className="text-red-500 text-xs italic mt-1">{validation.policies}</p>
              )}
            </div>
            <div className="flex justify-end gap-2">
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleCancel}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                variant="primary" 
                loading={loading}
              >
                {loading ? "Updating..." : "Update Benefit"}
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
} 