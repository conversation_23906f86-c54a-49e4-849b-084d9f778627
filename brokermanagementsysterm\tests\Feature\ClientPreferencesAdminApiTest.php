<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\ClientPreferences;
use App\Models\RoleSetupService;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ClientPreferencesAdminApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        RoleSetupService::createRoles();
        // Assuming roles are handled with a `broker()` state
        $this->admin = User::factory()->admin()->create();
        $this->broker = User::factory()->admin()->create();
        $this->actingAs($this->admin, 'sanctum');

        $this->client = Client::factory()->create(['broker_id' => $this->broker->user_id]);
        $this->vehicle = Vehicle::factory()->create(['client_id' => $this->client->client_id]);

    }

    public function test_admin_can_store_preferences_for_client()
    {
        $payload = [
            'preferred_insurers' => ['CIC'],
            'preferred_cover_types' => ['comprehensive'],
            'preferred_channel' => 'email',
            'renewal_reminder_enabled' => true,
            'budget_min' => 5000,
            'budget_max' => 15000,
        ];

        $response = $this->postJson("/api/v1/preferences/{$this->client->client_id}/{$this->vehicle->vehicle_id}", $payload);

        $response->assertStatus(201)
            ->assertJsonFragment(['message' => 'Preferences saved successfully']);

        $this->assertDatabaseHas('IBMS_CLIENT_PREFERENCES', [
            'vehicle_id' => $this->vehicle->vehicle_id,
            'preferred_channel' => 'email',
        ]);
    }

    public function test_admin_can_view_vehicle_preferences()
    {
        $prefs = ClientPreferences::factory()->create(['vehicle_id' => $this->vehicle->vehicle_id]);

        $response = $this->getJson("/api/v1/preferences/vehicle/{$this->vehicle->vehicle_id}");

        $response->assertStatus(200)
            ->assertJsonFragment(['id' => $prefs->id]);
    }

    public function test_admin_can_view_client_vehicle_preferences()
    {

        // Create preferences for that vehicle
        $prefs = ClientPreferences::factory()->create([
            'vehicle_id' => $this->vehicle->vehicle_id,
        ]);

        // Call the API endpoint
        $response = $this->getJson("/api/v1/preferences/client/{$this->client->client_id}");

        // Assert status and payload
        $response->assertStatus(200)
            ->assertJsonFragment(['id' => $prefs->id]);
    }

    public function test_admin_can_update_preferences()
    {
        $prefs = ClientPreferences::factory()->create(['vehicle_id' => $this->vehicle->vehicle_id]);

        $response = $this->putJson("/api/v1/preferences/{$prefs->id}", [
            'preferred_channel' => 'whatsapp',
        ]);

        $response->assertStatus(200)
            ->assertJsonFragment(['preferred_channel' => 'whatsapp']);
    }

    public function test_admin_can_delete_preferences()
    {
        $prefs = ClientPreferences::factory()->create(['vehicle_id' => $this->vehicle->vehicle_id]);

        $response = $this->deleteJson("/api/v1/preferences/{$prefs->id}");

        $response->assertStatus(200)
            ->assertJsonFragment(['message' => 'Preference deleted successfully']);

        $this->assertDatabaseMissing('IBMS_CLIENT_PREFERENCES', ['id' => $prefs->id]);
    }
}
