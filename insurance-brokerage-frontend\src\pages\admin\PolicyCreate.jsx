import React, { useState, useEffect } from "react";
import { <PERSON>, PageHeader, <PERSON>ton, Alert } from "../../components";
import FormField from "../../components/FormFields";
import { useNavigate, useLocation } from "react-router-dom";
import { policyService, insuranceService, coverTypeService } from "../../services";
import { benefitService } from "../../services/benefitService";

export default function PolicyCreate() {
  const [form, setForm] = useState({
    name: "",
    product: "Motor Insurance",
    coverTypeId: "",
    insurerId: "",
    basePremium: "",
    sumInsured: "",
    commissionRate: "",
    vehicleTypes: [],
    benefits: "",
    status: "Active"
  });
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [alertMessage, setAlertMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [validation, setValidation] = useState({});
  const [insurers, setInsurers] = useState([]);
  const [coverTypes, setCoverTypes] = useState([]);
  const [benefits, setBenefits] = useState([]);
  const [optionsLoading, setOptionsLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    async function fetchOptions() {
      setOptionsLoading(true);
      try {
        const [insurersData, coverTypesData, benefitsData] = await Promise.all([
          insuranceService.getCompanies(),
          coverTypeService.getCoverTypes(),
          benefitService.getBenefits(),
        ]);
        setInsurers(insurersData);
        setCoverTypes(coverTypesData);
        setBenefits(benefitsData);
        // Preselect insurer if ?insurer=ID is in the URL
        const params = new URLSearchParams(location.search);
        const insurerId = params.get("insurer");
        if (insurerId) {
          setForm((prev) => ({ ...prev, insurerId }));
        }
      } catch (err) {
        // Optionally handle error
      } finally {
        setOptionsLoading(false);
      }
    }
    fetchOptions();
  }, [location.search]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    if (name === "vehicleTypes") {
      setForm((prev) => {
        const newTypes = checked
          ? [...prev.vehicleTypes, value]
          : prev.vehicleTypes.filter((t) => t !== value);
        return { ...prev, vehicleTypes: newTypes };
      });
    } else if (name === "benefits") {
      setForm((prev) => {
        const newBenefits = checked
          ? [...(prev.benefits || []), value]
          : (prev.benefits || []).filter((b) => b !== value);
        return { ...prev, benefits: newBenefits };
      });
    } else {
      setForm((prev) => ({ ...prev, [name]: value }));
      setValidation((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const validate = () => {
    const errors = {};
    if (!form.name) errors.name = "Policy name is required";
    if (!form.coverTypeId) errors.coverTypeId = "Cover type is required";
    if (!form.insurerId) errors.insurerId = "Insurer is required";
    if (!form.basePremium) errors.basePremium = "Base premium is required";
    if (!form.sumInsured) errors.sumInsured = "Sum insured is required";
    if (!form.commissionRate) errors.commissionRate = "Commission rate is required";
    if (form.vehicleTypes.length === 0) errors.vehicleTypes = "Select at least one vehicle type";
    if (!form.benefits || form.benefits.length === 0) errors.benefits = "Select at least one benefit";
    setValidation(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validate()) return;
    setLoading(true);
    try {
      await policyService.createPolicy({
        name: form.name,
        product: form.product,
        coverTypeId: Number(form.coverTypeId),
        insurerId: Number(form.insurerId),
        basePremium: Number(form.basePremium),
        sumInsured: Number(form.sumInsured),
        commissionRate: Number(form.commissionRate),
        vehicleTypes: form.vehicleTypes,
        benefits: form.benefits,
        status: form.status,
        createdAt: new Date().toISOString(),
      });
      setShowAlert(true);
      setAlertType("success");
      setAlertMessage("Policy created successfully!");
      setForm({
        name: "",
        product: "Motor Insurance",
        coverTypeId: "",
        insurerId: "",
        basePremium: "",
        sumInsured: "",
        commissionRate: "",
        vehicleTypes: [],
        benefits: "",
        status: "Active"
      });
      setTimeout(() => {
        navigate("/admin/policies");
      }, 1200);
    } catch (err) {
      setShowAlert(true);
      setAlertType("error");
      setAlertMessage("Failed to create policy");
    } finally {
      setLoading(false);
    }
  };

  if (optionsLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading options...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="flex-1 flex flex-col justify-center w-full mx-auto p-6 space-y-6">
        <PageHeader
          title="Add Policy"
          subtitle="Create a new insurance policy"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Insurers", href: "/admin/insurers" },
            ...(form.insurerId && insurers.length > 0
              ? [{
                  label: (insurers.find(i => String(i.id) === String(form.insurerId)) || {}).name || "Insurer",
                  href: "/admin/insurers/" + form.insurerId
                }]
              : []),
            { label: "Add Policy" },
          ]}
        />
        <Card title="New Policy">
          {showAlert && (
            <Alert
              type={alertType}
              title={alertType === "success" ? "Success" : "Error"}
              message={alertMessage}
              onClose={() => setShowAlert(false)}
            />
          )}
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-4">
                <FormField
                  label="Policy Name"
                  name="name"
                  value={form.name}
                  onChange={handleChange}
                  placeholder="Enter policy name"
                  isRequired={true}
                  validationError={validation.name}
                />
                <FormField
                  label="Product"
                  name="product"
                  value={form.product}
                  disabled={true}
                />
                <FormField
                  label="Cover Type"
                  name="coverTypeId"
                  type="select"
                  value={form.coverTypeId}
                  onChange={handleChange}
                  options={coverTypes.filter(ct => ct.category === "Motor").map((ct) => ({ value: ct.id, label: ct.name }))}
                  isRequired={true}
                  validationError={validation.coverTypeId}
                />
                {form.insurerId && insurers.length > 0 && (new URLSearchParams(location.search).get("insurer")) ? (
                  <FormField
                    label="Insurer"
                    name="insurerName"
                    value={(insurers.find(i => String(i.id) === String(form.insurerId)) || {}).name || ""}
                    disabled={true}
                  />
                ) : (
                  <FormField
                    label="Insurer"
                    name="insurerId"
                    type="select"
                    value={form.insurerId}
                    onChange={handleChange}
                    options={insurers.map((insurer) => ({ value: insurer.id, label: insurer.name }))}
                    isRequired={true}
                    validationError={validation.insurerId}
                  />
                )}
              </div>
              <div className="space-y-4">
                <FormField
                  label="Base Premium"
                  name="basePremium"
                  type="number"
                  value={form.basePremium}
                  onChange={handleChange}
                  placeholder="Enter base premium"
                  isRequired={true}
                  validationError={validation.basePremium}
                />
                <FormField
                  label="Sum Insured"
                  name="sumInsured"
                  type="number"
                  value={form.sumInsured}
                  onChange={handleChange}
                  placeholder="Enter sum insured"
                  isRequired={true}
                  validationError={validation.sumInsured}
                />
                <FormField
                  label="Commission Rate (%)"
                  name="commissionRate"
                  type="number"
                  value={form.commissionRate}
                  onChange={handleChange}
                  placeholder="Enter commission rate"
                  isRequired={true}
                  validationError={validation.commissionRate}
                />
                <FormField 
                  label="Status"
                  name="status"
                  type="select"
                  value={form.status}
                  onChange={handleChange}
                  options={[
                    { value: "Active", label: "Active" },
                    { value: "Inactive", label: "Inactive" },
                  ]}
                  isRequired={true}
                />
              </div>
              <div className="space-y-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Vehicle Type</label>
                  <div className="flex gap-4 flex-wrap">
                    {['Private', 'Commercial', 'Special', 'Motorcycle'].map((type) => (
                      <label key={type} className="inline-flex items-center">
                        <input
                          type="checkbox"
                          name="vehicleTypes"
                          value={type}
                          checked={form.vehicleTypes.includes(type)}
                          onChange={handleChange}
                          className="form-checkbox h-4 w-4 text-blue-600"
                        />
                        <span className="ml-2 text-sm text-gray-700">{type}</span>
                      </label>
                    ))}
                  </div>
                  {validation.vehicleTypes && (
                    <div className="text-red-500 text-xs mt-1">{validation.vehicleTypes}</div>
                  )}
                <div className="flex-1 flex flex-col justify-between">
                  <label className="block text-sm font-medium text-gray-700 mb-1">Benefits</label>
                  <div className="flex flex-wrap gap-4 min-h-[200px] max-h-[217px] overflow-y-auto border rounded p-2 bg-gray-50">
                    {benefits.map((benefit) => (
                      <label key={benefit.id} className="inline-flex items-center w-1/2 md:w-full">
                        <input
                          type="checkbox"
                          name="benefits"
                          value={String(benefit.id)}
                          checked={Array.isArray(form.benefits) && form.benefits.includes(String(benefit.id))}
                          onChange={handleChange}
                          className="form-checkbox h-4 w-4 text-blue-600"
                        />
                        <span className="ml-2 text-sm text-gray-700">{benefit.name}</span>
                      </label>
                    ))}
                  </div>
                  {validation.benefits && (
                    <div className="text-red-500 text-xs mt-1">{validation.benefits}</div>
                  )}
                </div>
              </div>
            </div>
            <div className="flex justify-end gap-4 mt-8">
              <Button type="submit" variant="primary" loading={loading}>
                Create Policy
              </Button>
              <Button type="button" variant="danger" onClick={() => navigate('/admin/policies')}>
                Cancel
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
}
