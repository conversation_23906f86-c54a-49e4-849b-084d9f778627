<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Insurance extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    protected $table = 'IBMS_INSURANCES';

    protected $primaryKey = 'insurance_id';

    protected $keyType = 'string';

    protected $dates = ['deleted_at'];

    public $incrementing = false;

    protected $fillable = [
        'insurance_id',
        'name',
        'company_code',
        'email',
        'phone_number',
        'address',
        'logo_path',
        'is_active',
    ];

    /**
     * The "booted" method of the model.
     */
    protected static function booted()
    {
        static::creating(function ($insurance) {
            if (empty($insurance->company_code)) {
                $insurance->company_code = $insurance->generateCompanyCode();
            }
        });
    }

    /**
     * Generate a unique company code from the company name
     */
    public function generateCompanyCode(): string
    {
        $name = $this->name;
        $code = '';

        // Remove common suffixes and split into words
        $cleanName = preg_replace('/\b(insurance|co|inc|llc|group|holdings)\b/i', '', $name);
        $words = preg_split('/\s+/', trim($cleanName));

        // Take first letter of each word (up to 3 words)
        foreach (array_slice($words, 0, 3) as $word) {
            if (! empty($word)) {
                $code .= strtoupper(substr($word, 0, 1));
            }
        }

        // If code is less than 3 characters, pad with random letters
        while (strlen($code) < 3) {
            $code .= strtoupper(Str::random(1));
        }

        // Ensure code is unique
        $originalCode = $code;
        $counter = 1;

        while (static::where('company_code', $code)->exists()) {
            $code = $originalCode.$counter;
            $counter++;
        }

        return $code;
    }

    public function insuranceCategoryProducts()
    {
        return $this->hasMany(InsuranceCategoryProduct::class, 'insurance_id');
    }

    public function policies()
    {
        return $this->hasManyThrough(
            InsuranceProductPolicy::class,
            InsuranceCategoryProduct::class,
            'insurance_id',
            'insurance_category_product_id',
            'insurance_id',
            'insurance_category_product_id'
        );
    }
}
