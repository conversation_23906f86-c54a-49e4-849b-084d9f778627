<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

/**
 * @OA\Schema(
 *     schema="Client",
 *     required={"first_name", "last_name", "contact_email", "contact_phone", "id_number", "broker_id"},
 *
 *     @OA\Property(property="client_id", type="integer", example=1),
 *     @OA\Property(property="first_name", type="string", example="John"),
 *     @OA\Property(property="last_name", type="string", example="Doe"),
 *     @OA\Property(property="contact_email", type="string", format="email", example="<EMAIL>"),
 *     @OA\Property(property="contact_phone", type="string", example="+254712345678"),
 *     @OA\Property(property="id_number", type="integer", example=12345678),
 *     @OA\Property(property="broker_id", type="integer", example=2),
 *     @OA\Property(property="created_at", type="string", format="date-time", example="2025-06-27T10:00:00Z"),
 *     @OA\Property(property="updated_at", type="string", format="date-time", example="2025-06-27T10:00:00Z")
 * )
 */
class Client extends Model
{
    //
    use HasFactory;

    protected $table = 'IBMS_CLIENTS';

    protected $primaryKey = 'client_id'; // ✅ Add this if your table uses a custom PK

    public $incrementing = false;

    protected $keyType = 'string';

    protected $fillable = [
        'first_name',
        'last_name',
        'contact_phone',
        'id_number',
        'broker_id',
        'contact_email',
        'vehicle_id',
    ];

    public function broker()
    {
        return $this->belongsTo(User::class, 'broker_id');
    }

    public function vehicles()
    {
        return $this->hasMany(Vehicle::class, 'client_id', 'client_id');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (! $model->getKey()) {
                $model->{$model->getKeyName()} = (string) Str::uuid();
            }
        });
    }
}
