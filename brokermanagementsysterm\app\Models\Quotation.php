<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Quotation extends Model
{
    use HasFactory, HasUuids;

    protected $primaryKey = 'quotation_id';

    protected $keyType = 'string';

    public $incrementing = false;

    protected $table = 'IBMS_QUOTATIONS';

    protected $fillable = [
        'quotation_id',
        'client_id',
        'vehicle_id',
        'insurance_category_product_id', // <-- ✅ recommended
        'total_price',
        'coverage_details',
        'valid_until',
        'status',
        'accepted_at',
    ];

    protected $casts = [
        'total_price' => 'float',
        'valid_until' => 'datetime',
        'coverage_details' => 'array',
        'accepted_at' => 'datetime',
    ];

    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class, 'vehicle_id');
    }

    public function insuranceCategoryProduct()
    {
        return $this->belongsTo(InsuranceCategoryProduct::class, 'insurance_category_product_id');
    }

    public function insurer()
    {
        return $this->insuranceCategoryProduct?->insurance(); // optional shortcut
    }
}
