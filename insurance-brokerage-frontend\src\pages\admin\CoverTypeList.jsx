import React, { useState, useEffect } from "react";
import { Card, PageHeader, Button, Alert, DataTable, SearchBar, Modal } from "../../components";
import { useNavigate } from "react-router-dom";
import { coverTypeService, insuranceService } from "../../services";

export default function CoverTypeList() {
  const [coverTypes, setCoverTypes] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCoverTypes, setFilteredCoverTypes] = useState([]);
  const [selectedInsurer, setSelectedInsurer] = useState("all");
  const [insurers, setInsurers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [coverTypeToDelete, setCoverTypeToDelete] = useState(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [alertMessage, setAlertMessage] = useState("");
  const navigate = useNavigate();

  const showSuccessAlert = (message) => {
    setAlertMessage(message);
    setAlertType("success");
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        const [coverTypesData, insurersData] = await Promise.all([
          coverTypeService.getCoverTypes(),
          insuranceService.getCompanies(),
        ]);
        // Only motor cover types, join with insurer
        const motorCoverTypes = coverTypesData
          .filter((ct) => ct.category === "Motor")
          .map((ct) => {
            const insurer = insurersData.find((ins) => String(ins.id) === String(ct.insurerId)) || insurersData[0];
            return {
              ...ct,
              insurer: insurer ? insurer.name : "Unknown",
              insurerId: insurer ? insurer.id : null,
            };
          });
        setCoverTypes(motorCoverTypes);
        setFilteredCoverTypes(motorCoverTypes);
        setInsurers(insurersData);
      } catch (err) {
        // Optionally handle error
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  useEffect(() => {
    let filtered = coverTypes;
    if (selectedInsurer !== "all") {
      filtered = filtered.filter((ct) => String(ct.insurerId) === String(selectedInsurer));
    }
    if (searchTerm) {
      filtered = filtered.filter(
        (ct) =>
          ct.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          ct.insurer.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    setFilteredCoverTypes(filtered);
  }, [searchTerm, coverTypes, selectedInsurer]);

  const columns = [
    {
      key: "name",
      label: "Cover Type Name",
      sortable: true,
      render: (value) => (
        <span className="font-semibold text-gray-800">{value}</span>
      ),
    },
    {
      key: "insurer",
      label: "Insurer",
      sortable: true,
      render: (value) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {value}
        </span>
      ),
    },
    {
      key: "description",
      label: "Description",
      sortable: false,
      render: (value) => (
        <span className="text-gray-600 text-sm">{value}</span>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      render: (_, item) => (
        <div className="flex gap-2">
          <Button size="small" variant="outline" onClick={() => navigate(`/admin/cover-types/${item.id}`)}>View</Button>
          <Button size="small" variant="primary" onClick={() => navigate(`/admin/cover-types/${item.id}/edit`)}>Edit</Button>
          <Button size="small" variant="danger" onClick={() => { setCoverTypeToDelete(item); setShowDeleteModal(true); }}>Delete</Button>
        </div>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full mx-auto p-6 space-y-6">
        <PageHeader
          title="Cover Types"
          subtitle="Manage all motor insurance cover types offered by insurers"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Cover Types" },
          ]}
          actions={[
            {
              label: "Add Cover Type",
              variant: "primary",
              onClick: () => window.location.href = "/admin/cover-types/create",
              icon: "+",
            },
          ]}
        />
        {showAlert && (
            <Alert
              type={alertType}
              title={alertType === "success" ? "Success" : "Error"}
              message={alertMessage}
              onClose={() => setShowAlert(false)}
            />
          )}
        <Card title="Cover Types" subtitle={`${filteredCoverTypes.length} found`}>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Insurer</label>
              <select
                className="w-56 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-700"
                value={selectedInsurer}
                onChange={(e) => setSelectedInsurer(e.target.value)}
              >
                <option value="all">All Insurers</option>
                {insurers.map((insurer) => (
                  <option key={insurer.id} value={insurer.id}>{insurer.name}</option>
                ))}
              </select>
            </div>
            <div className="flex-1 mt-5">
              <SearchBar
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
                placeholder="Search by Cover Type or Insurer..."
              />
            </div>
          </div>
          {loading ? (
            <div className="text-center py-10 text-gray-500">Loading cover types...</div>
          ) : (
            <DataTable
              data={filteredCoverTypes}
              columns={columns}
              searchable={false}
              pagination={true}
              itemsPerPage={10}
            />
          )}
          <Modal
            isOpen={showDeleteModal}
            onClose={() => setShowDeleteModal(false)}
            title="Delete Cover Type"
            size="small"
          >
            <div className="space-y-4">
              <p>Are you sure you want to delete the <strong>Cover Type {coverTypeToDelete?.name}</strong>?</p>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowDeleteModal(false)}>Cancel</Button>
                <Button variant="danger" onClick={() => { setShowDeleteModal(false); showSuccessAlert(`Cover type ${coverTypeToDelete?.name} deleted successfully!`); }}>Delete</Button>
              </div>
            </div>
          </Modal>
        </Card>
      </div>
    </div>
  );
}
