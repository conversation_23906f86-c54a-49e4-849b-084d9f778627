<?php

namespace App\Models;

class RolePermissionsService implements PermissionsInterface
{
    public function hasPermission(Role $role, int $permission): bool
    {
        return ($role->role_permissions & $permission) === $permission;
    }

    public function addPermission(Role $role, int $permission): bool
    {
        if ($this->hasPermission($role, $permission)) {
            return true;
        }

        $role->role_permissions |= $permission;

        return $role->save();
    }

    public function removePermission(Role $role, int $permission): bool
    {
        if (! $this->hasPermission($role, $permission)) {
            return false;
        }

        $role->role_permissions &= ~$permission;

        return $role->save();
    }
}
