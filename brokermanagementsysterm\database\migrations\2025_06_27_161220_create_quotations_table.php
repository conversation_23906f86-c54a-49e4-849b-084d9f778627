<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('IBMS_QUOTATIONS', function (Blueprint $table) {
            $table->uuid('quotation_id')->primary();

            $table->foreignUuid('client_id')
                ->constrained('IBMS_CLIENTS', 'client_id')
                ->onDelete('restrict');

            $table->foreignUuid('vehicle_id')
                ->constrained('IBMS_VEHICLES', 'vehicle_id')
                ->onDelete('restrict');

            $table->foreignUuid('insurance_category_product_id')
                ->constrained('IBMS_INSURANCE_CATEGORY_PRODUCTS', 'insurance_category_product_id')
                ->onDelete('restrict');

            $table->decimal('total_price', 12, 2);
            $table->json('coverage_details')->nullable(); // you can store dynamic coverage info
            $table->enum('status', ['pending', 'approved', 'declined'])->default('pending');
            $table->timestamp('valid_until')->nullable();
            $table->timestamp('accepted_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('IBMS_QUOTATIONS');
    }
};
