<?php

namespace Tests\Unit;

// ✅ Use Laravel's TestCase
use App\Models\RoleSetupService;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserModelTest extends TestCase
{
    use RefreshDatabase;

    public function test_usermodel(): void
    {

        $roleSetup = new RoleSetupService;
        $roleSetup::createRoles();
        $user = User::factory()->create();

        $this->assertFalse($user->email_verified);
        $this->assertNotNull($user->role_id);
        $this->assertNotEmpty($user->last_name);
        $this->assertNotEmpty($user->first_name);
        $this->assertNotEmpty($user->email);
        $this->assertNotEmpty($user->phone_number);
    }
}
