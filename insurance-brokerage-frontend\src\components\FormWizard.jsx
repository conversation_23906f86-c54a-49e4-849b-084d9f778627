import React, { useState , useEffect } from "react";
import Button from "./Button";
import Card from "./Card";
import { formWizardStyles } from "../Styles/uiTheme";

const FormWizard = ({ steps, onComplete, onCancel, className = "", initialData,  externalErrors = {} }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});

  const currentStepData = steps[currentStep];
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === steps.length - 1;

  useEffect(() => {
    if (initialData && Object.keys(initialData).length > 0) {
      setFormData(initialData);
    }
  }, [initialData]);

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  const validateCurrentStep = () => {
    const currentErrors = {};

    if (currentStepData.validation) {
      currentStepData.validation(formData, currentErrors);
    }

    setErrors(currentErrors);
    return Object.keys(currentErrors).length === 0;
  };

  useEffect(() => {
    if (Object.keys(externalErrors).length > 0) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        ...externalErrors
      }));
    }
  }, [externalErrors]);

  const handleNext = () => {
    if (validateCurrentStep()) {
      if (isLastStep) {
        onComplete(formData);
      } else {
        setCurrentStep((prev) => prev + 1);
      }
    }
  };

  const handlePrevious = () => {
    setCurrentStep((prev) => prev - 1);
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  const getStepStatus = (stepIndex) => {
    if (stepIndex < currentStep) return "completed";
    if (stepIndex === currentStep) return "current";
    return "upcoming";
  };

  const stepStyles = formWizardStyles.step;

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const status = getStepStatus(index);
            const styles = stepStyles[status];

            return (
              <div key={index} className="flex items-center">
                <div className="flex items-center">
                  <div
                    className={`
                    flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm font-medium
                    ${styles.container}
                  `}
                  >
                    {status === "completed" ? "✓" : index + 1}
                  </div>
                  <div className="ml-3">
                    <p className={`text-sm font-medium ${styles.label}`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-400">{step.description}</p>
                  </div>
                </div>

                {index < steps.length - 1 && (
                  <div
                    className={`
                    flex-1 h-0.5 mx-4
                    ${styles.connector}
                  `}
                  />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Form Content */}
      <Card
        title={currentStepData.title}
        subtitle={currentStepData.description}
      >
        <div className="space-y-6">
          {currentStepData.fields.map((field) => (
            <div key={field.name}>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {field.label}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </label>

              {field.type === "select" ? (
                <select
                  value={formData[field.name] || ""}
                  onChange={(e) =>
                    handleInputChange(field.name, e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select {field.label}</option>
                  {field.options.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              ) : field.type === "textarea" ? (
                <textarea
                  value={formData[field.name] || ""}
                  onChange={(e) =>
                    handleInputChange(field.name, e.target.value)
                  }
                  rows={field.rows || 3}
                  placeholder={field.placeholder}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              ) : (
                <input
                  type={field.type || "text"}
                  value={formData[field.name] || ""}
                  onChange={(e) =>
                    handleInputChange(field.name, e.target.value)
                  }
                  placeholder={field.placeholder}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              )}

              {errors[field.name] && (
                <p className="mt-1 text-sm text-red-600">
                  {errors[field.name]}
                </p>
              )}

              {field.helpText && (
                <p className="mt-1 text-sm text-gray-500">{field.helpText}</p>
              )}
            </div>
          ))}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
          <div className="flex space-x-3">
            {!isFirstStep && (
              <Button variant="outline" onClick={handlePrevious}>
                Previous
              </Button>
            )}
            <Button variant="ghost" onClick={handleCancel}>
              Cancel
            </Button>
          </div>

          <Button variant="primary" onClick={handleNext} loading={false}>
            {isLastStep ? "Complete Registration" : "Next"}
          </Button>
        </div>
      </Card>
    </div>
  );
};

export default FormWizard;
