<?php

namespace Tests\Unit;

use App\Models\Category;
use App\Models\CategoryProduct;
use App\Models\Client;
use App\Models\RoleSetupService;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VehicleModelTest extends TestCase
{
    /**
     * A basic unit test example.
     */
    use RefreshDatabase;

    public function test_example(): void
    {
        $this->assertTrue(true);
    }

    public function test_vehicle_create(): void
    {
        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();
        $testProduct = Category::factory()->create();
        $testCover = CategoryProduct::factory()->create(['category_id' => $testProduct]);

        $client = Client::factory()->create([
            'broker_id' => $testBroker->user_id,
        ]);
        $vehicle = Vehicle::factory()->create([
            'client_id' => $client,
            'cover_type' => $testCover,
        ]);

        $this->assertNotNull($vehicle);
        $this->assertEquals($vehicle->vehicle_id, 1);
    }

    public function test_vehicle_methods(): void
    {

        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();
        $testProduct = Category::factory()->create();
        $testCover = CategoryProduct::factory()->create(['category_id' => $testProduct]);

        $client = Client::factory()->create([
            'broker_id' => $testBroker->user_id,
        ]);
        $vehicle = Vehicle::factory()->create([
            'client_id' => $client,
            'cover_type' => $testCover,
        ]);

        $this->assertNotNull($vehicle);

        $this->assertEquals($vehicle->client->client_id, $client->client_id);

    }
}
