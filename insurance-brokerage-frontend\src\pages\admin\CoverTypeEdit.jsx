import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Card, PageHeader, Button, Alert } from "../../components";
import FormField from "../../components/FormFields";
import { coverTypeService, insuranceService } from "../../services";

export default function CoverTypeEdit() {
  const navigate = useNavigate();
  const { id } = useParams();
  const [form, setForm] = useState({
    name: "",
    description: "",
    insurerId: ""
  });
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [alertMessage, setAlertMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [validation, setValidation] = useState({});
  const [error, setError] = useState("");
  const [insurers, setInsurers] = useState([]);

  // Fetch cover type and insurers on mount
  useEffect(() => {
    fetchCoverType();
    fetchInsurers();
  }, [id]);

  const fetchCoverType = async () => {
    try {
      setInitialLoading(true);
      const coverType = await coverTypeService.getCoverType(id);
      setForm({
        name: coverType.name || "",
        description: coverType.description || "",
        insurerId: coverType.insurerId || ""
      });
      setError("");
    } catch (err) {
      setError("Failed to fetch cover type details");
      console.error("Error fetching cover type:", err);
    } finally {
      setInitialLoading(false);
    }
  };

  const fetchInsurers = async () => {
    try {
      const insurersData = await insuranceService.getCompanies();
      setInsurers(insurersData);
    } catch (err) {
      // Optionally handle error
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    setValidation((prev) => ({ ...prev, [name]: "" }));
  };

  const validate = () => {
    const errors = {};
    if (!form.name) errors.name = "Cover type name is required";
    if (!form.description) errors.description = "Description is required";
    if (!form.insurerId) errors.insurerId = "Insurer is required";
    setValidation(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validate()) return;
    try {
      setLoading(true);
      setError("");
      const coverTypeData = {
        name: form.name,
        description: form.description,
        insurerId: parseInt(form.insurerId),
        category: "Motor",
        status: "Active"
      };
      await coverTypeService.updateCoverType(id, coverTypeData);
      setShowAlert(true);
      setAlertType("success");
      setAlertMessage("Cover type updated successfully!");
      setTimeout(() => {
        navigate("/admin/cover-types");
      }, 1500);
    } catch (err) {
      setError("Failed to update cover type");
      console.error("Error updating cover type:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    navigate("/admin/cover-types");
  };

  if (initialLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading cover type details...</p>
        </div>
      </div>
    );
  }

  if (error && !initialLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
          <PageHeader
            title="Edit Cover Type"
            subtitle="Update motor insurance cover type information"
            breadcrumbs={[
              { label: "Dashboard", href: "/admin/dashboard" },
              { label: "Cover Types", href: "/admin/cover-types" },
              { label: "Edit Cover Type" },
            ]}
            actions={[
              {
                label: "Back to Cover Types",
                variant: "outline",
                onClick: handleCancel,
              }
            ]}
          />
          <Alert
            type="error"
            title="Error"
            message={error}
            onClose={() => setError("")}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
        <PageHeader
          title="Edit Cover Type"
          subtitle="Update motor insurance cover type information"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Cover Types", href: "/admin/cover-types" },
            { label: "Edit Cover Type" },
          ]}
          actions={[
            {
              label: "Back to Cover Types",
              variant: "outline",
              onClick: handleCancel,
            }
          ]}
        />
        <Card title="Edit Cover Type">
          {showAlert && (
            <Alert
              type={alertType}
              title={alertType === "success" ? "Success" : "Error"}
              message={alertMessage}
              onClose={() => setShowAlert(false)}
            />
          )}
          {error && (
            <Alert
              type="error"
              title="Error"
              message={error}
              onClose={() => setError("")}
            />
          )}
          <form className="space-y-6" onSubmit={handleSubmit}>
            <FormField
              label="Cover Type Name"
              name="name"
              value={form.name}
              onChange={handleChange}
              placeholder="Enter cover type name"
              isRequired={true}
              validationError={validation.name}
            />
            <FormField
              label="Description"
              name="description"
              type="textarea"
              value={form.description}
              onChange={handleChange}
              placeholder="Enter description"
              isRequired={true}
              validationError={validation.description}
            />
            <FormField
              label="Insurer"
              name="insurerId"
              type="select"
              value={form.insurerId}
              onChange={handleChange}
              options={insurers.map((insurer) => ({ value: insurer.id, label: insurer.name }))}
              isRequired={true}
              validationError={validation.insurerId}
            />
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button type="submit" variant="primary" loading={loading}>
                Save Changes
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
} 