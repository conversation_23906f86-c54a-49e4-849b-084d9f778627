<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\CategoryProduct;
use App\Models\Insurance;
use App\Models\InsuranceCategoryProduct;
use App\Models\InsuranceProductPolicy;
use App\Models\PolicyBenefit;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

// class InsuranceModelsTest extends TestCase
// {
//     use RefreshDatabase;

//     #[Test]
//     public function it_can_create_an_insurance_company()
//     {
//         $insurance = Insurance::create([
//             'name' => 'Test Insurance',
//             'email' => '<EMAIL>',
//             'phone_number' => '**********',
//             'is_active' => true,
//         ]);

//         $this->assertDatabaseHas('IBMS_INSURANCES', [
//             'name' => 'Test Insurance',
//             'email' => '<EMAIL>',
//             'is_active' => true,
//         ]);
//     }

//     #[Test]
//     public function it_can_create_a_category()
//     {
//         $category = Category::create([
//             'category_name' => 'HEALTH_INSURANCE',
//             'category_description' => 'Health insurance products',
//         ]);

//         $this->assertDatabaseHas('IBMS_CATEGORIES', [
//             'category_name' => 'HEALTH_INSURANCE',
//         ]);
//     }

//     #[Test]
//     public function it_can_create_a_category_product()
//     {
//         $category = Category::factory()->create();

//         $product = CategoryProduct::create([
//             'category_product_name' => 'Comprehensive Health Plan',
//             'category_product_description' => 'Covers all health needs',
//             'category_id' => $category->category_id,
//         ]);

//         $this->assertDatabaseHas('IBMS_CATEGORY_PRODUCTS', [
//             'category_product_name' => 'Comprehensive Health Plan',
//         ]);
//     }

//     #[Test]
//     public function it_can_link_insurance_to_category_product()
//     {
//         $insurance = Insurance::factory()->create();
//         $category = Category::factory()->create();
//         $product = CategoryProduct::factory()->create(['category_id' => $category->category_id]);

//         $link = InsuranceCategoryProduct::create([
//             'insurance_id' => $insurance->insurance_id,
//             'category_id' => $category->category_id,
//             'category_product_id' => $product->category_product_id,
//             'is_active' => true,
//         ]);

//         $this->assertDatabaseHas('IBMS_INSURANCE_CATEGORY_PRODUCTS', [
//             'insurance_id' => $insurance->insurance_id,
//             'category_product_id' => $product->category_product_id,
//         ]);
//     }

//     #[Test]
//     public function it_can_create_an_insurance_policy()
//     {
//         $insuranceProduct = InsuranceCategoryProduct::factory()->create();

//         $policy = InsuranceProductPolicy::create([
//             'insurance_category_product_id' => $insuranceProduct->insurance_category_product_id,
//             'policy_code' => 'POL-001',
//             'base_premium' => 1000.00,
//             'sum_insured' => 100000.00,
//             'commission_rate' => 0.15,
//             'is_active' => true,
//         ]);

//         $this->assertDatabaseHas('IBMS_INSURANCE_PRODUCT_POLICIES', [
//             'policy_code' => 'POL-001',
//         ]);
//     }

//     #[Test]
//     public function it_can_add_benefits_to_a_policy()
//     {
//         $policy = InsuranceProductPolicy::factory()->create();

//         $benefit = PolicyBenefit::create([
//             'insurance_product_policy_id' => $policy->insurance_product_policy_id,
//             'deescription' => '24/7 Roadside Assistance',
//         ]);

//         $this->assertDatabaseHas('IBMS_POLICY_BENEFITS', [
//             'deescription' => '24/7 Roadside Assistance',
//         ]);
//     }

//     #[Test]
//     public function it_can_get_insurance_policies_through_relationships()
//     {
//         $insurance = Insurance::factory()->create();
//         $category = Category::factory()->create();
//         $product = CategoryProduct::factory()->create(['category_id' => $category->category_id]);
//         $link = InsuranceCategoryProduct::factory()->create([
//             'insurance_id' => $insurance->insurance_id,
//             'category_id' => $category->category_id,
//             'category_product_id' => $product->category_product_id,
//         ]);
//         $policy = InsuranceProductPolicy::factory()->create([
//             'insurance_category_product_id' => $link->insurance_category_product_id,
//         ]);

//         // Test relationships
//         $this->assertInstanceOf(Insurance::class, $link->insurance);
//         $this->assertInstanceOf(Category::class, $link->category);
//         $this->assertInstanceOf(CategoryProduct::class, $link->categoryProduct);
//         $this->assertInstanceOf(InsuranceProductPolicy::class, $link->policies->first());
//     }

//     #[Test]
//     public function it_can_soft_delete_an_insurance_company()
//     {
//         $insurance = Insurance::factory()->create();
//         $insurance->delete();

//         $this->assertSoftDeleted($insurance);
//     }
// }
