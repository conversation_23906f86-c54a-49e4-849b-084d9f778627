import React, {useEffect, useState} from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>, PageHeader, Button, Alert } from "../../components";
import FormField from "../../components/FormFields";
import {userService} from "../../services/index.js";

const ChangePassword = () => {
  const navigate = useNavigate();
  const [form, setForm] = useState({
    current_password: "",
    password: "",
    password_confirmation: ""
  });
  const [validation, setValidation] = useState({});
  const [loading, setLoading] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [alertMessage, setAlertMessage] = useState("");
  const [success, setSuccess] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    setValidation((prev) => ({ ...prev, [name]: "" }));
  };

  const validate = () => {
   const errors = {};
    if (!form.current_password) errors.current_password = "Current password is required";
    if (!form.password) errors.password = "New password is required";
    if (!form.password_confirmation) errors.password_confirmation = "Please confirm your new password";
    if (form.password && form.password_confirmation && form.password !== form.password_confirmation) {
      errors.password_confirmation = "Passwords do not match";
    }
    setValidation(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validate()) return;
    setLoading(true);
    try {
      // Simulate API call
       userService.changePassword({form, setValidation, setSuccess, setLoading});

      setForm({ current_password: "", password: "", password_confirmation: "" });
      if(success) {
        setShowAlert(true);
        setAlertType("success");
        setAlertMessage("Password changed successfully!");
        setTimeout(() => navigate("/broker/profile"), 1200);
      }
    } catch (err) {
      setShowAlert(true);
      setAlertType("error");
      setAlertMessage("Failed to change password");
    }
  /*  finally {
      setLoading(false);
    }*/
  };


if (loading){
  return <p>Loading . . .</p>
}


  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center">
      <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
        <PageHeader
          title="Change Password"
          subtitle="Update your account password"
          breadcrumbs={[
            { label: "Dashboard", href: "/broker/dashboard" },
            { label: "Profile", href: "/broker/profile" },
            { label: "Change Password" },
          ]}
        />
        <Card title="Change Password">
          {showAlert && (
            <Alert
              type={alertType}
              title={alertType === "success" ? "Success" : "Error"}
              message={alertMessage}
              onClose={() => setShowAlert(false)}
            />
          )}
          <form className="space-y-6" onSubmit={handleSubmit}>
            <FormField
              label="Current Password"
              name="current_password"
              type="password"
              value={form.current_password}
              onChange={handleChange}
              placeholder="Enter current password"
              validationError={validation.current_password}
            />
            <FormField
              label="New Password"
              name="password"
              type="password"
              value={form.password}
              onChange={handleChange}
              placeholder="Enter new password"
              validationError={validation.password}
            />
            <FormField
              label="Confirm New Password"
              name="password_confirmation"
              type="password"
              value={form.password_confirmation}
              onChange={handleChange}
              placeholder="Confirm new password"
              validationError={validation.password_confirmation}
            />
            <div className="flex justify-end">
              <Button type="submit" variant="primary" loading={loading}>
                Change Password
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
};

export default ChangePassword; 