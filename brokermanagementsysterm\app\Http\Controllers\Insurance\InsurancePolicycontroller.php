<?php

namespace App\Http\Controllers;

use App\Models\InsuranceProductPolicy;
use Illuminate\Http\Request;

class InsurancePolicyController extends Controller
{
    public function index()
    {
        return InsuranceProductPolicy::with(['insuranceCategoryProduct'])->get();
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'insurance_category_product_id' => 'required|exists:IBMS_INSURANCE_CATEGORY_PRODUCTS,insurance_category_product_id',
            'policy_code' => 'required|string|unique:IBMS_INSURANCE_PRODUCT_POLICIES',
            'base_premium' => 'required|numeric',
            'sum_insured' => 'required|numeric',
            'commission_rate' => 'required|numeric',
            'vehicle_commercial' => 'boolean',
            'vehicle_private' => 'boolean',
            'vehicle_motorcycle' => 'boolean',
            'vehicle_special' => 'boolean',
            'is_active' => 'boolean',
        ]);

        return InsuranceProductPolicy::create($validated);
    }

    public function show(InsuranceProductPolicy $policy)
    {
        return $policy->load(['insuranceCategoryProduct', 'benefits']);
    }

    public function update(Request $request, InsuranceProductPolicy $policy)
    {
        $validated = $request->validate([
            'policy_code' => 'sometimes|string|unique:IBMS_INSURANCE_PRODUCT_POLICIES,policy_code,'.$policy->insurance_product_policy_id.',insurance_product_policy_id',
            'base_premium' => 'sometimes|numeric',
            'sum_insured' => 'sometimes|numeric',
            'commission_rate' => 'sometimes|numeric',
            'vehicle_commercial' => 'sometimes|boolean',
            'vehicle_private' => 'sometimes|boolean',
            'vehicle_motorcycle' => 'sometimes|boolean',
            'vehicle_special' => 'sometimes|boolean',
            'is_active' => 'sometimes|boolean',
        ]);

        $policy->update($validated);

        return $policy;
    }

    public function destroy(InsuranceProductPolicy $policy)
    {
        $policy->delete();

        return response()->noContent();
    }
}
