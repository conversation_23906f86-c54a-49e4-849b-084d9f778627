<?php

namespace Database\Factories;

use App\Models\Category;
use App\Models\CategoryProduct;
use Illuminate\Database\Eloquent\Factories\Factory;

class CategoryProductFactory extends Factory
{
    protected $model = CategoryProduct::class;

    public function definition()
    {
        return [
            'category_product_name' => $this->faker->unique()->randomElement([
                'Comprehensive Auto Cover',
                'Third Party Liability',
                'Family Health Plan',
                'Business Protection Plan',
                'Personal Accident Cover',
                'Home & Contents Insurance',
                'Travel Medical Insurance',
                'Critical Illness Cover',
                'Pet Wellness Plan',
                'Income Protection Policy',
            ]),
            'category_product_description' => $this->faker->sentence(),
            'category_product_code' => 'PROD-'.$this->faker->unique()->numberBetween(1000, 9999),
            'category_id' => Category::factory(),
            'category_product_isactive' => $this->faker->boolean(90), // 90% chance to be true
        ];
    }
}
