<?php

namespace Database\Seeders;

use App\Models\Insurance;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class InsuranceSeeder extends Seeder
{
    public function run()
    {
        $companies = [
            ['name' => 'Allianz Insurance', 'company_code' => 'ALZ'],
            ['name' => 'AXA Insurance', 'company_code' => 'AXA'],
            ['name' => 'Zurich Insurance', 'company_code' => 'ZUR'],
            ['name' => 'Aetna Insurance', 'company_code' => 'AET'],
            ['name' => 'Prudential Insurance', 'company_code' => 'PRU'],
            ['name' => 'MetLife Insurance', 'company_code' => 'MET'],
            ['name' => 'State Farm Insurance', 'company_code' => 'STF'],
            ['name' => 'Liberty Mutual Insurance', 'company_code' => 'LMI'],
            ['name' => 'Nationwide Insurance', 'company_code' => 'NWI'],
            ['name' => 'Progressive Insurance', 'company_code' => 'PRO'],
        ];

        foreach ($companies as $company) {
            Insurance::create([
                'insurance_id' => Str::uuid(),
                'name' => $company['name'],
                'company_code' => $company['company_code'],
                'email' => strtolower($company['company_code']).'@example.com',
                'phone_number' => '1-800-'.rand(100, 999).'-'.rand(1000, 9999),
                'address' => rand(100, 9999).' Main St, Anytown, USA',
                'logo_path' => 'logos/'.strtolower($company['company_code']).'.png',
                'is_active' => true,
            ]);
        }
    }
}
