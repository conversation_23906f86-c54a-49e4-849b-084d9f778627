import React, {useEffect, useState} from "react";
import { Card, PageHeader, Button, Alert } from "../../components";
import { useNavigate } from "react-router-dom";
import axios from "/src/utils/axios.js";
import { userService } from '../../services/userService.js'

export default function Profile() {
  const [showSuccess, setShowSuccess] = useState(false);
  const [userRes, setUserRes] = useState({});
  const [loading, setLoading] = useState(false);

  //const [brokerData, setBrokerData ] = useState({});
  
  // Mock broker data - in a real app, this would come from context or API
/*  const [brokerData] = useState({
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+254 700 123 456",
    idNumber: "12345678",
    licenseNumber: "BRK-2024-001",
    address: "Nairobi, Kenya",
    status: "Active"
  });*/




  useEffect(() => {
    const fetchUser = async () => {
      try {
        const user = await userService.getCurrentUser(); // 'me'
        setUserRes(user);
        console.log('user data', user); // Logs 'me'
      } catch (error) {
        console.error('Failed to fetch user', error);
      }
    };

    fetchUser();
  }, []);

   useEffect(() => {
    // if (userRes) {
     if (Object.keys(userRes).length === 0 && userRes.constructor === Object) {
       setLoading(true);
     }
     else {
       setLoading(false);

     }
   }, [userRes]);


  /* const [userRes, setUserRes] = useState(null);

   useEffect(() => {
     const fetchUser = async () => {
       try {
         const user = await userService.getCurrentUser();
         setUserRes(user);
         console.log('user data', user);
       } catch (error) {
         console.error('Failed to fetch user', error);
       }
     };

     fetchUser();
   }, []);*/


  const navigate = useNavigate();
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }


  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full mx-auto p-10 space-y-6">
        <PageHeader
          title="Broker Profile"
          subtitle="Manage your personal information and account details"
          breadcrumbs={[
            { label: "Dashboard", href: "/broker/dashboard" },
            { label: "Profile" },
          ]}
          actions={[
            {
              label: "Edit",
              variant: "primary",
              onClick: () => navigate(`/broker/profile/edit`),
            },
          ]}
        />

        {showSuccess && (
          <Alert
            type="success"
            title="Profile Updated"
            message="Your profile information has been successfully updated."
            onClose={() => setShowSuccess(false)}
          />
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
          {/* Profile Card */}
          <div className="lg:col-span-1">
            <Card title="Profile Picture" subtitle="Your profile photo">
              <div className="flex flex-col items-center space-y-4">
                <div className="relative">
                  <div className="h-32 w-32 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-4xl font-bold text-blue-600">
                     {/* {brokerData.name.split(' ').map(n => n[0]).join('')}*/}
                    </span>
                  </div>
                </div>
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900">{userRes?.first_name+ ' ' +userRes?.last_name }</h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-2 ${
                    userRes.status === "Active" ? "bg-green-100 text-green-800" : "bg-gray-200 text-gray-600"
                  }`}>
                    Active
                  </span>
                </div>
              </div>
            </Card>
          </div>

          {/* Profile Details */}
          <div className="lg:col-span-2">
            <Card 
              title="Personal Information" 
              subtitle="Your basic profile details"
            >
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Full Name
                    </label>
                    <p className="text-sm text-gray-900 font-medium">{userRes?.first_name+ ' ' +userRes?.last_name }</p>
                  </div>

                  {/* Email */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Email Address
                    </label>
                    <p className="text-sm text-gray-900 font-medium">{userRes?.email}</p>
                  </div>

                  {/* Phone Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Phone Number
                    </label>
                    <p className="text-sm text-gray-900 font-medium">{userRes?.phone_number}</p>
                  </div>

                  {/* ID Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      ID Number
                    </label>
                    <p className="text-sm text-gray-900 font-medium">{userRes?.id_number}</p>
                  </div>

                  {/* License Number */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      License Number
                    </label>
                    <p className="text-sm text-gray-900 font-medium"> {userRes?.license_number}</p>
                  </div>

                  {/* Address */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Address
                    </label>
                    <p className="text-sm text-gray-900 font-medium">{userRes?.address}</p>
                  </div>
                </div>

                <div className="pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900">Account Status</h4>
                      <p className="text-sm text-gray-600">Your account is currently active and verified</p>
                    </div>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                        userRes?.deleted_at === null ? "bg-green-100 text-green-800" : "bg-gray-200 text-gray-600"
                    }`}>
  {userRes?.deleted_at === null ? "Active" : "Inactive"}
</span>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>

        {/* Additional Information Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          <Card title="Account Security" subtitle="Manage your account security settings">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Password</h4>
                  <p className="text-sm text-gray-600">Last changed 30 days ago</p>
                </div>
                <Button className="mt-4" size="small" variant="primary" onClick={() => navigate('/broker/change-password')}>
                  Change Password
                </Button>
              </div>
            </div>
          </Card>

          <Card title="Activity Summary" subtitle="Your recent account activity">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Quotes Generated</h4>
                  <p className="text-sm text-gray-600">24 this month</p>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">Active Clients</h4>
                  <p className="text-sm text-gray-600">12 clients</p>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
