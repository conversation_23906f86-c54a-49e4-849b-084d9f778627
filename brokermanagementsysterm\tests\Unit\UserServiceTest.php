<?php

namespace Tests\Unit;

use App\Models\IBMSPermissions;
use App\Models\Role;
use App\Models\RoleSetupService;
use App\Models\User;
use App\Models\UserService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserServiceTest extends TestCase
{
    /**
     * A basic unit test example.
     */
    use RefreshDatabase;

    public function test_example(): void
    {

        $this->assertTrue(true);
    }

    public function testhas_required_permission(): void
    {
        $userService = new UserService;
        $roleService = new RoleSetupService;

        $roleService::createRoles();
        $user = User::factory()->create(
            [
                'role_id' => Role::where('role_id', 2)->first()?->role_id,
            ]
        );

        $this->assertFalse($userService->hasRequiredPermission($user, IBMSPermissions::IBMS_ADD_BROKER));
        $this->assertTrue($userService->hasRequiredPermission($user, IBMSPermissions::IBMS_ADD_CLIENT));
    }

    public function testis_admin(): void
    {
        $userService = new UserService;
        $roleService = new RoleSetupService;

        $roleService::createRoles();
        $user = User::factory()->create([
            'role_id' => Role::where('role_id', 2)->first()?->role_id,

        ]);

        $this->assertFalse($userService->isAdmin($user));

        $anotherUser = User::factory()->create([
            'role_id' => Role::where('role_id', 1)->first()?->role_id,

        ]);
        $this->assertTrue($userService->isAdmin($anotherUser));
    }

    public function testis_broker(): void
    {
        $userService = new UserService;
        $roleService = new RoleSetupService;

        $roleService::createRoles();
        $user = User::factory()->create([
            'role_id' => Role::where('role_id', 2)->first()?->role_id,

        ]);
        $this->assertTrue($userService->isBroker($user));

        $anotherUser = User::factory()->create([
            'role_id' => Role::where('role_id', 1)->first()?->role_id,
        ]);
        $this->assertFalse($userService->isBroker($anotherUser));
    }
}
