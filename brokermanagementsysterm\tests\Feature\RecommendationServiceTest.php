<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\CategoryProduct;
use App\Models\Client;
use App\Models\ClientPreferences;
use App\Models\Insurance;
use App\Models\InsuranceCategoryProduct;
use App\Models\Role;
use App\Models\RoleSetupService;
use App\Models\User;
use App\Models\Vehicle;
use App\Services\RecommendationGAService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;
use Tests\TestCase;

class RecommendationTestSeeder
{
    // public function run(): array
    // {
    //     $this->seedCategoriesAndProducts();
    //     $this->seedInsurers();
    //     $this->seedInsuranceProducts();

    //     $setup = new RoleSetupService();
    //     $setup::createRoles();
    //     $testBroker = User::factory()->broker()->create(['role_id' => Role::find(2)]);
    //     $client = Client::factory()->create(['broker_id' => $testBroker->user_id]);
    //     $vehicle = Vehicle::factory()->create(['client_id' => $client->client_id]);

    //     ClientPreferences::factory()->create([
    //         'vehicle_id' => $vehicle->vehicle_id,
    //         'preferred_cover_types' => ['COMPREHENSIVE_MOTOR'],
    //         'preferred_insurers' => Insurance::pluck('insurance_id')->take(2)->toArray(),
    //         'budget_min' => 1000,
    //         'budget_max' => 10000,
    //     ]);

    //     return compact('client', 'vehicle');
    // }
    public function run(): array
    {
        $this->seedCategoriesAndProducts();
        $this->seedInsurers();
        $this->seedInsuranceProducts();

        $setup = new RoleSetupService;
        $setup::createRoles();

        $testBroker = User::factory()->broker()->create(['role_id' => Role::find(2)]);
        $client = Client::factory()->create(['broker_id' => $testBroker->user_id]);

        $coverOptions = [
            ['COMPREHENSIVE_MOTOR'],
            ['THIRD_PARTY_MOTOR'],
            ['COMPREHENSIVE_MOTOR', 'THIRD_PARTY_MOTOR'],
            ['ACCIDENTS_THEFT_MOTOR'],
            ['COMPREHENSIVE_MOTOR', 'ACCIDENTS_THEFT_MOTOR'],
        ];

        $usageOptions = ['personal', 'commercial', 'public'];

        $vehicles = [];

        for ($i = 0; $i < 5; $i++) {
            $vehicle = Vehicle::factory()->create([
                'client_id' => $client->client_id,
                'vehicle_purpose' => $usageOptions[$i % count($usageOptions)],
            ]);

            ClientPreferences::factory()->create([
                'vehicle_id' => $vehicle->vehicle_id,
                'preferred_cover_types' => $coverOptions[$i % count($coverOptions)],
                'preferred_insurers' => Insurance::inRandomOrder()->take(2)->pluck('insurance_id')->toArray(),
                'budget_min' => 5000 + ($i * 1000),
                'budget_max' => 15000 + ($i * 1000),
                'vehicle_usage' => $vehicle->vehicle_purpose,
            ]);

            $vehicles[] = $vehicle;
        }

        return compact('client', 'vehicles');
    }

    private function seedCategoriesAndProducts(): void
    {
        $category = Category::factory()->create(['category_name' => 'MOTOR_INSURANCE']);

        $products = [
            ['COMPREHENSIVE_MOTOR', 'AUTO-COMP', 1],
            ['THIRD_PARTY_MOTOR', 'AUTO-TP', 2],
            ['ACCIDENTS_THEFT_MOTOR', 'AUTO-ATM', 4],
        ];

        foreach ($products as [$name, $code, $bit]) {
            CategoryProduct::create([
                'category_product_id' => Str::uuid(),
                'category_id' => $category->category_id,
                'category_product_name' => $name,
                'category_product_description' => "$name description",
                'category_product_code' => $code,
                'bit_value' => $bit,
                'category_product_isactive' => true,
            ]);
        }
    }

    private function seedInsurers(): void
    {
        for ($i = 1; $i <= 5; $i++) {
            Insurance::create([
                'insurance_id' => Str::uuid(),
                'name' => "Insurer $i",
                'email' => "insurer$<EMAIL>",
                'phone_number' => "07110000$i",
                'address' => 'Nairobi',
                'logo_path' => "logos/insurer$i.png",
                'is_active' => true,
            ]);
        }
    }

    private function seedInsuranceProducts(): void
    {
        $insurers = Insurance::all();
        $categoryProducts = CategoryProduct::all();
        $categoryId = Category::first()->category_id;

        foreach ($insurers as $insurer) {
            foreach ($categoryProducts as $product) {
                InsuranceCategoryProduct::create([
                    'insurance_category_product_id' => Str::uuid(),
                    'insurance_id' => $insurer->insurance_id,
                    'category_product_id' => $product->category_product_id,
                    'category_id' => $categoryId,
                    'vehicle_usage' => 'personal',
                    'price' => rand(500, 15000),
                    'is_active' => true,
                ]);
            }
        }
    }
}

class RecommendationServiceTest extends TestCase
{
    use RefreshDatabase;

    public function test_recommendation_engine_returns_scored_results()
    {
        $seeder = new RecommendationTestSeeder;
        $data = $seeder->run();
        $client = $data['client'];

        $recommendationService = new RecommendationGAService;
        $recommendations = $recommendationService->recommendForClient($client, 5);

        $this->assertNotEmpty($recommendations);

        foreach ($recommendations as $rec) {
            $this->assertArrayHasKey('product_id', $rec);
            $this->assertArrayHasKey('cover_type', $rec);
            $this->assertArrayHasKey('insurer_name', $rec);
            $this->assertArrayHasKey('price', $rec);
            $this->assertArrayHasKey('fitness_score', $rec);

            $this->assertNotNull($rec['product_id']);
            $this->assertNotNull($rec['fitness_score']);
        }
        foreach ($recommendations as $i => $rec) {
            echo '🔹 Recommendation #'.($i + 1)."\n";
            echo "   Vehicle ID      #  : {$rec['vehicle_id']}]\n";
            echo "   👉 Product ID     : {$rec['product_id']}\n";
            echo "   🛡️ Cover Type     : {$rec['cover_type']}\n";
            echo "   📝 Description     : {$rec['cover_description']}\n";
            echo "   🏢 Insurer         : {$rec['insurer_name']}\n";
            echo '   💰 Price (KES)     : '.number_format($rec['price'], 2)."\n";
            echo '   📊 Fitness Score   : '.number_format($rec['fitness_score'], 3)."\n";
            echo "---------------------------------------------\n";
        }
    }
}
