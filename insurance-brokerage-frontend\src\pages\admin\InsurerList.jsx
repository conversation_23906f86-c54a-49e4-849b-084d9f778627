import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { 
  PageHeader, 
  Card, 
  Button, 
  Alert, 
  Modal, 
  DataTable,
  StatusBadge 
} from "../../components";
import { insuranceService } from "../../services";
import { statusBadgeStyles } from "../../Styles/uiTheme";

const InsurerList = () => {
  const navigate = useNavigate();
  const [companies, setCompanies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [alertType, setAlertType] = useState("success");

  // Fetch companies on component mount
  useEffect(() => {
    fetchCompanies();
  }, []);

  const fetchCompanies = async () => {
    try {
      setLoading(true);
      const data = await insuranceService.getCompanies();
      setCompanies(data);
      setError("");
    } catch (err) {
      setError("Failed to fetch insurance companies");
      console.error("Error fetching companies:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!selectedCompany) return;

    try {
      await insuranceService.deleteCompany(selectedCompany.id);
      setCompanies(companies.filter(company => company.id !== selectedCompany.id));
      setShowDeleteModal(false);
      setSelectedCompany(null);
      showSuccessAlert("Insurance company deleted successfully");
    } catch (err) {
      showErrorAlert("Failed to delete insurance company");
      console.error("Error deleting company:", err);
    }
  };

  const showSuccessAlert = (message) => {
    setAlertMessage(message);
    setAlertType("success");
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  const showErrorAlert = (message) => {
    setAlertMessage(message);
    setAlertType("error");
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  const columns = [
    { 
      key: "name", 
      label: "Company Name", 
      sortable: true,
      render: (value, item) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-blue-600 font-semibold text-sm">
                {value.charAt(0).toUpperCase()}
              </span>
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{value}</div>
          </div>
        </div>
      )
    },
    { 
      key: "email", 
      label: "Email", 
      sortable: true 
    },
    { 
      key: "phone", 
      label: "Phone", 
      sortable: true 
    },
    { 
      key: "coverTypesCount", 
      label: "Cover Types", 
      sortable: true,
      render: (value) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          {value || 0}
        </span>
      )
    },
    { 
      key: "policiesCount", 
      label: "Policies", 
      sortable: true,
      render: (value) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          {value || 0}
        </span>
      )
    },
    {
      key: "actions",
      label: "Actions",
      render: (_, item) => (
        <div className="flex gap-2">
          <Button size="small" variant="outline" onClick={() => handleView(item)}>View</Button>
          <Button size="small" variant="primary" onClick={() => handleEdit(item)}>Edit</Button>
          <Button size="small" variant="danger" onClick={() => handleDeleteClick(item)}>Delete</Button>
        </div>
      ),
    },
  ];

  const handleView = (company) => {
    navigate(`/admin/insurers/${company.id}`);
  };

  const handleEdit = (company) => {
    navigate(`/admin/insurers/${company.id}/edit`);
  };

  const handleDeleteClick = (company) => {
    setSelectedCompany(company);
    setShowDeleteModal(true);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading insurance companies...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
        <div className="w-full mx-auto p-6 space-y-6">
          <PageHeader
            title="Insurance Companies"
            subtitle="Manage insurance companies and their information"
            breadcrumbs={[
              { label: "Dashboard", href: "/admin/dashboard" },
              { label: "Insurance Companies" }
            ]}
            actions={[
              {
                label: "Add New Company",
                variant: "primary",
                onClick: () => navigate("/admin/insurers/create"),
                icon: "+"
              }
            ]}
          />
          {showAlert && (
            <Alert
              type={alertType}
              title={alertType === "success" ? "Success" : "Error"}
              message={alertMessage}
              onClose={() => setShowAlert(false)}
            />
          )}
          {error && (
            <Alert
              type="error"
              title="Error"
              message={error}
              onClose={() => setError("")}
            />
          )}
          <Card
            title="Insurance Companies"
            subtitle={`${companies.length} companies found`}
          >
            <DataTable
              data={companies}
              columns={columns}
              searchable={true}
              pagination={true}
              itemsPerPage={10}
            />
          </Card>
        </div>
      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete Insurance Company"
        size="small"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            Are you sure you want to delete{" "}
            <span className="font-semibold text-gray-900">
              {selectedCompany?.name}
            </span>
            ? This action cannot be undone.
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowDeleteModal(false)}
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={handleDelete}
            >
              Delete
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default InsurerList;
