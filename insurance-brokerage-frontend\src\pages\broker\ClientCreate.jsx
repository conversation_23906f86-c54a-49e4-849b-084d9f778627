import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Alert from "../../components/Alert";
import Button from "../../components/Button";
import Card from "../../components/Card";
import FormWizard from "../../components/FormWizard";
import PageHeader from "../../components/PageHeader";
import {clientService} from "../../services/clientService";

const wizardSteps = [
	{
		title: "Personal Information",
		description: "Enter the client's personal details",
		fields: [
			{
				name: "first_name",
				label: "First Name",
				type: "text",
				placeholder: "Enter first name",
				required: true,
			},
			{
				name: "last_name",
				label: "Last Name",
				type: "text",
				placeholder: "Enter last name",
				required: true,
			},
			{
				name: "id_number",
				label: "ID Number",
				type: "text",
				placeholder: "Enter ID number",
				required: true,
			},
		],
		validation: (data, errors) => {
			if (!data.first_name) errors.first_name = "First name is required";
			if (!data.last_name) errors.last_name = "Last name is required";
			if (!data.id_number) errors.id_number = "ID number is required";
		},
	},
	{
		title: "Contact Information",
		description: "Enter the client's contact details",
		fields: [
			{
				name: "contact_phone",
				label: "Phone Number",
				type: "tel",
				placeholder: "Enter phone number",
				required: true,
			},
			{
				name: "contact_email",
				label: "Email",
				type: "email",
				placeholder: "Enter email address",

				required: true,
			},
		],
		validation: (data, errors) => {
			if (!data.contact_phone) errors.contact_phone = "Phone number is required";
			if (!data.contact_email) errors.contact_email = "Email is required";
		},
	},
];

export default function ClientCreate() {
	const navigate = useNavigate();
	const [alert, setAlert] = useState({
		show: false,
		type: "info",
		title: "",
		message: "",
	});

	const handleWizardComplete = async (data) => {
		await clientService
			.createClient(data);
			console.log("Client created:", data);
			return;
		setAlert({
			show: true,
			type: "success",
			title: "Success",
			message: "Client registered successfully!",
		});
		// Redirect to client list after a short delay
		setTimeout(() => {
			navigate("/broker/clients");
		}, 1200); // 1.2 seconds for user to see the alert
	};

	const handleCancel = () => {
		navigate("/broker/clients");
	};

	return (
		<div className="min-h-screen bg-gray-50 flex flex-col sm:flex-row">
			<div className="flex-1 flex flex-col items-center justify-center py-6 px-2 w-full">
				<div className="w-full max-auto mx-auto"> 
					<PageHeader
						title="Register Client"
						subtitle="Add a new client to the system using the step-by-step form."
						breadcrumbs={[
							{ label: "Dashboard", href: "/broker/dashboard" },
							{ label: "Clients", href: "/broker/clients" },
							{ label: "Register Client" },
						]}
						className="w-full"
						actions={[
							{
								label: "Go Back to Client List",
								variant: "primary",
								onClick: () => navigate("/broker/clients"),
							},
						]}
					/>
					<div className="h-8" />
					{alert.show && (
						<div className="mb-4 w-full">
							<Alert
								type={alert.type}
								title={alert.title}
								message={alert.message}
								onClose={() => setAlert({ ...alert, show: false })}
							/>
						</div>
					)}
					<Card
						title="Client Registration"
						subtitle="Step-by-step client registration form"
						className="w-full"
					>
						<FormWizard
							steps={wizardSteps}
							onComplete={handleWizardComplete}
							onCancel={handleCancel}
						/>
					</Card>
				</div>
			</div>
		</div>
	);
}
