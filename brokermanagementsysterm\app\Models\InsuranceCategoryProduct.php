<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InsuranceCategoryProduct extends Model
{
    use HasFactory, HasUuids;

    protected $primaryKey = 'insurance_category_product_id';

    protected $keyType = 'string';

    public $incrementing = false;

    protected $table = 'IBMS_INSURANCE_CATEGORY_PRODUCTS';

    protected $fillable = [
        'insurance_id', 'category_id', 'category_product_id',
        'is_active', 'price', 'vehicle_usage', ];

    public function insurance()
    {
        return $this->belongsTo(Insurance::class, 'insurance_id');
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function categoryProduct()
    {
        return $this->belongsTo(CategoryProduct::class, 'category_product_id');
    }

    public function policies()
    {
        return $this->hasMany(InsuranceProductPolicy::class, 'insurance_category_product_id');  //
    }
}
