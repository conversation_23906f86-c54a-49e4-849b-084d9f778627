replacements = {
    'IBMS_USER_ID': 'user_id',
    'IBMS_USER_FIRST_NAME': 'first_name',
    'IBMS_USER_LAST_NAME': 'last_name',
    'IBMS_USER_EMAIL': 'email',
    'IBMS_USER_CONTACT_PHONE': 'phone',
    'IBMS_USER_VERIFIED_AT': 'verified_at',
    'IBMS_USER_EMAIL_VERIFIED': 'email_verified',
    'IBMS_USER_PASSWORD': 'password',
    'IBMS_USER_LASTSEEN': 'last_seen',
    'IBMS_USER_ROLE_ID': 'role_id',
    'IBMS_ROLE_ID': 'role_id',
    'IBMS_ROLE_NAME': 'role_name',
    'IBMS_ROLE_PERMISSIONS': 'role_permissions',
    'IBMS_CLIENT_ID': 'client_id',
    'IBMS_CLIENT_FIRST_NAME': 'first_name',
    'IBMS_CLIENT_LAST_NAME': 'last_name',
    'IBMS_CLIENT_CONTACT_EMAIL': 'email',
    'IBMS_CLIENT_CONTACT_PHONE': 'phone',
    'IBMS_CLIENT_ID_NUMBER': 'id_number',
    'IBMS_CLIENT_BROKER_ID': 'broker_id',
    'IBMS_VEHICLE_ID': 'vehicle_id',
    'IBMS_VEHICLE_MILEAGE': 'mileage',
    'IBMS_VEHICLE_MAKE': 'make',
    'IBMS_VEHICLE_MODEL': 'model',
    'IBMS_VEHICLE_REG_NO': 'registration_number',
    'IBMS_VEHICLE_VALUE': 'value',
    'IBMS_VEHICLE_COVERTYPE': 'cover_type',
    'IBMS_VEHICLE_CLIENT_ID': 'client_id',
}

import subprocess

for key, value in replacements.items():
    result = subprocess.run(["./rename.sh", key, value], capture_output=True, text=True)
    print(f"Renaming {key} -> {value}")
    print("STDOUT:")
    print(result.stdout)
    print("STDERR:")
    print(result.stderr)

