<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('IBMS_USERS', function (Blueprint $table) {
            $table->uuid('user_id')->primary();
            $table->string('first_name')->nullable(false);
            $table->string('id_number')->nullable(false);
            $table->string('last_name')->nullable(false);
            $table->string('email')->unique()->nullable(false);
            $table->string('phone_number')->unique()->nullable(false);
            $table->timestamp('verified_at')->nullable();
            $table->boolean('email_verified')->default(false);
            $table->string('password');
            $table->timestamp('last_seen')->default(now());
            $table->unsignedBigInteger('role_id');
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();

        });

        Schema::create('IBMS_PASSWORD_RESET_TOKENS', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('IBMS_SESSIONS', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignUuid('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IBMS_USERS');
        Schema::dropIfExists('IBMS_PASSWORD_RESET_TOKENS');
        Schema::dropIfExists('IBMS_SESSIONS');
    }
};
