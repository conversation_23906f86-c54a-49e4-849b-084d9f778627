import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { <PERSON>Header, Alert, Card } from "../../components";
import FormField from "../../components/FormFields";
import { insuranceService } from "../../services";

const InsurerEdit = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [form, setForm] = useState({
    name: "",
    phone: "",
    email: "",
  });
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState("");
  const [showAlert, setShowAlert] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [alertType, setAlertType] = useState("success");
  const [validation, setValidation] = useState({});

  // Fetch insurer data on component mount
  useEffect(() => {
    fetchInsurer();
  }, [id]);

  const fetchInsurer = async () => {
    try {
      setInitialLoading(true);
      const insurer = await insuranceService.getCompany(id);
      setForm({
        name: insurer.name || "",
        phone: insurer.phone || "",
        email: insurer.email || "",
      });
      setError("");
    } catch (err) {
      setError("Failed to fetch insurance company details");
      console.error("Error fetching insurer:", err);
    } finally {
      setInitialLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
    setValidation((prev) => ({ ...prev, [name]: "" }));
  };

  const validate = () => {
    const errors = {};
    if (!form.name) errors.name = "Company name is required";
    if (!form.phone) errors.phone = "Contact number is required";
    if (!form.email) errors.email = "Contact email is required";
    if (form.email && !/\S+@\S+\.\S+/.test(form.email)) {
      errors.email = "Please enter a valid email address";
    }
    setValidation(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validate()) return;
    try {
      setLoading(true);
      setError("");
      const companyData = {
        ...form,
        status: "Active",
      };
      await insuranceService.updateCompany(id, companyData);
      showSuccessAlert("Insurance company updated successfully!");
      setTimeout(() => {
        navigate("/admin/insurers");
      }, 1500);
    } catch (err) {
      setError(err.response?.data?.message || "Failed to update insurance company");
    } finally {
      setLoading(false);
    }
  };

  const showSuccessAlert = (message) => {
    setAlertMessage(message);
    setAlertType("success");
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  const handleCancel = () => {
    navigate("/admin/insurers");
  };

  if (initialLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading insurance company details...</p>
        </div>
      </div>
    );
  }

  if (error && !initialLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex-1 overflow-x-hidden p-4">
          <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
            <PageHeader
              title="Edit Insurance Company"
              subtitle="Update insurance company information"
              breadcrumbs={[
                { label: "Dashboard", href: "/admin/dashboard" },
                { label: "Insurance Companies", href: "/admin/insurers" },
                { label: "Edit Company" }
              ]}
              actions={[
                {
                  label: "Back to Companies",
                  variant: "outline",
                  onClick: handleCancel,
                }
              ]}
            />
            <Alert
              type="error"
              title="Error"
              message={error}
              onClose={() => setError("")}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex-1 overflow-x-hidden p-4">
        <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
          <PageHeader
            title="Edit Insurance Company"
            subtitle="Update insurance company information"
            breadcrumbs={[
              { label: "Dashboard", href: "/admin/dashboard" },
              { label: "Insurance Companies", href: "/admin/insurers" },
              { label: "Edit Company" }
            ]}
            actions={[
              {
                label: "Back to Companies",
                variant: "outline",
                onClick: handleCancel,
              }
            ]}
          />
          {showAlert && (
            <Alert
              type={alertType}
              title={alertType === "success" ? "Success" : "Error"}
              message={alertMessage}
              onClose={() => setShowAlert(false)}
            />
          )}
          {error && (
            <Alert
              type="error"
              title="Error"
              message={error}
              onClose={() => setError("")}
            />
          )}
          <Card title="Edit Insurance Company">
            <form className="space-y-6" onSubmit={handleSubmit}>
              <FormField
                label="Company Name"
                name="name"
                value={form.name}
                onChange={handleChange}
                placeholder="Enter company name"
                isRequired={true}
                validationError={validation.name}
              />
              <FormField
                label="Contact Number"
                name="phone"
                type="tel"
                value={form.phone}
                onChange={handleChange}
                placeholder="+254700000000"
                isRequired={true}
                validationError={validation.phone}
              />
              <FormField
                label="Contact Email"
                name="email"
                type="email"
                value={form.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
                isRequired={true}
                validationError={validation.email}
              />
              <div className="flex justify-end gap-2">
                <button
                  type="button"
                  className="px-4 py-2 rounded bg-gray-200 text-gray-700 hover:bg-gray-300 font-medium"
                  onClick={handleCancel}
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 rounded bg-blue-700 text-white hover:bg-blue-800 font-medium"
                  disabled={loading}
                >
                  {loading ? "Updating..." : "Update Company"}
                </button>
              </div>
            </form>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default InsurerEdit; 