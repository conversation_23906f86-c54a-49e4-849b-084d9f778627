import React, {useEffect, useState} from "react";
import { useParams, useNavigate } from "react-router-dom";
import PageHeader from "../../components/PageHeader";
import {userService} from "../../services/index.js";

// Use the same mockBrokers as in BrokerList for now
const mockBrokers = [
  {
    id: 1,
    first_name: "<PERSON>",
    lastName: "Do<PERSON>",
    id_number: "123456",
    phone_number: "0712345678",
    email: "<EMAIL>",
    role: "broker",
  },
  {
    id: 2,
    first_name: "<PERSON>",
    lastName: "<PERSON>",
    id_number: "654321",
    phone_number: "0723456789",
    email: "<EMAIL>",
    role: "broker",
  },
  {
    id: 3,
    first_name: "<PERSON>",
    lastName: "<PERSON>",
    id_number: "789012",
    phone_number: "0734567890",
    email: "<EMAIL>",
    role: "broker",
  },
];

const BrokerDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  // You may want to fetch broker from API in real app, here we use mockBrokers

const [broker, setBrokerData ] = useState({});
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const user = await userService.getUser(id); // 'me'
        setBrokerData(user);
        console.log('user data', user); // Logs 'me'
      } catch (error) {
        console.error('Failed to fetch user', error);
      }
    };

    fetchUser();
  }, []);

  useEffect(() => {
    // if (userRes) {
    if (Object.keys(broker).length === 0 && broker.constructor === Object) {
      setLoading(true);
    }
    else {
      setLoading(false);

    }
  }, [broker]);
  if (loading) {
    return <div>Loading...</div>;
  }
  if (!broker) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-gray-500 text-lg">Broker not found.</div>
      </div>
    );
  }


  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center py-8 px-2 sm:py-12 sm:px-4">
      <div className="w-full max-w-4xl mx-auto">
        <PageHeader
          title="Broker Details"
          subtitle={`Details for ${broker.first_name} ${broker.last_name}`}
          breadcrumbs={[
            { label: "User Registration", href: "/admin/user-registration" },
            { label: "Broker List", href: "/admin/brokers" },
            {
              label: `Broker Details: ${broker.first_name} ${broker.last_name}`,
            },
          ]}
          actions={[
            {
              label: "Back to List",
              variant: "primary",
              onClick: () => navigate("/admin/brokers"),
            },
          ]}
        />
        <div className="bg-white rounded-xl shadow-lg p-10 mt-8">
          <div className="space-y-4">
            <div>
              <span className="font-semibold">First Name: </span>
              <span>{broker.first_name}</span>
            </div>
            <div>
              <span className="font-semibold">Last Name: </span>
              <span>{broker.last_name}</span>
            </div>
            <div>
              <span className="font-semibold">ID Number: </span>
              <span>{broker.id_number}</span>
            </div>
            <div>
              <span className="font-semibold">phone Number: </span>
              <span>{broker.phone_number}</span>
            </div>
            <div>
              <span className="font-semibold">Email: </span>
              <span>{broker.email}</span>
            </div>
            <div>
              <span className="font-semibold">Role: </span>
              <span>
                {broker?.role?.role_name}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BrokerDetails;