<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

class CategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Category::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'category_name' => $this->faker->randomElement([
                'MOTOR_INSURANCE',
                'HEALTH_INSURANCE',
                'TRAVEL_INSURANCE',
                'PROPERTY_INSURANCE',
                'LIFE_INSURANCE',
            ]),
            'category_description' => function (array $attributes) {
                return match ($attributes['category_name']) {
                    'MOTOR_INSURANCE' => 'Motor vehicle insurance products',
                    'HEALTH_INSURANCE' => 'Health and medical insurance plans',
                    'TRAVEL_INSURANCE' => 'Travel protection policies',
                    'PROPERTY_INSURANCE' => 'Home and property coverage',
                    'LIFE_INSURANCE' => 'Life coverage and annuities',
                    default => 'Insurance products'
                };
            },
        ];
    }
}
