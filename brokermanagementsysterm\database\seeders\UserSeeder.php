<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */

    /* public function run(): void
     {

             User::create([
                 'name' => '<PERSON>',
                 'email' => $email,
                 'phone' => $phone,
                 'password' => bcrypt('password'), // or Hash::make()
             ]);

             $this->command->info('User seeded successfully.');
         } else {
             $this->command->info('User already exists. Seeder skipped.');
         }
     }
}*/

    public function run(): void
    {
        $email = '<EMAIL>';
        $email2 = '<EMAIL>';
        $phone_number = '+254700123456';
        $phone_number2 = '+254700123457';

        $exists = User::where('email', $email)
            ->orWhere('phone_number', $phone_number)
            ->exists();

        if (! $exists) {
            $user_password = Hash::make('Aimsoft@1');
            User::firstOrCreate([
                'first_name' => 'Aimsoft',
                'last_name' => 'Admin',
                'id_number' => 'qwert1',
                // 'name' =>  $request->input('first_name') . ' ' . $request->input('last_name'),
                'email' => $email,
                'phone_number' => $phone_number,
                'role_id' => 1,
                'password' => $user_password]
            );
            // $user_password = Hash::make('Aimsoft@1');
            User::firstOrCreate([
                'first_name' => 'AimsoftBroker',
                'last_name' => 'Admin',
                'id_number' => 'qwert111',
                // 'name' =>  $request->input('first_name') . ' ' . $request->input('last_name'),
                'email' => $email2,
                'phone_number' => $phone_number2,
                'role_id' => 2,
                'password' => $user_password]
            );
            $this->command->info('User seeded successfully.');
        } else {
            $this->command->info('User already exists. Seeder skipped.');
        }
    }
}
