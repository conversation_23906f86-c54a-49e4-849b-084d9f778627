<?php

namespace App\Listeners;

use App\Events\UserRegistered;
use App\Mail\QuotationMail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendWelcomeEmail
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UserRegistered $event)
    {
        Log::info('Liseners');
        $user = $event->user;
        $mailData = [
        'title' => 'Onboarding Mail from ' . config('app.name'),
        'body' => $user->first_name .' ' . $user->last_name . ' An account has been created for you. Visit ' . config('app.frontend_url') . '. Your email is ' . $user->email . ' and password  : Aimosf@1' ,
    ];


        Mail::to($event->user->email)-> send(new QuotationMail($mailData));
    }
}
