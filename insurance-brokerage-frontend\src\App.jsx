import React, { useState } from "react";
import { InsuranceProvider } from "./context/InsuranceContext";
import Sidebar from "./components/sidebar";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  useLocation,
} from "react-router-dom";
import AuthRoutes, {AdminRoutes, BrokerRoutes, GuestRoutes} from "./context/AuthRoutes.jsx";
import { AuthProvider } from "./context/AuthProvider";
// Admin pages
import AdminDashboard from "./pages/admin/Dashboard";
import BrokerList from "./pages/admin/BrokerList";
import AdminList from "./pages/admin/AdminList";
import UserRegistration from "./pages/admin/UserRegistration";
import BrokerDetails from "./pages/admin/BrokerDetails";
import InsurerList from "./pages/admin/InsurerList";
import InsurerCreate from "./pages/admin/InsurerCreate";
import InsurerEdit from "./pages/admin/InsurerEdit";
import InsurerDetails from "./pages/admin/InsurerDetails";
import ProductList from "./pages/admin/ProductList";
import ProductCreate from "./pages/admin/ProductCreate";
import ProductDetails from "./pages/admin/ProductDetails";
import CoverTypeList from "./pages/admin/CoverTypeList";
import CoverTypeCreate from "./pages/admin/CoverTypeCreate";
import CoverTypeEdit from "./pages/admin/CoverTypeEdit";
import CoverTypeDetails from "./pages/admin/CoverTypeDetails";
import PolicyList from "./pages/admin/PolicyList";
import PolicyCreate from "./pages/admin/PolicyCreate";
import PolicyEdit from "./pages/admin/PolicyEdit";
import PolicyDetails from "./pages/admin/PolicyDetails";
import BenefitList from "./pages/admin/BenefitList";
import BenefitCreate from "./pages/admin/BenefitCreate";
import BenefitEdit from "./pages/admin/BenefitEdit";
import BenefitDetails from "./pages/admin/BenefitDetails";
import QuoteList from "./pages/admin/QuoteList";
import QuoteDetails from "./pages/admin/QuoteDetails";
// Broker pages
import BrokerDashboard from "./pages/broker/Dashboard";
import ClientList from "./pages/broker/ClientList";
import ClientCreate from "./pages/broker/ClientCreate";
import ClientDetails from "./pages/broker/ClientDetails";
import VehicleCreate from "./pages/broker/VehicleCreate";
import VehicleDetails from "./pages/broker/VehicleDetails";
import VehicleEdit from "./pages/broker/VehicleEdit";
import QuoteCreate from "./pages/broker/QuoteCreate";
import BrokerQuoteDetails from "./pages/broker/QuoteDetails";
import PolicyCatalog from "./pages/broker/PolicyCatalog";
import Profile from "./pages/broker/Profile";
import ComponentDemo from "./pages/ComponentDemo";
import Login from "./pages/Login";
import BrokerEdit from "./pages/admin/BrokerEdit";
import AdminDetailsPage from "./pages/admin/AdminDetails";
import AdminEditPage from "./pages/admin/AdminEdit";
import ProfileEdit from "./pages/broker/ProfileEdit";
import BrokerPolicyDetails from "./pages/broker/PolicyDetails";
import ClientEdit from "./pages/broker/ClientEdit";
import VehicleView from "./pages/broker/VehicleView";
import ChangePassword    from "./pages/broker/ChangePassword.jsx";
import VehicleRecommendations from "./pages/broker/VehicleRecommendations";
import VehiclePreferencesForm from "./pages/broker/VehiclePreferencesForm";

function AppLayout() {
  const location = useLocation();
  const hideSidebar = location.pathname === "/";
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const toggleSidebar = () => setIsSidebarCollapsed((v) => !v);
  return (
    <div className="min-h-screen flex bg-gray-100">
      {!hideSidebar && (
        <Sidebar
          isSidebarCollapsed={isSidebarCollapsed}
          toggleSidebar={toggleSidebar}
        />
      )}
      <div
        className={`flex-1 overflow-x-hidden p-4 ${
          !hideSidebar
            ? isSidebarCollapsed
              ? "md:ml-20"
              : "md:ml-64"
            : ""
        }`}
      >
        <Routes>
          <Route element={<AuthRoutes />}>
            {" "}
          {/* Admin Pages */}

            <Route element={<AdminRoutes />}>
                {" "}
                <Route path="/admin/dashboard" element={<AdminDashboard />} />
                <Route path="/admin/brokers" element={<BrokerList />} />
                <Route path="/admin/admin-list" element={<AdminList />} />
                <Route path="/admin/user-registration" element={<UserRegistration />} />
                <Route path="/admin/brokers/:id" element={<BrokerDetails />} />
                <Route path="/admin/brokers/:id/edit" element={<BrokerEdit />} />
                <Route path="/admin/insurers" element={<InsurerList />} />
                <Route path="/admin/insurers/create" element={<InsurerCreate />} />
                <Route path="/admin/insurers/:id/edit" element={<InsurerEdit />} />
                <Route path="/admin/insurers/:id" element={<InsurerDetails />} />
                <Route path="/admin/products" element={<ProductList />} />
                <Route path="/admin/products/create" element={<ProductCreate />} />
                <Route path="/admin/products/:id" element={<ProductDetails />} />
                <Route path="/admin/cover-types" element={<CoverTypeList />} />
                <Route
                  path="/admin/cover-types/create"
                  element={<CoverTypeCreate />}
                />
                <Route path="/admin/cover-types/:id/edit" element={<CoverTypeEdit />} />
                <Route path="/admin/cover-types/:id" element={<CoverTypeDetails />} />
                <Route path="/admin/policies" element={<PolicyList />} />
                <Route path="/admin/policies/create" element={<PolicyCreate />} />
                <Route path="/admin/policies/:id/edit" element={<PolicyEdit />} />
                <Route path="/admin/policies/:id" element={<PolicyDetails />} />
                <Route path="/admin/benefits" element={<BenefitList />} />
                <Route path="/admin/benefits/create" element={<BenefitCreate />} />
                <Route path="/admin/benefits/:id/edit" element={<BenefitEdit />} />
                <Route path="/admin/benefits/:id" element={<BenefitDetails />} />
                <Route path="/admin/quotes" element={<QuoteList />} />
                <Route path="/admin/quotes/:id" element={<QuoteDetails />} />
                <Route path="/admin/admin-list/:id" element={<AdminDetailsPage />} />
                <Route path="/admin/admin-list/:id/edit" element={<AdminEditPage />} />
            </Route>
          {/* Broker Pages */}
              <Route element={<BrokerRoutes />}>
                  {" "}
          <Route path="/broker/dashboard" element={<BrokerDashboard />} />
          <Route path="/broker/clients" element={<ClientList />} />
          <Route path="/broker/clients/create" element={<ClientCreate />} />
          <Route path="/broker/clients/:id" element={<ClientDetails />} />
          <Route path="/broker/clients/:id/edit" element={<ClientEdit />} />
          <Route path="/broker/vehicles/create" element={<VehicleCreate />} />
          <Route path="/broker/vehicles/details" element={<VehicleDetails />} />
          <Route path="/broker/vehicles/details/:id" element={<VehicleDetails />} />
          <Route path="/broker/vehicles/:id" element={<VehicleView />} />
          <Route path="/broker/quotes/create" element={<QuoteCreate />} />
          <Route path="/broker/quotes/:id" element={<BrokerQuoteDetails />} />
          <Route path="/broker/policy-catalog" element={<PolicyCatalog />} />
          <Route path="/broker/profile" element={<Profile />} />
          <Route path="/broker/profile/edit" element={<ProfileEdit />} />
          <Route path="/broker/policies/:id" element={<BrokerPolicyDetails />} />
          <Route path="/broker/vehicles/edit" element={<VehicleEdit />} />
          <Route path="/broker/change-password" element={<ChangePassword />} />
          <Route path="/broker/vehicles/recommendations" element={<VehicleRecommendations />} />
          <Route path="/broker/vehicles/preferences" element={<VehiclePreferencesForm />} />
              </Route>
              <Route path="*" element={<p>404 Error - Nothing here...</p>} />
          </Route>

            <Route element={<GuestRoutes />}>
                {" "}
                <Route path="/component-demo" element={<ComponentDemo />} />
                {/* Login */}
                <Route path="/" element={<Login />} />
            </Route>
        </Routes>
      </div>
    </div>
  );
}

function App() {
  return (
      <Router>
        <AuthProvider>
            <AppLayout />
        </AuthProvider>
      </Router>
  );
}

export default App;
