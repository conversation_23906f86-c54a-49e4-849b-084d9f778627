import React, { useState, useRef } from "react";
import <PERSON><PERSON> from "../../components/Alert";
import Button from "../../components/Button";
import Card from "../../components/Card";
import PageHeader from "../../components/PageHeader";
import FormWizard from "../../components/FormWizard";
import { userService } from '../../services/userService.js'
import Modal from "../../components/Modal.jsx";

const wizardSteps = [
  {
    title: "Personal Information",
    description: "Enter the user's personal details",
    fields: [
      { name: "first_name", label: "First Name", type: "text", required: true },
      { name: "last_name", label: "Last Name", type: "text", required: true },
      { name: "id_number", label: "ID Number", type: "text", required: true },
      {
        name: "role_id",
        label: "Role",
        type: "select",
        options: [
          { value: 2, label: "Broker" },
          { value: 1, label: "Admin" },
        ],
        required: true,
      },
    ],
    validation: (data, errors) => {
      if (!data?.first_name) errors.first_name = "First name is required";
      if (!data?.last_name) errors.last_name = "Last name is required";
      if (!data?.id_number) errors.id_number = "ID number is required";
      if (!data?.role_id) errors.role_id = "Role is required";
    },
  },
  {
    title: "Contact Information",
    description: "Enter the user's contact details",
    fields: [
      { name: "phone_number", label: "Phone Number", type: "tel", required: true },
      { name: "email", label: "Email", type: "email", required: true },
    ],
    validation: (data, errors) => {
      if (!data.phone_number) errors.phone_number = "Phone number is required";
      if (!data.email) errors.email = "Email is required";
    },
  },
];

const UserRegistration = () => {
  const [alert, setAlert] = useState({ show: false, type: "info", title: "", message: "" });
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  const [externalErrors, setExternalErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const handleWizardComplete =async (userData) => {
    try {
      setLoading(true);
      userService.createUser({userData, setExternalErrors, setLoading, setSuccess});
      if(success) {
        setAlert({
          show: true,
          type: "success",
          title: "Success",
          message: "User registered successfully!",
        });
        // Redirect based on role_id
        if (userData && userData?.role_id === 1) {
          setTimeout(() => navigate("/admin/admin-list"), 1000);
        } else {
          setTimeout(() => navigate("/admin/brokers"), 1000);
        }
      }
    }
    catch (error) {
      setLoading(false);
      if (error?.response?.status === 422 && error.response.data?.errors) {
        const backendErrors = {};
        const apiErrors = error.response.data.errors;

        for (const key in apiErrors) {
          backendErrors[key] = apiErrors[key][0];
        }

        setExternalErrors(backendErrors); // pass to wizard
      }
      else {
        setAlert({
          show: true,
          type: "error",
          title: "Error",
          message: "Something went wrong. Please try again.",
        });
      }
    }

  };

  const navigate = (url) => {
    window.location.href = url;
  };

  // Close dropdown on click outside
  React.useEffect(() => {
    if (!dropdownOpen) return;
    const handleClick = (e) => {
      if (dropdownRef.current && !dropdownRef.current.contains(e.target)) {
        setDropdownOpen(false);
      }
    };
    window.addEventListener("mousedown", handleClick, true);
    return () => window.removeEventListener("mousedown", handleClick, true);
  }, [dropdownOpen]);
/*if (loading) {
   setAlert({
          show: true,
          type: "success",
          title: "Success",
          message: "loading",
        });
}*/
  return (


    <div className="min-h-screen bg-gray-100 flex flex-col sm:flex-row">
      <Modal
          isOpen={loading}
          // onClose={() => setShowDeleteModal(false)}
          title="Delete Broker"
          size="small"
      >
        <div className="space-y-4">
          <p> Loading</p>
          <div className="flex justify-end gap-2">
          </div>
        </div>
      </Modal>
      <div className="flex-1 flex flex-col items-center justify-center py-8 px-2 w-full">
        <div className="w-full max-w-7xl mx-auto flex flex-col items-center">
          <PageHeader
            title="User Registration"
            subtitle="Register users and manage admins and brokers"
            breadcrumbs={[
              { label: "Dashboard", href: "/insurance" },
              { label: "User Management" },
            ]}
            className="w-full"
            actions={[
              {
                label: (
                  <span className="flex items-center gap-2">
                    View Users
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
                    </svg>
                  </span>
                ),
                variant: "primary",
                onClick: () => setDropdownOpen((v) => !v),
                ref: dropdownRef,
                id: "view-users-dropdown-btn"
              },
            ]}
          />
          {/* Dropdown menu for View Users */}
          {dropdownOpen && (
            <div
              ref={dropdownRef}
              className="absolute z-[9999] mt-2 right-8 bg-white border border-gray-200 rounded-md shadow-lg w-44"
              style={{ top: 90 }}
            >
              <button
                className="w-full text-left px-4 py-2 hover:bg-gray-100"
                onClick={() => { setDropdownOpen(false); navigate("/admin/brokers"); }}
              >
                Brokers
              </button>
              <button
                className="w-full text-left px-4 py-2 hover:bg-gray-100"
                onClick={() => { setDropdownOpen(false); navigate("/admin/admin-list"); }}
              >
                Admins
              </button>
            </div>
          )}
          <div className="h-8" />
          {/* Alert below header, above form */}
          {alert.show && (
            <div className="mb-4 w-full max-w-2xl mx-auto">
              <Alert
                type={alert.type}
                title={alert.title}
                message={alert.message}
                onClose={() => setAlert({ ...alert, show: false })}
              />f
            </div>
          )}
          {/* Registration Form */}
          <div className="w-full flex justify-center">
            <div className="w-full max-w-7xl">
              <Card
                title="FormWizard Component"
                subtitle="Step-by-step form wizard"
                className="w-full"
              >
                <FormWizard
                  steps={wizardSteps}
                  onComplete={handleWizardComplete}
                  onCancel={() => navigate("/insurance")}
                  externalErrors={externalErrors}
                />
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
      );
};

export default UserRegistration;