import React from "react";
import Card from "./Card";
import { dashboardStatsStyles } from "../Styles/uiTheme";

const DashboardStats = ({ stats }) => {
  const {
    totalCompanies = 0,
    totalProducts = 0,
    totalPolicies = 0,
    activeCompanies = 0,
    totalRevenue = 0,
    pendingApprovals = 0,
    monthlyGrowth = 0,
    customerSatisfaction = 0,
  } = stats;

  const statCards = [
    {
      title: "Total Companies",
      value: totalCompanies,
      icon: "🏢",
      color: "blue",
      trend: monthlyGrowth > 0 ? "up" : "down",
      trendValue: `${Math.abs(monthlyGrowth)}%`,
      description: "Registered insurance companies",
    },
    {
      title: "Total Products",
      value: totalProducts,
      icon: "📦",
      color: "green",
      trend: "up",
      trendValue: "+12%",
      description: "Available insurance products",
    },
    {
      title: "Active Policies",
      value: totalPolicies,
      icon: "📄",
      color: "purple",
      trend: "up",
      trendValue: "+8%",
      description: "Currently active policies",
    },
    {
      title: "Active Insurers",
      value: activeCompanies,
      icon: "✅",
      color: "yellow",
      trend: "up",
      trendValue: "+5%",
      description: "Companies with active status",
    },
    {
      title: "Total Revenue",
      value: `$${totalRevenue.toLocaleString()}`,
      icon: "💰",
      color: "emerald",
      trend: "up",
      trendValue: "+15%",
      description: "Monthly revenue generated",
    },
    {
      title: "Pending Approvals",
      value: pendingApprovals,
      icon: "⏳",
      color: "orange",
      trend: "down",
      trendValue: "-3%",
      description: "Awaiting approval",
    },
    {
      title: "Customer Satisfaction",
      value: `${customerSatisfaction}%`,
      icon: "😊",
      color: "pink",
      trend: "up",
      trendValue: "+2%",
      description: "Average satisfaction score",
    },
    {
      title: "Monthly Growth",
      value: `${monthlyGrowth}%`,
      icon: "📈",
      color: "indigo",
      trend: monthlyGrowth > 0 ? "up" : "down",
      trendValue: monthlyGrowth > 0 ? "+2.5%" : "-1.2%",
      description: "Month-over-month growth",
    },
  ];

  const { colorMap } = dashboardStatsStyles;
  const getColorClasses = (color) => {
    return colorMap[color] || colorMap.blue;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">
          Dashboard Overview
        </h2>
        <div className="text-sm text-gray-500">
          Last updated: {new Date().toLocaleDateString()}
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <Card
            key={index}
            className="hover:shadow-lg transition-shadow duration-200"
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-500">
                  {stat.title}
                </p>
                <p className="text-2xl font-bold text-gray-900 mt-1">
                  {stat.value}
                </p>
                <p className="text-xs text-gray-500 mt-1">{stat.description}</p>

                {/* Trend indicator */}
                <div className="flex items-center mt-2">
                  <span
                    className={`text-xs font-medium ${
                      stat.trend === "up" ? "text-green-600" : "text-red-600"
                    }`}
                  >
                    {stat.trend === "up" ? "↗" : "↘"} {stat.trendValue}
                  </span>
                  <span className="text-xs text-gray-500 ml-1">
                    from last month
                  </span>
                </div>
              </div>

              <div
                className={`flex-shrink-0 p-3 rounded-full border ${getColorClasses(
                  stat.color
                )}`}
              >
                <span className="text-2xl">{stat.icon}</span>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card title="Quick Actions" className="mt-8">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors">
            <span className="text-2xl mb-2">➕</span>
            <span className="text-sm font-medium text-gray-700">
              Add Company
            </span>
          </button>
          <button className="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-green-300 hover:bg-green-50 transition-colors">
            <span className="text-2xl mb-2">📦</span>
            <span className="text-sm font-medium text-gray-700">
              New Product
            </span>
          </button>
          <button className="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-purple-300 hover:bg-purple-50 transition-colors">
            <span className="text-2xl mb-2">📋</span>
            <span className="text-sm font-medium text-gray-700">
              View Reports
            </span>
          </button>
          <button className="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-orange-300 hover:bg-orange-50 transition-colors">
            <span className="text-2xl mb-2">⚙️</span>
            <span className="text-sm font-medium text-gray-700">Settings</span>
          </button>
        </div>
      </Card>
    </div>
  );
};

export default DashboardStats;
