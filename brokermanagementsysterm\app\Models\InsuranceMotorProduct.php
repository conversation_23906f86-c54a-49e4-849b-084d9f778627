<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class InsuranceMotorProduct extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'IBMS_INSURANCE_MOTOR_PRODUCTS';

    protected $primaryKey = 'id';

    protected $fillable = [
        'insurance_id',
        'category_id',
        'category_id',
        'policy_id',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    // Relationships
    public function insurance(): BelongsTo
    {
        return $this->belongsTo(Insurance::class, 'insurance_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(CategoryProduct::class, 'category_id');
    }

    public function policy(): BelongsTo
    {
        return $this->belongsTo(CategoryProductPolicy::class, 'policy_id');
    }
}
