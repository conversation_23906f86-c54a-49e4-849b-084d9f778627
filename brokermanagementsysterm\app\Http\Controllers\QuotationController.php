<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Quotation;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class QuotationController extends Controller
{
    protected function adminAuthorizedToQuote(array $validated): bool
    {
        $client = Client::find($validated['client_id']);
        $broker = User::find($validated['broker_id']);
        $vehicle = Vehicle::find($validated['vehicle_id']);

        return $client && $broker && $vehicle &&
            $broker->isBroker() &&
            $client->broker_id === $broker->user_id &&
            $vehicle->client_id === $client->client_id;
    }

    public function brokerAuthorisedToQuote(array $validated): bool
    {
        $client = Client::find($validated['client_id']);
        $broker = User::find($validated['broker_id']);
        $vehicle = Vehicle::find($validated['vehicle_id']);

        return $client && $broker && $vehicle &&
            $broker->isBroker() &&
            $client->broker_id === $broker->user_id &&
            $vehicle->client_id === $client->client_id;
    }

    public function store(Request $request): JsonResponse
    {

        $current_user = $request->user();

        if ($current_user->isBroker()) {
            $validatedData = $request->validate([
                'client_id' => ['required', 'uuid', 'exists:IBMS_CLIENTS,client_id'],
                'vehicle_id' => ['required', 'integer', 'exists:IBMS_VEHICLES,vehicle_id'],
                'insurance_category_product_id' => ['required', 'uuid', 'exists:IBMS_INSURANCE_CATEGORY_PRODUCTS,insurance_category_product_id'],
                'status' => ['required', 'in:pending,accepted,rejected'],
                'total_price' => ['nullable', 'numeric'],
                'coverage_details' => ['nullable', 'array'],
                'valid_until' => ['nullable', 'date'],
            ]);
            $validatedData['broker_id'] = $current_user->user_id;

            if ($this->brokerAuthorisedToQuote($validatedData)) {
                try {
                    $quotation = Quotation::create($validatedData);

                    return response()->json([
                        'status' => 'success',
                        'message' => 'Quotation created successfully',
                        'data' => $quotation,
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Failed to create quotation', ['error' => $e->getMessage()]);

                    return response()->json([
                        'status' => 'error',
                        'message' => 'An error occurred while creating the quotation. Please contact the administrator.',
                        'errors' => [$e->getMessage()],
                    ], 422);
                }
            }

            return response()->json(['message' => 'Unauthorized'], 403);
        } elseif ($current_user->isAdmin()) {
            $validatedData = $request->validate([
                'client_id' => ['required', 'uuid', 'exists:IBMS_CLIENTS,client_id'],
                'vehicle_id' => ['required', 'integer', 'exists:IBMS_VEHICLES,vehicle_id'],
                'broker_id' => ['required', 'uuid', 'exists:IBMS_USERS,user_id'],
                'insurance_category_product_id' => ['required', 'uuid', 'exists:IBMS_INSURANCE_CATEGORY_PRODUCTS,insurance_category_product_id'],
                'status' => ['required', 'in:pending,accepted,rejected'],
                'total_price' => ['nullable', 'numeric'],
                'coverage_details' => ['nullable', 'array'],
                'valid_until' => ['nullable', 'date'],
            ]);

            if ($this->adminAuthorizedToQuote($validatedData)) {
                try {
                    $quotation = Quotation::create($validatedData);

                    return response()->json([
                        'status' => 'success',
                        'message' => 'Quotation created successfully',
                        'data' => $quotation,
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Failed to create quotation', ['error' => $e->getMessage()]);

                    return response()->json([
                        'status' => 'error',
                        'message' => 'An error occurred while creating the quotation. Please contact the administrator.',
                        'errors' => [$e->getMessage()],
                    ], 422);
                }
            }

            return response()->json(['message' => 'Unauthorized'], 403);

        } else {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

    }

    public function createFromRecommendation(Request $request): JsonResponse
    {
        $current_user = $request->user();

        if ($current_user->isBroker()) {
            $validated = $request->validate([
                'client_id' => ['required', 'uuid', 'exists:IBMS_CLIENTS,client_id'],

                'recommendation' => ['required', 'array'],
                'recommendation.vehicle_id' => ['required', 'integer', 'exists:IBMS_VEHICLES,vehicle_id'],
                'recommendation.product_id' => ['required', 'uuid', 'exists:IBMS_INSURANCE_CATEGORY_PRODUCTS,insurance_category_product_id'],
                'recommendation.price' => ['required', 'numeric'],
                'recommendation.cover_type' => ['nullable', 'string'],
                'recommendation.cover_description' => ['nullable', 'string'],
                'recommendation.fitness_score' => ['nullable', 'numeric'],
            ]);
            $validated['broker_id'] = $current_user->user_id;

            $validated['vehicle_id'] = $validated['recommendation']['vehicle_id'];
            $validated['insurance_category_product_id'] = $validated['recommendation']['product_id'];
            $validated['total_price'] = $validated['recommendation']['price'];
            $validated['coverage_details'] = [
                'cover_type' => $validated['recommendation']['cover_type'] ?? null,
                'cover_description' => $validated['recommendation']['cover_description'] ?? null,
                'fitness_score' => $validated['recommendation']['fitness_score'] ?? null,
            ];
            $validated['status'] = 'pending';
            $validated['valid_until'] = now()->addDays(14);
            if ($this->brokerAuthorisedToQuote($validated)) {
                try {
                    $quote = Quotation::create([
                        'client_id' => $validated['client_id'],
                        'vehicle_id' => $validated['recommendation']['vehicle_id'],
                        'broker_id' => $validated['broker_id'],
                        'insurance_category_product_id' => $validated['recommendation']['product_id'],
                        'total_price' => $validated['recommendation']['price'],
                        'coverage_details' => [
                            'cover_type' => $validated['recommendation']['cover_type'] ?? null,
                            'cover_description' => $validated['recommendation']['cover_description'] ?? null,
                            'fitness_score' => $validated['recommendation']['fitness_score'] ?? null,
                        ],
                        'status' => 'pending',
                        'valid_until' => now()->addDays(14), // or any logic you want
                    ]);

                    return response()->json([
                        'status' => 'success',
                        'message' => 'Quotation created from recommendation.',
                        'data' => $quote,
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Failed to create quotation from recommendation', ['error' => $e->getMessage()]);

                    return response()->json([
                        'status' => 'error',
                        'message' => 'An error occurred while creating the quotation.',
                        'errors' => [$e->getMessage()],
                    ], 422);
                }
            }

            return response()->json(['message' => 'Unauthorised'], 403);

        } elseif ($current_user->isAdmin()) {
            $validated = $request->validate([
                'client_id' => ['required', 'uuid', 'exists:IBMS_CLIENTS,client_id'],
                'broker_id' => ['required', 'uuid', 'exists:IBMS_USERS,user_id'],
                'recommendation' => ['required', 'array'],
                'recommendation.vehicle_id' => ['required', 'integer', 'exists:IBMS_VEHICLES,vehicle_id'],
                'recommendation.product_id' => ['required', 'uuid', 'exists:IBMS_INSURANCE_CATEGORY_PRODUCTS,insurance_category_product_id'],
                'recommendation.price' => ['required', 'numeric'],
                'recommendation.cover_type' => ['nullable', 'string'],
                'recommendation.cover_description' => ['nullable', 'string'],
                'recommendation.fitness_score' => ['nullable', 'numeric'],
            ]);
            $validated['vehicle_id'] = $validated['recommendation']['vehicle_id'];
            $validated['insurance_category_product_id'] = $validated['recommendation']['product_id'];
            $validated['total_price'] = $validated['recommendation']['price'];
            $validated['coverage_details'] = [
                'cover_type' => $validated['recommendation']['cover_type'] ?? null,
                'cover_description' => $validated['recommendation']['cover_description'] ?? null,
                'fitness_score' => $validated['recommendation']['fitness_score'] ?? null,
            ];
            $validated['status'] = 'pending';
            $validated['valid_until'] = now()->addDays(14);

            if ($this->adminAuthorizedToQuote($validated)) {
                try {
                    $quote = Quotation::create([
                        'client_id' => $validated['client_id'],
                        'vehicle_id' => $validated['recommendation']['vehicle_id'],
                        'broker_id' => $validated['broker_id'],
                        'insurance_category_product_id' => $validated['recommendation']['product_id'],
                        'total_price' => $validated['recommendation']['price'],
                        'coverage_details' => [
                            'cover_type' => $validated['recommendation']['cover_type'] ?? null,
                            'cover_description' => $validated['recommendation']['cover_description'] ?? null,
                            'fitness_score' => $validated['recommendation']['fitness_score'] ?? null,
                        ],
                        'status' => 'pending',
                        'valid_until' => now()->addDays(14), // or any logic you want
                    ]);

                    return response()->json([
                        'status' => 'success',
                        'message' => 'Quotation created from recommendation.',
                        'data' => $quote,
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Failed to create quotation from recommendation', ['error' => $e->getMessage()]);

                    return response()->json([
                        'status' => 'error',
                        'message' => 'An error occurred while creating the quotation.',
                        'errors' => [$e->getMessage()],
                    ], 422);
                }
            }

            return response()->json(['message' => 'Unauthorised'], 403);
        }
    }

    public function edit(Request $request, $quotation_id): JsonResponse
    {
        $current_user = $request->user();

        $quotation = Quotation::with('client')->findOrFail($quotation_id);

        if ($current_user->isBroker()) {
            // Ensure the quotation belongs to the broker's client
            if ($quotation->client->broker_id !== $current_user->user_id) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'You are not authorized to access this quotation.',
                ], 403);
            }
        }

        $validatedData = $request->validate([
            'insurance_category_product_id' => ['required', 'uuid', 'exists:IBMS_INSURANCE_CATEGORY_PRODUCTS,insurance_category_product_id'],
            'status' => ['required', 'in:pending,accepted,rejected'],
            'total_price' => ['nullable', 'numeric'],
            'coverage_details' => ['nullable', 'array'],
            'valid_until' => ['nullable', 'date'],
        ]);

        return response()->json([
            'status' => 'success',
            'data' => $quotation,
        ]);
    }

    public function show(Request $request, $quotation_id): JsonResponse
    {
        $current_user = $request->user();

        $quotation = Quotation::with('client')->findOrFail($quotation_id);
        if ($current_user->isBroker()) {
            if ($quotation->client->broker_id == $current_user->user_id) {
                return response()->json($quotation);
            }

            return response()->json(['message' => 'Quote Not Found'], 404);
        } elseif ($current_user->isAdmin()) {
            return response()->json($quotation);
        } else {
            return response()->json(['message' => 'Unauthorised'], 403);
        }
    }

    public function index(Request $request)
    {
        $current_user = $request->user();

        if ($current_user->isBroker()) {
            $quotations = Quotation::whereHas('client', function ($query) use ($current_user) {
                $query->where('broker_id', $current_user->user_id);
            })->get();

            return response()->json([
                'status' => 'success',
                'data' => $quotations,
            ]);
        } elseif ($current_user->isAdmin()) {
            $quotations = Quotation::all();

            return response()->json($quotations);
        } else {
            return response()->json(['message' => 'Unauthorised'], 403);
        }
    }

    public function acceptQuote(Request $request, $quotation_id): JsonResponse
    {
        $current_user = $request->user();

        $quotation = Quotation::with('client')->findOrFail($quotation_id);

        if ($current_user->isBroker()) {
            if ($quotation->client->broker_id == $current_user->user_id) {
                if ($quotation->status === 'accepted') {
                    return response()->json([
                        'status' => 'info',
                        'message' => 'This quotation has already been accepted.',
                    ]);
                }
                try {
                    $quotation->update([
                        'status' => 'accepted',
                    ])
                        ->save();

                    return response()->json([$quotation]);
                } catch (\Exception $e) {
                    return response()->json(
                        [
                            'status' => 'error',
                            'message' => 'Failed to accept quotation. Please try again or contact support.',
                            'errors' => [$e->getMessage()],
                        ], 422);
                }
            }

            return response()->json(['message' => 'Unauthorised'], 403);
        } elseif ($current_user->isAdmin()) {
            if ($quotation->status === 'accepted') {
                return response()->json([
                    'status' => 'info',
                    'message' => 'This quotation has already been accepted.',
                ]);
            }
            try {
                $quotation->update([
                    'status' => 'accepted',
                ])
                    ->save();

                return response()->json([$quotation]);
            } catch (\Exception $e) {
                return response()->json(
                    [
                        'status' => 'error',
                        'message' => 'Failed to accept quotation. Please try again or contact support.',
                        'errors' => [$e->getMessage()],
                    ], 422);
            }

        } else {
            return response()->json(['message' => ''], 403);
        }

    }

    public function destroy(Request $request, $quotation_id)
    {
        $current_user = $request->user();
        $quotation = Quotation::with('client')->findOrFail($quotation_id);

        if ($current_user->isAdmin()) {
            $quotation->delete();

            return response()->json(['message' => ''], 200);
        } elseif ($current_user->isBroker()) {
            if ($quotation->client->broker_id == $current_user->user_id) {
                $quotation->delete();

                return response()->json(['message' => ''], 200);
            }

            return response()->json(['message' => 'Unauthorised'], 403);
        } else {
            return response()->json(['message' => ''], 403);
        }
    }
}
