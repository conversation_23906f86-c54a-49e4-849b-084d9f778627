import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import DataTable from "../../components/DataTable";
import PageHeader from "../../components/PageHeader";
import { mockQuotes } from "../../services/mockData";
import Button from "../../components/Button";
import Modal from "../../components/Modal";
import Alert  from "../../components/Alert";

export default function QuoteList() {
  const navigate = useNavigate();
  const [quotes] = useState(mockQuotes);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [quoteToDelete, setQuoteToDelete] = useState(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [alertMessage, setAlertMessage] = useState("");

  const showSuccessAlert = (message) => {
    setAlertMessage(message);
    setAlertType("success");
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  const columns = [
    {
      key: "quoteNumber",
      label: "Quote Number",
      sortable: true,
    },
    {
      key: "clientName",
      label: "Client Name",
      sortable: true,
    },
    {
      key: "clientEmail",
      label: "Client Email",
      sortable: true,
    },
    {
      key: "vehicleDetails",
      label: "Vehicle",
      sortable: false,
      render: (vehicleDetails) => (
        <div className="text-sm">
          <div className="font-medium">{vehicleDetails.registrationNumber}</div>
          <div className="text-gray-500">
            {vehicleDetails.make} {vehicleDetails.model} ({vehicleDetails.year})
          </div>
        </div>
      ),
    },
    {
      key: "insurerName",
      label: "Insurance Company",
      sortable: true,
    },
    {
      key: "policyName",
      label: "Policy",
      sortable: true,
      render: (policyName, quote) => (
        <div className="text-sm">
          <div className="font-medium">{policyName}</div>
          <div className="text-gray-500">{quote.coverTypeName}</div>
        </div>
      ),
    },
    {
      key: "premiumAmount",
      label: "Premium",
      sortable: true,
      type: "currency",
    },
    {
      key: "status",
      label: "Status",
      sortable: true,
      type: "status",
    },
    {
      key: "createdAt",
      label: "Created Date",
      sortable: true,
      type: "date",
    },
  ];

  const handleViewQuote = (quote) => {
    navigate(`/admin/quotes/${quote.id}`);
  };

  return (
    <div className="w-full">
      <PageHeader
        title="Quote Management"
        subtitle="View and manage all insurance quotes"
      />
      {showAlert && (
        <Alert
          type={alertType}
          title={alertType === "success" ? "Success" : "Error"}
          message={alertMessage}
          onClose={() => setShowAlert(false)}
        />
      )}
      <div className="mt-6">
        <DataTable
          data={quotes}
          columns={columns}
          onView={handleViewQuote}
          searchable={true}
          filterable={true}
          pagination={true}
          itemsPerPage={10}
          className="w-full"
        />
      </div>

      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete Quote"
        size="small"
      >
        <div className="space-y-4">
          <p>Are you sure you want to delete quote <span className="font-semibold">{quoteToDelete?.quoteNumber}</span>?</p>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setShowDeleteModal(false)}>Cancel</Button>
            <Button variant="danger" onClick={() => { setShowDeleteModal(false); showSuccessAlert(`Quote ${quoteToDelete?.quoteNumber} deleted successfully!`); }}>Delete</Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
