import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { PageHeader, Card, Alert } from "../../components";
import { vehicleService } from "../../services/vehicleService";

export default function VehicleEdit() {
  const location = useLocation();
  const navigate = useNavigate();
  const vehicle = location.state?.vehicle || {};
  const client = location.state?.client || null;
  const [formData, setFormData] = useState({ ...vehicle });
  const [alert, setAlert] = useState({ show: false, type: "info", title: "", message: "" });
  const [submitting, setSubmitting] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log("Form submitted, starting update process...");
    setSubmitting(true);
    setAlert({ show: false, type: "info", title: "", message: "" });

    // Safety check: Ensure vehicle has vehicle_id
    if (!vehicle.vehicle_id) {
      console.log("Vehicle ID missing:", vehicle);
      setAlert({
        show: true,
        type: "error",
        title: "Update Failed",
        message: "Vehicle ID is missing. Please go back and select the vehicle again.",
      });
      setSubmitting(false);
      return;
    }

    console.log("Vehicle ID found:", vehicle.vehicle_id);
    console.log("Form data to update:", formData);

    try {
      const allowedFields = ['make', 'model', 'registration_number', 'mileage', 'value', 'cover_type'];
      const updateData = {};

      // Only include allowed fields from formData
      allowedFields.forEach(field => {
        if (formData[field] !== undefined && formData[field] !== null) {
          updateData[field] = formData[field];
        }
      });

      // Remove cover_type if it's null, undefined, or empty string
      if (!updateData.cover_type || updateData.cover_type.trim() === '') {
        delete updateData.cover_type;
      }

      // Ensure numeric fields are properly formatted
      if (updateData.mileage) {
        updateData.mileage = parseInt(updateData.mileage);
      }
      if (updateData.value) {
        updateData.value = parseFloat(updateData.value);
      }

      const response = await vehicleService.updateVehicle(vehicle, updateData);
      console.log("Vehicle update response:", response);
      console.log("Update successful, setting alert...");

      setAlert({
        show: true,
        type: "success",
        title: "Vehicle Updated",
        message: `Vehicle ${formData.make} ${formData.model} updated successfully!`,
      });

      console.log("Alert set, current alert state:", { show: true, type: "success" });

      // Add a longer delay and log the navigation
      console.log("Setting timeout for navigation...");
      setTimeout(() => {
        console.log("Navigating to vehicle details...");
        navigate("/broker/vehicles/details", { state: { client } });
      }, 2000);
    } catch (error) {
      console.error("Vehicle update error:", error);
      console.error("Error response:", error?.response?.data);

      setAlert({
        show: true,
        type: "error",
        title: "Update Failed",
        message: error?.response?.data?.message || error.message || "Failed to update vehicle.",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center py-6 px-2 w-full">
      <div className="w-full max-w-4xl">
        <PageHeader
          title={`Edit Vehicle: ${formData.make || ""} ${formData.model || ""} (${formData.registration_number || ""})`}
          subtitle="Edit vehicle details"
          breadcrumbs={[
            { label: "Clients", href: "/broker/clients" },
            { label: "Vehicle List", href: "/broker/vehicles/details", state: { client } },
            { label: `Edit: ${formData.make || ""} ${formData.model || ""} (${formData.registration_number || ""})` },
          ].filter(Boolean)}
          actions={[
            {
              label: "Back to Vehicle List",
              variant: "primary",
              onClick: () => navigate("/broker/vehicles/details", { state: { client } }),
            },
          ]}
        />
        <div className="mt-8">
          <Card title="Edit Vehicle Details" subtitle="Update the vehicle information below">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1  gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Make</label>
                  <input
                    type="text"
                    name="make"
                    value={formData.make || ""}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Model</label>
                  <input
                    type="text"
                    name="model"
                    value={formData.model || ""}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Registration Number</label>
                  <input
                    type="text"
                    name="registration_number"
                    value={formData.registration_number || ""}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Purpose</label>
                  <select
                    name="vehicle_purpose"
                    value={formData.vehicle_purpose || ""}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    required
                  >
                    <option value="">Select Purpose</option>
                    <option value="Personal">Personal</option>
                    <option value="Commercial">Commercial</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Mileage</label>
                  <input
                    type="number"
                    name="mileage"
                    value={formData.mileage || ""}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Value</label>
                  <input
                    type="number"
                    name="value"
                    value={formData.value || ""}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    required
                  />
                </div>
              </div>
              <div className="flex justify-end gap-4">
                <button
                  type="button"
                  className="bg-gray-200 text-gray-700 px-6 py-2 rounded-lg font-semibold"
                  onClick={() => navigate("/broker/vehicles/details", { state: { client } })}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors duration-150 disabled:opacity-60 disabled:cursor-not-allowed"
                  disabled={submitting}
                >
                  {submitting ? "Saving..." : "Save Changes"}
                </button>
              </div>
            </form>
            {alert.show && (
              <div className="fixed top-5 right-2 sm:right-5 z-[2000] min-w-[250px] max-w-xs w-auto">
                <Alert
                  type={alert.type}
                  title={alert.title}
                  message={alert.message}
                  onClose={() => setAlert({ ...alert, show: false })}
                />
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}
