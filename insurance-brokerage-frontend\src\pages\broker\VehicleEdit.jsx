import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { PageHeader, Card, Alert } from "../../components";
import { vehicleService } from "../../services/vehicleService";

export default function VehicleEdit() {
  const location = useLocation();
  const navigate = useNavigate();
  const vehicle = location.state?.vehicle || {};
  const client = location.state?.client || null;
  const [formData, setFormData] = useState({ ...vehicle });
  const [alert, setAlert] = useState({ show: false, type: "info", title: "", message: "" });
  const [submitting, setSubmitting] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setAlert({ show: false, type: "info", title: "", message: "" });
    try {
      await vehicleService.updateVehicle(vehicle, formData);
      setAlert({
        show: true,
        type: "success",
        title: "Vehicle Updated",
        message: `Vehicle ${formData.make} ${formData.model} updated successfully!`,
      });
      setTimeout(() => navigate("/broker/vehicles/details", { state: { client } }), 1500);
    } catch (error) {
      setAlert({
        show: true,
        type: "error",
        title: "Update Failed",
        message: error?.response?.data?.message || error.message || "Failed to update vehicle.",
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center py-6 px-2 w-full">
      <div className="w-full max-w-4xl">
        <PageHeader
          title={`Edit Vehicle: ${formData.make || ""} ${formData.model || ""} (${formData.registration_number || ""})`}
          subtitle="Edit vehicle details"
          breadcrumbs={[
            { label: "Clients", href: "/broker/clients" },
            { label: "Vehicle List", href: "/broker/vehicles/details", state: { client } },
            { label: `Edit: ${formData.make || ""} ${formData.model || ""} (${formData.registration_number || ""})` },
          ].filter(Boolean)}
          actions={[
            {
              label: "Back to Vehicle List",
              variant: "primary",
              onClick: () => navigate("/broker/vehicles/details", { state: { client } }),
            },
          ]}
        />
        <div className="mt-8">
          <Card title="Edit Vehicle Details" subtitle="Update the vehicle information below">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1  gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Make</label>
                  <input
                    type="text"
                    name="make"
                    value={formData.make || ""}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Model</label>
                  <input
                    type="text"
                    name="model"
                    value={formData.model || ""}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Registration Number</label>
                  <input
                    type="text"
                    name="registration"
                    value={formData.registration_number || ""}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Usage</label>
                  <select
                    name="usage"
                    value={formData.usage || ""}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    required
                  >
                    <option value="">Select Usage</option>
                    <option value="Personal">Personal</option>
                    <option value="Commercial">Commercial</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Value</label>
                  <input
                    type="number"
                    name="value"
                    value={formData.value || ""}
                    onChange={handleChange}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                    required
                  />
                </div>
              </div>
              <div className="flex justify-end gap-4">
                <button
                  type="button"
                  className="bg-gray-200 text-gray-700 px-6 py-2 rounded-lg font-semibold"
                  onClick={() => navigate("/broker/vehicles/details", { state: { client } })}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors duration-150 disabled:opacity-60 disabled:cursor-not-allowed"
                  disabled={submitting}
                >
                  {submitting ? "Saving..." : "Save Changes"}
                </button>
              </div>
            </form>
            {alert.show && (
              <div className="fixed top-5 right-2 sm:right-5 z-[2000] min-w-[250px] max-w-xs w-auto">
                <Alert
                  type={alert.type}
                  title={alert.title}
                  message={alert.message}
                  onClose={() => setAlert({ ...alert, show: false })}
                />
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}
