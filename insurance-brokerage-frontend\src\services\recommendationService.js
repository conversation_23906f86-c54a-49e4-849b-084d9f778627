import axios from "../utils/axios";

// Mock data for products, preferences, and insurers
const mockProducts = [
  {
    insurance_category_product_id: 1,
    price: 12000,
    insurance_id: 101,
    vehicle_usage: "Personal",
    categoryProduct: {
      category_product_name: "Comprehensive",
      category_product_description: "Full coverage for your vehicle."
    },
    insurance: {
      name: "InsureCo"
    }
  },
  {
    insurance_category_product_id: 2,
    price: 9000,
    insurance_id: 102,
    vehicle_usage: "Commercial",
    categoryProduct: {
      category_product_name: "Third Party",
      category_product_description: "Covers third party damages."
    },
    insurance: {
      name: "SafeGuard"
    }
  },
  {
    insurance_category_product_id: 3,
    price: 10500,
    insurance_id: 101,
    vehicle_usage: "Personal",
    categoryProduct: {
      category_product_name: "Comprehensive",
      category_product_description: "Full coverage for your vehicle."
    },
    insurance: {
      name: "InsureCo"
    }
  },
  {
    insurance_category_product_id: 4,
    price: 15000,
    insurance_id: 103,
    vehicle_usage: "Personal",
    categoryProduct: {
      category_product_name: "Third Party Fire & Theft",
      category_product_description: "Covers third party, fire, and theft."
    },
    insurance: {
      name: "Jubilee Insurance"
    }
  },
  {
    insurance_category_product_id: 5,
    price: 11000,
    insurance_id: 104,
    vehicle_usage: "Commercial",
    categoryProduct: {
      category_product_name: "Comprehensive",
      category_product_description: "Comprehensive cover for commercial vehicles."
    },
    insurance: {
      name: "UAP Insurance"
    }
  },
  {
    insurance_category_product_id: 6,
    price: 9500,
    insurance_id: 105,
    vehicle_usage: "Personal",
    categoryProduct: {
      category_product_name: "Third Party",
      category_product_description: "Third party only for personal vehicles."
    },
    insurance: {
      name: "CIC Insurance"
    }
  },
  {
    insurance_category_product_id: 7,
    price: 17000,
    insurance_id: 106,
    vehicle_usage: "Personal",
    categoryProduct: {
      category_product_name: "Comprehensive",
      category_product_description: "Premium comprehensive cover."
    },
    insurance: {
      name: "Britam Insurance"
    }
  }
];

const mockPreferences = {
  preferred_cover_types: JSON.stringify(["Comprehensive", "Third Party"]),
  budget_min: 9000,
  budget_max: 13000,
  preferred_insurers: JSON.stringify([101]),
  vehicle: null // will be set dynamically
};

export const recommendationService = {
  getRecommendations(vehicle, client, limit = 5) {
    // Simulate fetching preferences for the client
    const prefs = { ...mockPreferences, vehicle };

    // Evaluate fitness for each product
    const scored = mockProducts.map(product => {
      const fitness = evaluateFitness(product, prefs);
      return { ...product, fitness };
    });

    // Sort, take top N, and map to output format
    return scored
      .sort((a, b) => b.fitness - a.fitness)
      .slice(0, limit)
      .map(product => ({
        vehicle_id: vehicle.vehicle_id,
        product_id: product.insurance_category_product_id,
        cover_type: product.categoryProduct?.category_product_name || 'N/A',
        cover_description: product.categoryProduct?.category_product_description || 'N/A',
        insurer_name: product.insurance?.name || 'MISSING',
        price: product.price || 0,
        fitness_score: Math.round(product.fitness * 1000) / 1000,
      }));
  },
  async createPreferences(client_id, preferences) {
    // preferences: { preferred_insurers, preferred_cover_types, preferred_channel, renewal_reminder_enabled, budget_min, budget_max }
    const payload = { client_id, ...preferences };
    const response = await axios.post(`/api/v1/preferences/${client_id}/${preferences.vehicle_id}`, payload);
    return response.data;
  },

  // Add this new method to connect to the backend API
  async getRecommendationsFromAPI(vehicle_id, client_id, limit = 5) {
    try {
      const response = await axios.get(`/api/v1/recommendation`, {
        params: {
          vehicle_id,
          client_id,
          limit
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching recommendations from API:', error);
      throw error;
    }
  }
};

function evaluateFitness(product, prefs) {
  let score = 0.0;
  const coverType = product.categoryProduct?.category_product_name || null;
  const preferredCovers = typeof prefs.preferred_cover_types === 'string'
    ? JSON.parse(prefs.preferred_cover_types)
    : prefs.preferred_cover_types;
  if (Array.isArray(preferredCovers) && coverType && preferredCovers.includes(coverType)) {
    score += 0.4;
  }
  if (
    prefs.budget_min !== undefined &&
    prefs.budget_max !== undefined &&
    product.price !== undefined &&
    !isNaN(product.price)
  ) {
    if (product.price >= prefs.budget_min && product.price <= prefs.budget_max) {
      score += 0.3;
    } else if (product.price <= (prefs.budget_max * 1.1)) {
      score += 0.1;
    }
  }
  const preferredInsurers = typeof prefs.preferred_insurers === 'string'
    ? JSON.parse(prefs.preferred_insurers)
    : (prefs.preferred_insurers || []);
  if (Array.isArray(preferredInsurers) && preferredInsurers.includes(product.insurance_id)) {
    score += 0.2;
  }
  if (
    product.vehicle_usage &&
    prefs.vehicle &&
    product.vehicle_usage === prefs.vehicle.vehicle_purpose
  ) {
    score += 0.1;
  }
  return score;
} 