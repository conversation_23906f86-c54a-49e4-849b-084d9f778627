<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('IBMS_CATEGORIES', function (Blueprint $table) {
            $table->uuid('category_id')->primary()->unique();
            $table->string('category_name')->default('MOTOR_INSURANCE');
            $table->string('category_description')->default('MOTOR INSURANCE PRODUCT');
            $table->timestamps();
            $table->softDeletes();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IBMS_CATEGORIES');
    }
};
