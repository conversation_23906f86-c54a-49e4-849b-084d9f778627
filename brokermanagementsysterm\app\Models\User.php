<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;

/**
 * @OA\Schema(
 *   schema="IBMSUser",
 *   type="object",
 *   required={"id", "first_name", "last_name", "email", "phone", "password", "role_id"},
 *
 *   @OA\Property(property="id", type="integer", format="int64"),
 *   @OA\Property(property="first_name", type="string"),
 *   @OA\Property(property="last_name", type="string"),
 *   @OA\Property(property="email", type="string", format="email"),
 *   @OA\Property(property="phone", type="string"),
 *   @OA\Property(property="verified_at", type="string", format="date-time", nullable=true),
 *   @OA\Property(property="email_verified", type="boolean", default=false),
 *   @OA\Property(property="password", type="string", format="password"),
 *   @OA\Property(property="last_seen", type="string", format="date-time"),
 *   @OA\Property(property="role_id", type="integer"),
 *   @OA\Property(property="remember_token", type="string"),
 *   @OA\Property(property="created_at", type="string", format="date-time"),
 *   @OA\Property(property="updated_at", type="string", format="date-time"),
 *   @OA\Property(property="deleted_at", type="string", format="date-time", nullable=true)
 * )
 */
class User extends Authenticatable
{
    use HasApiTokens, HasFactory, HasUuids, Notifiable, SoftDeletes;

    protected $table = 'IBMS_USERS';

    protected $primaryKey = 'user_id';

    public $incrementing = false;

    protected $keyType = 'string';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $guarded = [];

    //   protected  $fillable = [];
    /*protected $fillable = [
        'first_name',
        'last_name',
        'name',
        //] 'username',
        'role_id',
        'email',
        'email_verified',
        'password',
        'phone_number',
    ];*/

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'role_id' => 'integer',
        'email_verified' => 'boolean',
    ];

    public function role()
    {
        return $this::belongsTo(Role::class, 'role_id');
    }

    public function isAdmin(): bool
    {
        return $this->role->role_name === 'ADMIN';
    }

    public function isBroker(): bool
    {
        return $this->role->role_name === 'BROKER';
    }

    public function hasRequiredPermission(int $permission): bool
    {
        return $this->role->hasPermission($permission);
    }
}
