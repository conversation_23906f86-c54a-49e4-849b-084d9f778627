# Insurance Brokerage System Components

This directory contains all the reusable React components for the Insurance Brokerage System. All components are built with functional components and styled with Tailwind CSS.

## 📁 Component Structure

### Existing Components
- `FormFields.jsx` - Reusable form input components
- `StatsCard.jsx` - Statistics display cards
- `StatsOverview.jsx` - Overview statistics grid
- `StatusBadge.jsx` - Status indicators (enhanced)
- `SuccessMessage.jsx` - Success notification messages
- `searchbar.jsx` - Search input component
- `filter_dropdown.jsx` - Filter dropdown component
- `sidebar.jsx` - Navigation sidebar
- `tables.jsx` - Data table component

### New Components
- `Alert.jsx` - Alert/notification component
- `Button.jsx` - Reusable button component
- `Card.jsx` - Card layout component
- `DataTable.jsx` - Advanced data table with sorting/searching
- `DashboardStats.jsx` - Comprehensive dashboard statistics
- `FormWizard.jsx` - Step-by-step form wizard
- `Modal.jsx` - Modal dialog component
- `PageHeader.jsx` - Page header with breadcrumbs and actions

## 🎨 Design System

### Color Palette
- **Primary**: Blue (`blue-600`, `blue-700`)
- **Success**: Green (`green-600`, `green-700`)
- **Warning**: Yellow (`yellow-600`, `yellow-700`)
- **Error**: Red (`red-600`, `red-700`)
- **Neutral**: Gray (`gray-50` to `gray-900`)

### Typography
- **Headings**: `text-2xl font-bold` (Page titles)
- **Subheadings**: `text-lg font-semibold` (Section titles)
- **Body**: `text-sm` (Regular text)
- **Captions**: `text-xs` (Small text)

### Spacing
- **Container**: `p-6` (Standard padding)
- **Cards**: `p-6` (Card padding)
- **Buttons**: `px-4 py-2` (Button padding)
- **Forms**: `space-y-6` (Form field spacing)

## 📋 Component Documentation

### Alert Component
Displays different types of alerts and notifications.

```jsx
import { Alert } from '../components';

<Alert
  type="success" // 'info', 'success', 'warning', 'error'
  title="Success!"
  message="Operation completed successfully!"
  onClose={() => setShowAlert(false)}
  showIcon={true}
/>
```

### Button Component
Reusable button with multiple variants and sizes.

```jsx
import { Button } from '../components';

<Button
  variant="primary" // 'primary', 'secondary', 'success', 'danger', 'warning', 'outline', 'ghost'
  size="default" // 'small', 'default', 'large'
  onClick={handleClick}
  disabled={false}
  loading={false}
  icon="➕"
>
  Click Me
</Button>
```

### Card Component
Flexible card layout with header, content, and footer sections.

```jsx
import { Card } from '../components';

<Card
  title="Card Title"
  subtitle="Card subtitle"
  headerAction={<Button>Action</Button>}
  footer={<div>Footer content</div>}
  padding="default" // 'none', 'small', 'default', 'large'
  shadow="default" // 'none', 'small', 'default', 'large', 'xlarge'
  border={true}
>
  Card content goes here
</Card>
```

### DataTable Component
Advanced table with sorting, searching, and pagination.

```jsx
import { DataTable } from '../components';

const columns = [
  { key: 'name', label: 'Name', sortable: true },
  { key: 'status', label: 'Status', type: 'status', sortable: true },
  { key: 'date', label: 'Date', type: 'date', sortable: true },
  { key: 'amount', label: 'Amount', type: 'currency', sortable: true }
];

<DataTable
  data={data}
  columns={columns}
  onView={(item) => handleView(item)}
  onEdit={(item) => handleEdit(item)}
  onDelete={(item) => handleDelete(item)}
  searchable={true}
  pagination={true}
  itemsPerPage={10}
/>
```

### DashboardStats Component
Comprehensive dashboard overview with statistics and quick actions.

```jsx
import { DashboardStats } from '../components';

const stats = {
  totalCompanies: 25,
  totalProducts: 150,
  totalPolicies: 8500,
  activeCompanies: 22,
  totalRevenue: 1250000,
  pendingApprovals: 8,
  monthlyGrowth: 12.5,
  customerSatisfaction: 94
};

<DashboardStats stats={stats} />
```

### FormWizard Component
Step-by-step form wizard with validation and progress tracking.

```jsx
import { FormWizard } from '../components';

const steps = [
  {
    title: 'Step 1',
    description: 'Basic information',
    fields: [
      {
        name: 'name',
        label: 'Name',
        type: 'text',
        required: true
      }
    ],
    validation: (data, errors) => {
      if (!data.name) errors.name = 'Name is required';
    }
  }
];

<FormWizard
  steps={steps}
  onComplete={(formData) => console.log(formData)}
  onCancel={() => navigate('/')}
/>
```

### Modal Component
Modal dialog with backdrop and keyboard support.

```jsx
import { Modal } from '../components';

<Modal
  isOpen={showModal}
  onClose={() => setShowModal(false)}
  title="Modal Title"
  size="default" // 'small', 'default', 'large', 'xlarge'
>
  Modal content goes here
</Modal>
```

### PageHeader Component
Page header with breadcrumbs, actions, and optional statistics.

```jsx
import { PageHeader } from '../components';

<PageHeader
  title="Page Title"
  subtitle="Page description"
  breadcrumbs={[
    { label: 'Home', href: '/' },
    { label: 'Current Page' }
  ]}
  actions={[
    {
      label: 'Add New',
      variant: 'primary',
      onClick: handleAdd
    }
  ]}
  stats={[
    {
      label: 'Total Items',
      value: '150',
      icon: '📊',
      iconBgColor: 'bg-blue-100',
      iconColor: 'text-blue-600'
    }
  ]}
/>
```

### StatusBadge Component
Status indicators with different colors and sizes.

```jsx
import { StatusBadge } from '../components';

<StatusBadge
  status="Active" // 'Active', 'Inactive', 'Pending', 'Suspended', 'Approved'
  size="default" // 'small', 'default', 'large'
/>
```

## 🚀 Usage Examples

### Basic Form with Validation
```jsx
import { Card, FormField, Button, Alert } from '../components';

const MyForm = () => {
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});
  const [showAlert, setShowAlert] = useState(false);

  const handleSubmit = () => {
    // Validation logic
    if (validateForm()) {
      setShowAlert(true);
    }
  };

  return (
    <Card title="My Form">
      <FormField
        label="Name"
        name="name"
        value={formData.name}
        onChange={(e) => setFormData({...formData, name: e.target.value})}
        error={errors.name}
        required
      />
      <Button onClick={handleSubmit}>Submit</Button>
      
      {showAlert && (
        <Alert
          type="success"
          message="Form submitted successfully!"
          onClose={() => setShowAlert(false)}
        />
      )}
    </Card>
  );
};
```

### Data Table with Actions
```jsx
import { DataTable, Button, Modal } from '../components';

const MyTable = () => {
  const [showModal, setShowModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  const handleView = (item) => {
    setSelectedItem(item);
    setShowModal(true);
  };

  return (
    <>
      <DataTable
        data={data}
        columns={columns}
        onView={handleView}
        onEdit={(item) => console.log('Edit:', item)}
        onDelete={(item) => console.log('Delete:', item)}
      />
      
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="Item Details"
      >
        {selectedItem && (
          <div>
            <p>Name: {selectedItem.name}</p>
            <p>Status: <StatusBadge status={selectedItem.status} /></p>
          </div>
        )}
      </Modal>
    </>
  );
};
```

## 🎯 Best Practices

1. **Consistent Styling**: Always use the predefined color palette and spacing
2. **Accessibility**: Include proper ARIA labels and keyboard navigation
3. **Responsive Design**: Ensure components work on all screen sizes
4. **Error Handling**: Provide clear error messages and validation feedback
5. **Loading States**: Show loading indicators for async operations
6. **Type Safety**: Use proper prop types and validation

## 🔧 Development

### Adding New Components
1. Create the component file in the `components` directory
2. Follow the existing naming conventions
3. Use Tailwind CSS for styling
4. Add proper JSDoc comments
5. Export from `index.js`
6. Add to the component demo page

### Testing Components
1. Test all variants and states
2. Verify responsive behavior
3. Check accessibility features
4. Test with different data types
5. Validate error handling

## 📱 Responsive Design

All components are built with mobile-first responsive design:
- **Mobile**: Single column layouts, stacked elements
- **Tablet**: Two-column grids where appropriate
- **Desktop**: Multi-column layouts, side-by-side elements

## ♿ Accessibility Features

- **Keyboard Navigation**: All interactive elements are keyboard accessible
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Color Contrast**: Meets WCAG AA standards
- **Focus Management**: Clear focus indicators and logical tab order
- **Error Announcements**: Screen reader announcements for errors

## 🎨 Customization

Components can be customized through:
- **Props**: Pass different variants, sizes, and colors
- **CSS Classes**: Override styles with custom classes
- **Theme**: Modify Tailwind config for global changes
- **Composition**: Combine components for complex layouts 