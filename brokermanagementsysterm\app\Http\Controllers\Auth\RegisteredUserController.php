<?php

namespace App\Http\Controllers\Auth;

use App\Events\UserRegistered;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules;

class RegisteredUserController extends Controller
{
    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function  index(Request $request)
    {
        $user_type = $request->query('user_type');
        if($user_type == 'admins'){
            $role_id = 1;
        }
        elseif ($user_type == 'brokers'){
            $role_id = 2;
        }else{
            return response()->json(['message' => 'user type is needed'], 422);
        }
        $users = User::where('role_id', $role_id)
            ->get();
        return response()->json($users);
    }
    public function store(Request $request): JsonResponse
    {
        $validated = Validator::make($request->all(), [
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'role_id' => ['required'],
            'id_number' => ['required'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:'.User::class],
            'phone_number' => ['required', 'string', 'max:255', 'unique:'.User::class],
           // 'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);
        if ($validated->fails()) {
            return response()->json(['status' =>'error', 'errors' => $validated->errors()], 422);
        }
$password = Hash::make('Aimsoft@1');
        $user = User::create([
            'first_name' => $request->input('first_name'),
            'last_name' => $request->input('last_name'),
           'id_number' => $request->input('id_number'),
           // 'name' =>  $request->input('first_name') . ' ' . $request->input('last_name'),
            'email' => $request->input('email'),
            'phone_number' => $request->input('phone_number'),
            'role_id' => $request->input('role_id'),
            'password' =>  $password
        ]);

        event(new Registered($user));
        event( new UserRegistered($user));


        return response()->json(['message' => 'User successfully registered.'], 201);
    }

    public function edit($user_id): JsonResponse
    {
        $data = User::findOrFail($user_id);

        return response()->json($data);
    }

    public function show($user_id): JsonResponse
    {
        $data = User::with('role')->findOrFail($user_id);

        return response()->json($data);
    }
    public function update(Request $request): JsonResponse
    {
        $validated_details= $request->all();
        //return response()->json($validated_details);
        unset($validated_details['password']);
        $user_id = $validated_details['user_id'];
        $validated = Validator::make($request->all(), [
            'user_id' => ['required'],
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'id_number' => ['required'],
            'role_id' => ['required'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255',
                Rule::unique(User::class)->ignore($user_id, 'user_id')],
            'phone_number' => ['required', 'string', 'max:255',
                Rule::unique(User::class)->ignore($user_id, 'user_id'),
            ],
            //  'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        if ($validated->fails()) {
            return response()->json(['status' =>'error', 'errors' => $validated->errors()], 422);
        }

        try {
            $user = User::findOrFail($request->input('user_id'));

            $updated_user = $user->update([
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
                'id_number' => $request->input('id_number'),
                // 'name' =>  $request->input('first_name') . ' ' . $request->input('last_name'),
                'email' => $request->input('email'),
                'phone_number' => $request->input('phone_number'),
                'role_id' => $request->input('role_id'),
                // 'password' => Hash::make($request->string('password')),
            ]);


            // event(new Registered($user));


            return response()->json(['status' => 'success', 'user_id' => $user,'message' =>   $user['first_name'].' '.$user['last_name'].' updated successfully']);
        }
        catch (\Exception $e){
            Log::debug($e);
            return response()->json([
                'status' => 'error',
                'message' => 'An Error Occured. Contact Adminstrator',
                'errors' => [$e->getMessage()]
            ], 422);
        }
    }


    public function destroy($user_id)
    {
        try {
            $user = User::withoutTrashed()->find($user_id);

            if (!$user) {

                return response()->json(['status' => 'error', 'message' =>  'User already deactivated']);
            }
            $user->delete();

            return response()->json(['status' => 'success', 'message' =>  'User deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => 'An error occurred', 'errors' => [$e->getMessage()]]);
        }
    }

    public function restore($user_id){

        try {
            $user = User::onlyTrashed()->find($user_id);

            if (!$user) {

                return response()->json(['status' => 'error', 'message' =>  'User already active']);
            }
            $user->restore();

            return response()->json(['status' => 'success', 'message' =>  $user['first_name'].'  '.$user['last_name'].' restored successfully']);
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => 'An error occurred', 'errors' => [$e->getMessage()]]);
        }
    }

    public function changePassword(Request $request)
    {
        // Validate input
       $user_type = $request->query('current_user');

        $validator = Validator::make($request->all(), [
            'current_password' => ['required', 'string', 'min:4'],
            'password' => ['required', 'string', 'min:4', 'confirmed'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors(),
            ], 422);
        }

        $user = auth()->user();

        // Check if current password is correct
        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'status' => 'error',
                'errors' => ['current_password' => 'The current password is incorrect.'],
                'message' => 'Current password is incorrect.',
            ], 422);
        }

        // Update the password
        $user->password = Hash::make($request->password);
        $user->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Password changed successfully.',
        ]);
    }
}
