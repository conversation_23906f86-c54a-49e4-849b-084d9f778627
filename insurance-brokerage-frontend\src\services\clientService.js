import axios from '../utils/axios';
import { mockClients } from './mockData';


// Clients API
export const clientService = {
  // Get all clients
  getClients: async () => {
    try {
      const response = await axios.get('/api/v1/clients');
      
      return response.data
    } catch (error) {
      throw error;
    }
  },

  // Get single client
  getClient: async (id) => {
    try {
      // For now, return mock data
      // const response = await axios.get(`/api/clients/${id}`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      const client = mockClients.find(c => c.id === parseInt(id));
      if (!client) {
        throw new Error('Client not found');
      }
      return client;
    } catch (error) {
      throw error;
    }
  },

  // Create new client
  createClient: async (clientData) => {
    try {
      // For now, simulate creation
      const response = await axios.post('/api/v1/clients', clientData);
      
      return response.data;
      
    } catch (error) {
      throw error;
    }
  },

  // Update client
  updateClient: async (clientData) => {
    try {
      // For now, simulate update
      const response = await axios.put(`/api/v1/clients/${clientData.client_id}`, clientData);
      return response.data;
      
    } catch (error) {
      throw error;
    }
  },

  // Delete client
  deleteClient: async (clientData) => {
    try {
      const response = await axios.delete(`/api/v1/clients/${clientData.client_id}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get clients by broker
  getClientsByBroker: async (brokerId) => {
    try {
      // For now, return mock data
      // const response = await axios.get(`/api/brokers/${brokerId}/clients`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      return mockClients.filter(c => c.brokerId === parseInt(brokerId));
    } catch (error) {
      throw error;
    }
  }
}; 

