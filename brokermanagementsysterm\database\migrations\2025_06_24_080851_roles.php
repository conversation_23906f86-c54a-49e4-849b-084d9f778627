<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('IBMS_ROLES', function (Blueprint $table) {
            $table->id('role_id');
            $table->timestamps();
            $table->string('role_name');
            $table->integer('role_permissions')->default(0);
        });
    }

    /**
     * Reverse th:e migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('IBMS_ROLES');
    }
};
