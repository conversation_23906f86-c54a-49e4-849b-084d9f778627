<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    public function run()
    {
        $categories = [
            [
                'category_name' => 'MOTOR_INSURANCE',
                'category_description' => 'Insurance for motor vehicles',
            ],
            [
                'category_name' => 'HEALTH_INSURANCE',
                'category_description' => 'Medical and health coverage plans',
            ],
            [
                'category_name' => 'LIFE_INSURANCE',
                'category_description' => 'Life coverage and investment plans',
            ],
            [
                'category_name' => 'PROPERTY_INSURANCE',
                'category_description' => 'Home and property protection',
            ],
        ];

        foreach ($categories as $category) {
            Category::firstOrCreate(
                ['category_name' => $category['category_name']],
                $category
            );
        }
    }
}
