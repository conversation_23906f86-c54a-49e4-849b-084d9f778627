import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  LayoutDashboard, Users, FileText, Briefcase, User, LogOut,
  Building, ShieldCheck, HandCoins, Car, FilePlus, BookOpen, Menu, X, PanelLeft
} from 'lucide-react';
import axios from '../utils/axios.js'
import {useAuth} from "../context/AuthProvider.jsx";

// Define navigation items with their respective icons
const adminNav = [
  { id: "/admin/dashboard", label: "Admin Dashboard", icon: LayoutDashboard },
  { id: "/admin/user-registration", label: "Users", icon: Users },
  { id: "/admin/insurers", label: "Insurers", icon: Building },
  { id: "/admin/cover-types", label: "Cover Types", icon: ShieldCheck },
  { id: "/admin/policies", label: "Policies", icon: Briefcase },
  { id: "/admin/benefits", label: "Benefits", icon: HandCoins },
  { id: "/admin/quotes", label: "Quotes", icon: FileText },
];

const brokerNav = [
  { id: "/broker/dashboard", label: "Broker Dashboard", icon: LayoutDashboard },
  { id: "/broker/clients", label: "Clients", icon: Users },
  // { id: "/broker/vehicles/create", label: "Add Vehicle", icon: Car },
  { id: "/broker/quotes/create", label: "New Quote", icon: FilePlus },
  { id: "/broker/policy-catalog", label: "Policy Catalog", icon: BookOpen },
  { id: "/broker/profile", label: "Profile", icon: User },
];

// Sidebar Component - now receives props for collapse state and toggle function
const Sidebar = ({ isSidebarCollapsed, toggleSidebar }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const {logout } = useAuth();

  // Determine if admin or broker based on path
  const isAdmin = location.pathname.startsWith("/admin");
  const navItems = isAdmin ? adminNav : brokerNav;



  return (
    <nav
      className={`fixed inset-y-0 left-0 h-screen bg-gray-800 text-white z-40
        flex flex-col rounded-r-lg m-2 p-4 font-inter
        ${isSidebarCollapsed ? 'w-20' : 'w-64'}
        transition-[width] duration-300 ease-in-out shadow-lg`}
    >
      {/* Sidebar Header */}
      <div className={`flex ${isSidebarCollapsed ? 'justify-center' : 'items-center justify-between'} mb-8 px-2`}>
        {!isSidebarCollapsed && (
          <div className="flex flex-col items-start">
            <h1 className="text-4xl font-extrabold text-blue-500 tracking-wide leading-none">IBMS</h1>
            {/* <span className="text-base font-bold text-blue-400 mt-1 uppercase tracking-wider">
              {isAdmin ? "Admin Panel" : "Broker Panel"}
            </span> */}
          </div>
        )}
        <button
          onClick={toggleSidebar}
          className="p-2 rounded-full hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
          aria-label={isSidebarCollapsed ? "Expand Sidebar" : "Collapse Sidebar"}
        >
          <PanelLeft size={20} className="flex-shrink-0" />
        </button>
      </div>

      {/* Navigation Links */}
      <ul className="flex-1 py-2 space-y-1">
        {navItems.map((item) => {
          const active = location.pathname.startsWith(item.id);
          const IconComponent = item.icon; // Get the Lucide icon component

          return (
            <li key={item.id}>
              <button
                onClick={() => navigate(item.id)}
                className={`w-full flex items-center p-3 rounded-lg
                  ${active ? 'bg-blue-600 text-white shadow-md' : 'text-gray-300 hover:bg-gray-700 hover:text-white'}
                  transition-all duration-200 ease-in-out group`}
              >
                {IconComponent && <IconComponent size={20} className="flex-shrink-0" />}
                <span
                  className={`ml-4 text-sm font-medium
                    ${isSidebarCollapsed ? 'hidden opacity-0' : 'block opacity-100'}
                    transition-all duration-300 ease-in-out`}
                >
                  {item.label}
                </span>
              </button>
            </li>
          );
        })}
      </ul>

      {/* Logout Button */}
      <div className="mt-auto pt-4 border-t border-gray-700">
        <button
          onClick={logout}
          className="flex items-center justify-center w-full p-3 rounded-lg
            bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500
            transition-colors duration-200 ease-in-out shadow-md"
        >
          <LogOut size={20} className="flex-shrink-0" />
          <span
            className={`ml-4 text-sm font-medium
              ${isSidebarCollapsed ? 'hidden opacity-0' : 'block opacity-100'}
              transition-all duration-300 ease-in-out`}
          >
            Logout
          </span>
        </button>
      </div>
    </nav>
  );
};

export default Sidebar;