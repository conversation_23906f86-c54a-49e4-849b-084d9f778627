import React from "react";
import { statusBadgeStyles } from "../Styles/uiTheme";

const StatusBadge = ({ status, size = "default" }) => {
  const { config: statusConfig, sizes: sizeClasses } = statusBadgeStyles;

  const config = statusConfig[status] || statusConfig["Inactive"];
  const sizeClass = sizeClasses[size] || sizeClasses.default;

  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${config.bgColor} ${config.textColor} ${config.borderColor} ${sizeClass}`}
    >
      {status}
    </span>
  );
};

export default StatusBadge;
