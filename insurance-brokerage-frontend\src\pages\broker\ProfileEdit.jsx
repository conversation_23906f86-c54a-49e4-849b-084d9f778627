import React, { useState } from "react";
import { <PERSON>, <PERSON>H<PERSON>er, <PERSON><PERSON>, Alert } from "../../components";
import { useNavigate } from "react-router-dom";

export default function ProfileEdit() {
  const navigate = useNavigate();
  // In a real app, fetch broker data from context or API
  const [formData, setFormData] = useState({
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+254 700 123 456",
    idNumber: "12345678",
    licenseNumber: "BRK-2024-001",
    address: "Nairobi, Kenya",
    status: "Active"
  });
  const [showSuccess, setShowSuccess] = useState(false);
  const [validation, setValidation] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    setValidation(prev => ({ ...prev, [name]: "" }));
  };

  const validate = () => {
    const errors = {};
    if (!formData.name) errors.name = "Full name is required";
    if (!formData.email) errors.email = "Email is required";
    if (!formData.phone) errors.phone = "Phone number is required";
    if (!formData.idNumber) errors.idNumber = "ID number is required";
    if (!formData.address) errors.address = "Address is required";
    setValidation(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSave = (e) => {
    e.preventDefault();
    if (!validate()) return;
    setShowSuccess(true);
    setTimeout(() => {
      setShowSuccess(false);
      navigate("/broker/profile");
    }, 1200);
  };

  const handleCancel = () => {
    navigate("/broker/profile");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
        <PageHeader
          title="Edit Profile"
          subtitle="Update your personal information"
          breadcrumbs={[
            { label: "Dashboard", href: "/broker/dashboard" },
            { label: "Profile", href: "/broker/profile" },
            { label: "Edit Profile" },
          ]}
        />
        <Card title="Edit Profile">
          {showSuccess && (
            <Alert
              type="success"
              title="Profile Updated"
              message="Your profile information has been successfully updated."
              onClose={() => setShowSuccess(false)}
            />
          )}
          <form className="space-y-6" onSubmit={handleSave}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your full name"
                />
                {validation.name && <p className="text-red-500 text-xs mt-1">{validation.name}</p>}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your email address"
                />
                {validation.email && <p className="text-red-500 text-xs mt-1">{validation.email}</p>}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your phone number"
                />
                {validation.phone && <p className="text-red-500 text-xs mt-1">{validation.phone}</p>}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">ID Number</label>
                <input
                  type="text"
                  name="idNumber"
                  value={formData.idNumber}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your ID number"
                />
                {validation.idNumber && <p className="text-red-500 text-xs mt-1">{validation.idNumber}</p>}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">License Number</label>
                <input
                  type="text"
                  name="licenseNumber"
                  value={formData.licenseNumber}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your license number"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
                <input
                  type="text"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your address"
                />
                {validation.address && <p className="text-red-500 text-xs mt-1">{validation.address}</p>}
              </div>
            </div>
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button type="submit" variant="primary">
                Save Changes
              </Button>
            </div>
          </form>
        </Card>
      </div>
    </div>
  );
} 