import React, { useEffect } from 'react';

export const SuccessMessage = ({ message, onClose }) => {
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => onClose(), 3000);
      return () => clearTimeout(timer);
    }
  }, [message, onClose]);

  if (!message) return null;

  return (
    <div className="fixed top-5 left-1/2 -translate-x-1/2 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 animate-fade-in-down">
      <p className="font-semibold">{message}</p>
      <button onClick={onClose} className="absolute top-1 right-2 text-white text-lg font-bold">
        &times;
      </button>
    </div>
  );
};

export default SuccessMessage;