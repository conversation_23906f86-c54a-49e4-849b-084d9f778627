import React, { useState , useEffect} from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import PageHeader from "../../components/PageHeader";
import Card from "../../components/Card";
import FormWizard from "../../components/FormWizard";
import Alert from "../../components/Alert";
import {userService} from "../../services/index.js";
const mockAdmins = [
  {
    id: 1,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    id_number: "A001",
    phone_number: "0700000001",
    email: "<EMAIL>",
    role_id: "admin",
  },
  {
    id: 2,
    first_name: "<PERSON>",
    last_name: "<PERSON><PERSON><PERSON>",
    id_number: "A002",
    phone_number: "0700000002",
    email: "<EMAIL>",
    role_id: "admin",
  },
  {
    id: 3,
    first_name: "<PERSON>",
    last_name: "<PERSON><PERSON>",
    id_number: "A003",
    phone_number: "0700000003",
    email: "<EMAIL>",
    role_id: "admin",
  },
];

const wizardSteps = [
  {
    title: "Personal Information",
    description: "Edit admin's personal details",
    fields: [
      { name: "first_name", label: "First Name", type: "text", required: true },
      { name: "last_name", label: "Last Name", type: "text", required: true },
      { name: "id_number", label: "ID Number", type: "text", required: true },
    ],
    validation: (data, errors) => {
      if (!data.first_name) errors.first_name = "First name is required";
      if (!data.last_name) errors.last_name = "Last name is required";
      if (!data.id_number) errors.id_number = "ID number is required";
    },
  },
  {
    title: "Contact Information",
    description: "Edit admin's contact details",
    fields: [
      { name: "phone_number", label: "Phone Number", type: "text", required: true },
      { name: "email", label: "Email", type: "email", required: true },
      { name: "role_id", label: "Role", type: "select", options: [
        { value: 1, label: "Admin" },
        { value: 1 , label: "Broker" },
      ], required: true },
    ],
    validation: (data, errors) => {
      if (!data.phone_number) errors.phone_number = "Phone number is required";
      if (!data.email) errors.email = "Email is required";
      if (!data.role_id) errors.role_id = "Role is required";
    },
  },
];

const AdminEditPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [alert, setAlert] = useState({ show: false, type: "success", title: "", message: "" });
 // const admin = mockAdmins.find((a) => String(a.id) === String(id));
  const [loading, setLoading] = useState(false);

  const [admin, setAdmin ] = useState({});
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const user = await userService.getUser(id); // 'me'
        setAdmin(user);
        console.log('user data',JSON.stringify(user)); // Logs 'me'
      } catch (error) {
        console.error('Failed to fetch user', error);
      }
    };

    fetchUser();
  }, []);

  useEffect(() => {
    // if (userRes) {
    if (Object.keys(admin).length === 0 && admin.constructor === Object) {
      setLoading(true);
    }
    else {
      setLoading(false);

    }
  }, [admin]);
  if (loading) {
    return <div>Loading...</div>;
  }

  if (!admin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-gray-500 text-lg">Admin not found.</div>
      </div>
    );
  }

  const handleWizardComplete = async (values) => {
    const user = await userService.updateUser(values);

    setAlert({
      show: true,
      type: "success",
      title: "Admin Updated",
      message: `Admin ${admin.first_name} ${admin.last_name} updated successfully!`,
    });
    setTimeout(() => {
      navigate("/admin/admin-list");
    }, 3000); // match Alert default duration
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center py-8 px-2">
      <div className="w-full max-w-4xl mx-auto">
        <PageHeader
          title="Edit Admin"
          subtitle={`Editing ${admin.first_name} ${admin.last_name}`}
          breadcrumbs={[
            { label: "User Registration", href: "/admin/user-registration" },
            { label: "Admin List", href: "/admin/admin-list" },
            { label: `Edit Admin: ${admin.first_name} ${admin.last_name}` },
          ]}
          className="w-full max-w-4xl"
          actions={[
            {
              label: "Back to Admin List",
              variant: "primary",
              onClick: () => navigate("/admin/admin-list"),
            },
          ]}
        />
        {/* FormWizard Example */}
        <Card title="FormWizard Component" subtitle="Step-by-step form wizard" className="mt-8">
          <FormWizard
            steps={wizardSteps}
            initialValues={admin} // Pass the admin object as initialValues to pre-populate the form
            onComplete={handleWizardComplete}
            onCancel={() => navigate("/insurance")}
          />
        </Card>
        {alert.show && (
          <div className="fixed top-5 right-2 sm:right-5 z-[2000] min-w-[250px] max-w-xs w-auto">
            <Alert
              type={alert.type}
              title={alert.title}
              message={alert.message}
              onClose={() => setAlert({ ...alert, show: false })}
              duration={3000}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminEditPage;
