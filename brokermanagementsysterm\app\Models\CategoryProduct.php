<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CategoryProduct extends Model
{
    use HasFactory, HasUuids;

    protected $primaryKey = 'category_product_id';

    protected $keyType = 'string';

    public $incrementing = false;

    protected $table = 'IBMS_CATEGORY_PRODUCTS';

    protected $fillable = [
        'category_product_name',
        'category_product_description',
        'category_product_code',
        'category_id',
        'category_product_isactive',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function insuranceCategoryProducts()
    {
        return $this->where('category_product_isactive', true);
    }

    public function vehicles()
    {
        return $this->hasMany(Vehicle::class, 'cover_type');
    }

    public function insurances()
    {
        return $this->belongsToMany(Insurance::class, 'insurance_category_products')
            ->withPivot(['price', 'is_active', 'vehicle_usage'])
            ->withTimestamps();
    }
}
