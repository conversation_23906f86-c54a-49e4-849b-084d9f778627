<?php

namespace Tests\Unit;

use App\Models\Client;
use App\Models\RoleSetupService;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ClientModelTest extends TestCase
{
    /**
     * A basic unit test example.
     */
    use RefreshDatabase;

    public function test_example(): void
    {
        $this->assertTrue(true);
    }

    public function test_create_client(): void
    {
        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();

        $this->assertNotNull($testBroker->user_id);

        $client = Client::factory()->create([
            'broker_id' => $testBroker->user_id,
        ]);

        $this->assertTrue(($client != null));
    }

    public function test_broker_attr(): void
    {
        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();

        $this->assertNotNull($testBroker->user_id);

        $client = Client::factory()->create([
            'broker_id' => $testBroker->user_id,
        ]);

        $this->assertNotNull(($client->broker()));
        $this->assertEquals($client->broker->role_id, $testBroker->role_id);

    }
}
