<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('IBMS_INSURANCE_CATEGORY_PRODUCTS', function (Blueprint $table) {
            $table->uuid('insurance_category_product_id')->primary()->unique();

            $table->foreignUuid('insurance_id')
                ->constrained('IBMS_INSURANCES', 'insurance_id')
                ->notnull();

            $table->foreignUuid('category_id')
                ->constrained('IBMS_CATEGORIES', 'category_id')
                ->notnull();

            $table->foreignUuid('category_product_id')
                ->constrained('IBMS_CATEGORY_PRODUCTS', 'category_product_id')
                ->notnull();
            $table->float('price')->default(0.0);
            $table->string('vehicle_usage');

            $table->boolean('is_active')->default(false);

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('IBMS_INSURANCE_CATEGORY_PRODUCTS');
    }
};
