<?php

namespace Tests\Unit;

use App\Models\IBMSPermissions;
use App\Models\Role;
use App\Models\RolePermissionsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use ReflectionClass;
use Tests\TestCase;

class RolePermissionServiceTest extends TestCase
{
    /**
     * A basic unit test example.
     */
    use RefreshDatabase;

    public function test_example(): void
    {
        $this->assertTrue(true);
    }

    public function test_rolehas_permission(): void
    {
        $allPermissions = (new ReflectionClass(IBMSPermissions::class))->getConstants();
        $brokerPermissions = [
            IBMSPermissions::IBMS_ADD_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_EDIT_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_ADD_CLIENT,
            IBMSPermissions::IBMS_EDIT_CLIENT,
            IBMSPermissions::IBMS_VIEW_CLIENT,
            IBMSPermissions::IBMS_CREATE_QUOTATION,
            IBMSPermissions::IBMS_EDIT_QUOTATION,
            IBMSPermissions::IBMS_VIEW_QUOTATION,
            IBMSPermissions::IBMS_VIEW_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_ENABLE_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_DISABLE_INSURANCE_COMPANY,
        ];
        $roleService = new RolePermissionsService;
        $role = Role::factory()->create(
            [
                'role_name' => 'testRole2',
                'role_permissions' => array_sum($brokerPermissions),
            ]
        );
        $this->assertTrue($roleService->hasPermission($role, IBMSPermissions::IBMS_ADD_INSURANCE_COMPANY));
        $this->assertFalse($roleService->hasPermission($role, IBMSPermissions::IBMS_ADD_BROKER));
    }

    public function test_roleadd_permission(): void
    {
        $brokerPermissions = [
            IBMSPermissions::IBMS_ADD_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_EDIT_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_ADD_CLIENT,
            IBMSPermissions::IBMS_EDIT_CLIENT,
            IBMSPermissions::IBMS_VIEW_CLIENT,
            IBMSPermissions::IBMS_CREATE_QUOTATION,
            IBMSPermissions::IBMS_EDIT_QUOTATION,
            IBMSPermissions::IBMS_VIEW_QUOTATION,
            IBMSPermissions::IBMS_VIEW_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_ENABLE_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_DISABLE_INSURANCE_COMPANY,
        ];

        $roleService = new RolePermissionsService;

        $role = Role::factory()->create([
            'role_name' => 'testRole3',
            'role_permissions' => array_sum($brokerPermissions),
        ]);
        $this->assertFalse($roleService->hasPermission($role, IBMSPermissions::IBMS_ADD_BROKER));
        $this->assertTrue($roleService->addPermission($role, IBMSPermissions::IBMS_ADD_BROKER));
        $this->assertTrue($roleService->hasPermission($role, IBMSPermissions::IBMS_ADD_BROKER));

    }

    public function test_roleremove_permission(): void
    {
        $brokerPermissions = [
            IBMSPermissions::IBMS_ADD_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_EDIT_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_ADD_CLIENT,
            IBMSPermissions::IBMS_EDIT_CLIENT,
            IBMSPermissions::IBMS_VIEW_CLIENT,
            IBMSPermissions::IBMS_CREATE_QUOTATION,
            IBMSPermissions::IBMS_EDIT_QUOTATION,
            IBMSPermissions::IBMS_VIEW_QUOTATION,
            IBMSPermissions::IBMS_VIEW_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_ENABLE_INSURANCE_COMPANY,
            IBMSPermissions::IBMS_DISABLE_INSURANCE_COMPANY,
        ];
        $roleService = new RolePermissionsService;
        $role = Role::factory()->create([
            'role_name' => 'testRole4',
            'role_permissions' => array_sum($brokerPermissions),
        ]);
        $this->assertTrue($roleService->hasPermission($role, IBMSPermissions::IBMS_ADD_INSURANCE_COMPANY));
        $this->assertTrue($roleService->removePermission($role, IBMSPermissions::IBMS_ADD_INSURANCE_COMPANY));
        $this->assertFalse($roleService->hasPermission($role, IBMSPermissions::IBMS_ADD_INSURANCE_COMPANY));

    }
}
