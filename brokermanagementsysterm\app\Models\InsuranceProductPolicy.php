<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InsuranceProductPolicy extends Model
{
    use HasFactory, HasUuids;

    protected $primaryKey = 'insurance_product_policy_id';

    protected $keyType = 'string';

    public $incrementing = false;

    protected $table = 'IBMS_INSURANCE_PRODUCT_POLICIES';

    protected $fillable = [
        'insurance_category_product_id', 'policy_code', 'base_premium',
        'sum_insured', 'commission_rate', 'vehicle_commercial',
        'vehicle_private', 'vehicle_motorcycle', 'vehicle_special', 'is_active',
    ];

    public function insuranceCategoryProduct()
    {
        return $this->belongsTo(InsuranceCategoryProduct::class, 'insurance_category_product_id');
    }

    public function benefits()
    {
        return $this->hasMany(PolicyBenefit::class, 'insurance_product_policy_id');
    }
}
