import React from "react";
import { tablesStyles } from "../Styles/uiTheme";

// --- Internal Helper Component: Table Row ---
const CompanyTableRow = ({ company, onView, onEdit, onDelete }) => {
  // Tailwind classes for status badges
  const { statusClasses } = tablesStyles;
  const statusBadgeClass = `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
    statusClasses[company.status] || "bg-gray-100 text-gray-800"
  }`;

  return (
    <tr className="hover:bg-gray-50">
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
        <strong>{company.name}</strong>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
        {company.contact}
        <br />
        <small className="text-gray-500">{company.email}</small>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
        {company.products}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
        {company.policies}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
        <span className={statusBadgeClass}>{company.status}</span>
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
        {company.lastUpdated}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <button
          className="text-blue-600 hover:text-blue-900 mr-3 p-1 rounded hover:bg-blue-100 transition duration-150 ease-in-out"
          onClick={() => onView(company.id)}
          title="View Details"
        >
          <i className="fas fa-eye"></i> {/* View Icon */}
        </button>
        <button
          className="text-indigo-600 hover:text-indigo-900 mr-3 p-1 rounded hover:bg-indigo-100 transition duration-150 ease-in-out"
          onClick={() => onEdit(company.id)}
          title="Edit Company"
        >
          <i className="fas fa-pencil-alt"></i> {/* Edit Icon */}
        </button>
        <button
          className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-100 transition duration-150 ease-in-out"
          onClick={() => onDelete(company.id)}
          title="Delete Company"
        >
          <i className="fas fa-trash"></i> {/* Delete Icon */}
        </button>
      </td>
    </tr>
  );
};

// --- Main Exported Component: InsuranceCompaniesTable ---
const InsuranceCompaniesTable = ({ companies, onView, onEdit, onDelete }) => {
  return (
    <div className="overflow-x-auto">
      {" "}
      {/* Ensures horizontal scrolling on small screens */}
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Company Name
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Contact Person/Email
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Products Offered
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Total Policies
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Status
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Last Updated
            </th>
            <th scope="col" className="relative px-6 py-3">
              <span className="sr-only">Actions</span>
            </th>{" "}
            {/* For accessibility */}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {companies.map((company) => (
            <CompanyTableRow
              key={company.id}
              company={company}
              onView={onView}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default InsuranceCompaniesTable;
