import React from "react";
import { useNavigate } from "react-router-dom";

export default function ErrorPage({ code = 500, message = "Something went wrong.", actionLabel = "Go Home", actionTo = "/" }) {
  const navigate = useNavigate();
  const codeColors = {
    404: "text-yellow-500 border-yellow-400",
    401: "text-red-500 border-red-400",
    403: "text-orange-500 border-orange-400",
    500: "text-red-600 border-red-500",
    400: "text-pink-500 border-pink-400",
    default: "text-gray-500 border-gray-400"
  };
  const colorClass = codeColors[code] || codeColors.default;

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 px-4">
      <div className={`text-7xl font-extrabold mb-4 border-b-4 pb-2 ${colorClass}`}>{code}</div>
      <div className="text-2xl font-semibold mb-2 text-gray-800">{message}</div>
      <button
        className="mt-6 px-6 py-2 bg-blue-600 text-white rounded-lg font-semibold hover:bg-blue-700 transition"
        onClick={() => navigate(actionTo)}
      >
        {actionLabel}
      </button>
    </div>
  );
} 