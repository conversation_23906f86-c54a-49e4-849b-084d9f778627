<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @OA\Schema(
 *   schema="ClientPreferences",
 *   type="object",
 *   required={"client_id"},
 *   title="Client Preferences",
 *   description="Insurance preferences associated with a client",
 *
 *   @OA\Property(
 *     property="client_id",
 *     type="string",
 *     format="uuid",
 *     description="Client UUID reference"
 *   ),
 *   @OA\Property(
 *     property="preferred_insurers",
 *     type="array",
 *     description="List of preferred insurance companies",
 *
 *     @OA\Items(type="string")
 *   ),
 *
 *   @OA\Property(
 *     property="preferred_cover_types",
 *     type="array",
 *     description="Preferred cover types",
 *
 *     @OA\Items(type="string", enum={"comprehensive", "third_party"})
 *   ),
 *
 *   @OA\Property(
 *     property="preferred_channel",
 *     type="string",
 *     enum={"email", "phone", "whatsapp"},
 *     description="Preferred communication channel"
 *   ),
 *   @OA\Property(
 *     property="renewal_reminder_enabled",
 *     type="boolean",
 *     description="Whether renewal reminders are enabled"
 *   ),
 *   @OA\Property(
 *     property="budget_min",
 *     type="number",
 *     format="float",
 *     description="Minimum budget for cover"
 *   ),
 *   @OA\Property(
 *     property="budget_max",
 *     type="number",
 *     format="float",
 *     description="Maximum budget for cover"
 *   ),
 *   @OA\Property(
 *     property="created_at",
 *     type="string",
 *     format="date-time"
 *   ),
 *   @OA\Property(
 *     property="updated_at",
 *     type="string",
 *     format="date-time"
 *   )
 * )
 */
class ClientPreferences extends Model
{
    use HasFactory;

    protected $table = 'IBMS_CLIENT_PREFERENCES';

    protected $primaryKey = 'id';

    protected $fillable = [
        'vehicle_id',
        'preferred_cover_types',
        'preferred_insurers',
        'blacklisted_insurers',
        'preferred_channel',
        'preferred_contact_time',
        'allow_marketing_emails',
        'auto_renewal_enabled',
        'renewal_reminder_enabled',
        'days_before_renewal_reminder',
        'budget_min',
        'budget_max',
        'vehicle_usage',
        'preferred_workshops',
        'digital_document_delivery',
        'physical_document_delivery',
        'require_detailed_explanation',
        'profile_score',
        'last_updated_by_broker',
    ];

    protected $casts = [
        'preferred_insurers' => 'array',
        'blacklisted_insurers' => 'array',
        'preferred_cover_types' => 'array',
        'preferred_workshops' => 'array',
        'allow_marketing_emails' => 'boolean',
        'auto_renewal_enabled' => 'boolean',
        'renewal_reminder_enabled' => 'boolean',
        'digital_document_delivery' => 'boolean',
        'physical_document_delivery' => 'boolean',
        'require_detailed_explanation' => 'boolean',
        'budget_min' => 'decimal:2',
        'budget_max' => 'decimal:2',
        'profile_score' => 'integer',
    ];

    public function vehicle()
    {
        return $this->belongsTo(Vehicle::class, 'vehicle_id');
    }
}
