<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('IBMS_CATEGORY_PRODUCTS', function (Blueprint $table) {
            $table->uuid('category_product_id')->primary();
            $table->string('category_product_name')->nullable(false)->unique(true);
            $table->string('category_product_description')->nullable(false);
            $table->integer('category_product_code')->nullable();
            $table->boolean('category_product_isactive')->default(false);
            $table->uuid('category_id')->nullable();
            $table->timestamps();
            $table->foreign('category_id')
                ->references('category_id')
                ->on('IBMS_CATEGORIES');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IBMS_CATEGORY_PRODUCTS');
    }

    // public function scopeActive($query)
    // {
    //     return $query->where('cateproduct_isactive', true);
    // }
};
