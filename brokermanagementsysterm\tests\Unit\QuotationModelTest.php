<?php

namespace Tests\Feature;

use App\Models\Client;
use App\Models\InsuranceCategoryProduct;
use App\Models\Quotation;
use App\Models\RoleSetupService;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class QuotationModelTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        RoleSetupService::createRoles();
        // Assuming roles are handled with a `broker()` state
        $this->broker = User::factory()->broker()->create();
        // $this->actingAs($this->broker, 'sanctum');

        $this->client = Client::factory()->create(['broker_id' => $this->broker->user_id]);
        $this->vehicle = Vehicle::factory()->create(['client_id' => $this->client->client_id]);

    }

    public function test_quotation_can_be_created()
    {

        $quotation = Quotation::factory()->create([
            'client_id' => $this->client->client_id,
            'vehicle_id' => $this->vehicle->vehicle_id,
        ]);

        $this->assertDatabaseHas('IBMS_QUOTATIONS', [
            'quotation_id' => $quotation->quotation_id,
        ]);
    }

    public function test_quotation_belongs_to_client()
    {
        $quotation = Quotation::factory()->create([
            'client_id' => $this->client->client_id,
            'vehicle_id' => $this->vehicle->vehicle_id,
        ]);
        $this->assertInstanceOf(Client::class, $quotation->client);
    }

    public function test_quotation_belongs_to_vehicle()
    {
        $quotation = Quotation::factory()->create([
            'client_id' => $this->client->client_id,
            'vehicle_id' => $this->vehicle->vehicle_id,
        ]);

        $this->assertInstanceOf(Vehicle::class, $quotation->vehicle);
    }

    public function test_quotation_belongs_to_insurance_product()
    {
        $quotation = Quotation::factory()->create([
            'client_id' => $this->client->client_id,
            'vehicle_id' => $this->vehicle->vehicle_id,
        ]);

        $this->assertInstanceOf(InsuranceCategoryProduct::class, $quotation->insuranceCategoryProduct);
    }

    public function test_can_mark_quotation_as_accepted()
    {
        $quotation = Quotation::factory()->create([
            'client_id' => $this->client->client_id,
            'vehicle_id' => $this->vehicle->vehicle_id,
        ]);

        $quotation->update([
            'status' => 'approved',
            'accepted_at' => now(),
        ]);

        $this->assertEquals('approved', $quotation->fresh()->status);
        $this->assertNotNull($quotation->fresh()->accepted_at);
    }
}
