import React, {useEffect, useState} from "react";
import Alert from "../../components/Alert";
import Button from "../../components/Button";
import Card from "../../components/Card";
import PageHeader from "../../components/PageHeader";
import DataTable from "../../components/DataTable";
import { useNavigate } from "react-router-dom";
import {userService} from "../../services/index.js";
import Modal from "../../components/Modal";


const AdminListPage = () => {
	const [admins, setAdmins] = useState([]);
	const [loading, setLoading] = useState(false);
	const [alert, setAlert] = useState({
		show: false,
		type: "info",
		title: "",
		message: "",
	});
	const [showDeleteModal, setShowDeleteModal] = useState(false);
	const [adminToDelete, setAdminToDelete] = useState(null);

	useEffect(() => {
		const fetchUser = async () => {
			try {
				const admins = await userService.getUsers('admins'); // 'me'
				setAdmins(admins);
				console.log('user data', admins); // Logs 'me'
			} catch (error) {
				console.error('Failed to fetch user', error);
			}
		};

		fetchUser();
	}, []);

	useEffect(() => {
		// if (userRes) {
		if (Object.keys(admins).length === 0 && admins.constructor === Object) {
			setLoading(true);
		}
		else {
			setLoading(false);

		}
	}, [admins]);

	const navigate = useNavigate();

	const columns = [

		{ key: "first_name", label: "First Name", sortable: true },
		{ key: "last_name", label: "Last Name", sortable: true },
		{ key: "id_number", label: "ID Number", sortable: true },
	];

	const handleTableAction = (action, item) => {
		if (action === "view") handleView(item.user_id);
		if (action === "edit") handleEdit(item.user_id);
		if (action === "delete") { setAdminToDelete(item); setShowDeleteModal(true); }
	};

	const handleEdit = (id) => {
		const admin = admins.find((a) => a.id === id);
		navigate(`/admin/admin-list/${id}/edit`, { state: { admin } });
	};

	const handleDelete = async (id) => {
		const user = await userService.deleteUser(id);
		//setAdmins(admins.filter((admin) => admin.id !== id));
		setAlert({
			show: true,
			type: "success",
			title: "Deleted",
			message: "Admin deleted successfully!",
		});
	};

	const handleView = (id) => {
		navigate(`/admin/admin-list/${id}`);
	};
	if (loading) {
		return <div>Loading...</div>;
	}
	return (
		<div className="min-h-screen bg-gray-50 flex flex-col items-center py-8 px-2">
			<div className="w-full max-auto mx-auto flex flex-col items-center">
				<PageHeader
					title="Admin List"
					subtitle="View and manage all admins"
					breadcrumbs={[
						{ label: "User Registration", href: "/admin/user-registration" },
						{ label: "Admin List" },
					]}
					className="w-full"
					actions={[
						{
							label: "Back to User Registration",
							variant: "primary",
							onClick: () =>
								(window.location.href = "/admin/user-registration"),
						},
					]}
				/>
				<div className="h-8" />
				{alert.show && (
					<div className="mb-4 w-full max-w-2xl mx-auto">
						<Alert
							type={alert.type}
							title={alert.title}
							message={alert.message}
							onClose={() => setAlert({ ...alert, show: false })}
						/>
					</div>
				)}
				<Card
					title="DataTable Component"
					subtitle="Advanced table with sorting, searching, and pagination"
					className="w-full max-auto"
				>
					<DataTable
						data={admins}
						columns={columns}
						onView={(item) => handleTableAction("view", item)}
						onEdit={(item) => handleTableAction("edit", item)}
						onDelete={(item) => handleTableAction("delete", item)}
						searchable={true}
						pagination={true}
						itemsPerPage={5}
					/>
				</Card>
				<Modal
					isOpen={showDeleteModal}
					onClose={() => setShowDeleteModal(false)}
					title="Delete Admin"
					size="small"
				>
					<div className="space-y-4">
						<p>Are you sure you want to delete <strong>Admin {adminToDelete?.first_name} {adminToDelete?.last_name}</strong>?</p>
						<div className="flex justify-end gap-2">
							<Button variant="outline" onClick={() => setShowDeleteModal(false)}>Cancel</Button>
							<Button variant="danger" onClick={() => {/* TODO: implement delete logic */ setShowDeleteModal(false);}}>Delete</Button>
						</div>
					</div>
				</Modal>
			</div>
		</div>
	);
};

export default AdminListPage;
