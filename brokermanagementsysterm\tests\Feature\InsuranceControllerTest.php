<?php

namespace Tests\Feature;

use App\Models\Insurance;
use App\Models\RoleSetupService;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InsuranceControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $adminUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Setup roles and create admin user
        $roleSetupService = new RoleSetupService;
        $roleSetupService::createRoles();

        $this->adminUser = User::factory()->admin()->create();
        $this->actingAs($this->adminUser, 'sanctum');
    }

    public function test_index_returns_all_insurances()
    {
        Insurance::factory()->count(5)->create();

        $response = $this->getJson('/api/v1/insurance/companies');

        $response->assertStatus(200)
            ->assertJsonCount(5);
    }

    public function test_store_creates_new_insurance()
    {
        $data = [
            'name' => 'Test Insurance',
            'company_code' => 'TST',
            'email' => '<EMAIL>',
            'phone_number' => '************',
            'address' => '123 Test St',
            'is_active' => true,
        ];

        $response = $this->postJson('/api/v1/insurance/companies', $data);

        $response->assertStatus(201)
            ->assertJson($data);

        $this->assertDatabaseHas('IBMS_INSURANCES', [
            'email' => '<EMAIL>',
        ]);
    }

    public function test_show_returns_specific_insurance()
    {
        $insurance = Insurance::factory()->create();

        $response = $this->getJson("/api/v1/insurance/companies/{$insurance->insurance_id}");

        // Check if response structure is correct first
        $response->assertStatus(200)
            ->assertJsonStructure([
                'insurance_id',
                'name',
                'company_code',
                'email',
                // other expected fields
            ]);

        // Then verify specific values
        $response->assertJsonFragment([
            'insurance_id' => $insurance->insurance_id->toString(),
            'name' => $insurance->name,
        ]);
    }

    public function test_update_modifies_existing_insurance()
    {
        $insurance = Insurance::factory()->create();

        $updateData = [
            'name' => 'Updated Insurance Name',
            'email' => '<EMAIL>',
        ];

        $response = $this->putJson("/api/v1/insurance/companies/{$insurance->insurance_id}", $updateData);

        $response->assertStatus(200)
            ->assertJsonFragment($updateData);

        $this->assertDatabaseHas('IBMS_INSURANCES', $updateData);
    }

    public function test_destroy_deletes_insurance()
    {
        $insurance = Insurance::factory()->create();

        // Verify pre-deletion state
        $this->assertDatabaseHas('IBMS_INSURANCES', [
            'insurance_id' => $insurance->insurance_id,
            'deleted_at' => null,
        ]);

        $response = $this->deleteJson("/api/v1/insurance/companies/{$insurance->insurance_id}");
        $response->assertStatus(204);

        // Get the record directly from database
        $deletedRecord = \DB::table('IBMS_INSURANCES')
            ->where('insurance_id', $insurance->insurance_id)
            ->first();

        // Debug output
        \Log::debug('Post-deletion record state', [
            'deleted_at' => $deletedRecord->deleted_at ?? 'NULL',
            'record' => $deletedRecord,
        ]);

        // Verify soft deletion
        $this->assertNotNull(
            $deletedRecord->deleted_at,
            'The deleted_at timestamp should be set after soft delete'
        );
    }

    public function test_validation_for_unique_fields()
    {
        $existing = Insurance::factory()->create([
            'name' => 'Existing Insurance',
            'company_code' => 'EXT',
            'email' => '<EMAIL>',
        ]);

        $data = [
            'name' => $existing->name,
            'company_code' => $existing->company_code,
            'email' => $existing->email,
            'phone_number' => '************',
            'address' => '123 Test St',
        ];

        $response = $this->postJson('/api/v1/insurance/companies', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['name', 'company_code', 'email']);
    }

    public function test_unauthenticated_access_denied()
    {
        // Proper way to logout in Sanctum
        $this->app['auth']->guard('sanctum')->forgetUser();

        $response = $this->getJson('/api/v1/insurance/companies');
        $response->assertStatus(401);
    }
}
