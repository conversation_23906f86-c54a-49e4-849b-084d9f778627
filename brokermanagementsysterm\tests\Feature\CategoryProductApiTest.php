<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\CategoryProduct;
use App\Models\RoleSetupService;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CategoryProductApiTest extends TestCase
{
    use RefreshDatabase;

    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();

        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();

        // Create admin user with role
        $admin = User::factory()->admin()->create();

        $this->actingAs($admin);

        // Ensure a product exists
        Category::factory()->create();
    }

    public function test_admin_can_create_a_new_cover_type()
    {

        $data = [
            'category_product_name' => 'Comprehensive',
            'category_product_description' => 'Covers everything',
            'cover_isactive' => true,
            'cover_code' => 111,
            'category_id' => Category::first()->category_id,
        ];

        $response = $this->postJson('/api/v1/cover-types', $data);

        $response->assertStatus(201)
            ->assertJsonFragment(['category_product_name' => 'Comprehensive']);

        $this->assertDatabaseHas('IBMS_CATEGORY_PRODUCTS', [
            'category_product_name' => 'Comprehensive',
        ]);
    }

    public function test_duplicate_cover_names_are_not_allowed()
    {
        CategoryProduct::factory()->create(['category_product_name' => 'Third Party']);

        $data = [
            'category_product_name' => 'Third Party',
            'category_product_description' => 'Duplicate test',
            'cover_isactive' => true,
            'cover_code' => 222,
            'category_id' => Category::first()->category_id,
        ];

        $response = $this->postJson('/api/v1/cover-types', $data);

        $response->assertStatus(422);
    }

    public function test_admin_can_view_all_cover_types()
    {
        CategoryProduct::factory()->count(3)->create();

        $response = $this->getJson('/api/v1/cover-types');

        $response->assertStatus(200)
            ->assertJsonCount(3);
    }

    public function test_admin_can_update_a_cover_type()
    {
        $cover = CategoryProduct::factory()->create([
            'category_product_name' => 'Old Name',
        ]);

        $data = [
            'category_product_name' => 'Updated Name',
            'category_product_description' => 'Updated description',
            'cover_isactive' => true,
            'cover_code' => 123,
            'category_id' => $cover->category_id,
        ];

        $response = $this->putJson("/api/v1/cover-types/{$cover->category_product_id}", $data);

        $response->assertStatus(200)
            ->assertJsonFragment(['category_product_name' => 'Updated Name']);

        $this->assertDatabaseHas('IBMS_CATEGORY_PRODUCTS', [
            'category_product_name' => 'Updated Name',
        ]);
    }

    public function test_admin_can_delete_cover_type_if_not_in_use()
    {
        $cover = CategoryProduct::factory()->create();

        $response = $this->deleteJson("/api/v1/cover-types/{$cover->category_product_id}");

        $response->assertStatus(200)
            ->assertJsonFragment(['message' => 'Cover deleted successfully']);

        $this->assertDatabaseMissing('IBMS_CATEGORY_PRODUCTS', [
            'id' => $cover->id,
        ]);
    }

    public function test_admin_cannot_delete_cover_type_if_used_by_vehicle()
    {
        $cover = CategoryProduct::factory()->create();
        $broker = User::factory()->broker()->create();
        $client = \App\Models\Client::factory()->create(['broker_id' => $broker]);
        Vehicle::factory()->create([
            'cover_type' => $cover->category_product_id,
            'client_id' => $client->client_id,
        ]);

        $response = $this->deleteJson("/api/v1/cover-types/{$cover->category_product_id}");

        $response->assertStatus(400)
            ->assertJsonFragment(['message' => 'Cannot delete cover type in use']);
    }

    public function test_only_admin_can_access_cover_type_routes()
    {
        $nonAdmin = User::factory()->broker()->create();

        $this->actingAs($nonAdmin);

        $response = $this->getJson('/api/v1/cover-types');
        $response->assertStatus(403); // Forbidden
    }
}
