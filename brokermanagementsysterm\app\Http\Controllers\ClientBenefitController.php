<?php

namespace App\Http\Controllers;

use App\Models\ClientBenefit;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class ClientBenefitController extends Controller
{
    public function store(Request $request): JsonResponse
    {

        $validated = Validator::make($request->all(), [

            'client_id' => ['required', 'string', 'max:255'],
            'benefit_id' => ['required', 'string', 'max:255'],

        ]);

        if ($validated->fails()) {
            return response()->json(['status' => 'error', 'errors' => $validated->errors()]);
        }
        try {
            $ClientBenefit = ClientBenefit::create([
                'client_id' => $request->input('client_id'),
                'benefit_id' => $request->input('benefit_id'),
            ]);

            return response()->json(['status' => 'success', 'message' => ' created successfully']);
        } catch (\Exception $e) {

            Log::debug($e);

            return response()->json([
                'status' => 'error',
                'message' => 'An Error Occured. Contact Adminstrator',
                'errors' => [$e->getMessage()],
            ], 422);
        }

    }

    public function edit($benefit_id): JsonResponse
    {
        $data = ClientBenefit::findOrFail($benefit_id);

        return response()->json($data);
    }

    public function show($benefit_id): JsonResponse
    {
        $data = ClientBenefit::findOrFail($benefit_id);

        return response()->json($data);
    }

    public function update(Request $request): JsonResponse
    {
        $validated_details = $request->all();
        // return response()->json($validated_details);
        $validated = Validator::make($request->all(), [

            'client_id' => ['required', 'string', 'max:255'],
            'benefit_id' => ['required', 'string', 'max:255'],

        ]);

        if ($validated->fails()) {
            return response()->json(['status' => 'error', 'errors' => $validated->errors()]);
        }

        try {
            $benefit = ClientBenefit::findOrFail($request->input('benefit_id'));

            $updated_benefit = $benefit->update([
                'client_id' => $request->input('name'),
                'benefit_id' => $request->input('description'),
            ]);

            // event(new Registered($benefit));

            return response()->json(['status' => 'success', 'benefit_id' => $request->input('benefit_id'), 'message' => $benefit['name'].' updated successfully']);
        } catch (\Exception $e) {
            Log::debug($e);

            return response()->json([
                'status' => 'error',
                'message' => 'An Error Occured. Contact Adminstrator',
                'errors' => [$e->getMessage()],
            ], 422);
        }
    }

    public function destroy($benefit_id)
    {
        try {
            $benefit = ClientBenefit::withoutTrashed()->find($benefit_id);

            if (! $benefit) {

                return response()->json(['status' => 'error', 'message' => 'Client Benefit already deactivated']);
            }
            $benefit->delete();

            return response()->json(['status' => 'success', 'message' => 'Client Benefit deleted successfully']);
        } catch (\Exception $e) {
            Log::debug($e);

            return response()->json(['status' => 'error', 'message' => 'An error occurred', 'errors' => [$e->getMessage()]]);
        }
    }

    public function restore($benefit_id)
    {

        try {
            $benefit = ClientBenefit::onlyTrashed()->find($benefit_id);
            if (! $benefit) {
                return response()->json(['status' => 'error', 'message' => 'Client Benefit already active']);
            }
            $benefit->restore();

            return response()->json(['status' => 'success', 'message' => $benefit['name'].' restored successfully']);
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => 'An error occurred', 'errors' => [$e->getMessage()]]);
        }
    }
}
