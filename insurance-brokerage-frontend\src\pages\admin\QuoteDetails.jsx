import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import Card from "../../components/Card";
import PageHeader from "../../components/PageHeader";
import StatusBadge from "../../components/StatusBadge";
import Button from "../../components/Button";
import { mockQuotes, mockClients, mockVehicles } from "../../services/mockData";

export default function QuoteDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [quote, setQuote] = useState(null);
  const [client, setClient] = useState(null);
  const [vehicle, setVehicle] = useState(null);

  useEffect(() => {
    const foundQuote = mockQuotes.find(q => q.id === parseInt(id));
    if (foundQuote) {
      setQuote(foundQuote);
      const foundClient = mockClients.find(c => c.id === foundQuote.clientId);
      const foundVehicle = mockVehicles.find(v => v.id === foundQuote.vehicleId);
      setClient(foundClient);
      setVehicle(foundVehicle);
    }
  }, [id]);

  if (!quote) {
    return (
      <div className="w-full">
        <PageHeader title="Quote Details" subtitle="Quote not found" />
        <div className="mt-6 text-center">
          <p className="text-gray-500">Quote not found</p>
          <Button onClick={() => navigate("/admin/quotes")} className="mt-4">
            Back to Quotes
          </Button>
        </div>
      </div>
    );
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "KES",
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="w-full">
      <PageHeader
        title="Quote Details"
        subtitle={`Quote Number: ${quote.quoteNumber}`}
        headerAction={
          <Button onClick={() => navigate("/admin/quotes")} variant="outline">
            Back to Quotes
          </Button>
        }
      />

      <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quote Information */}
        <Card title="Quote Information" className="lg:col-span-2">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Quote Number</label>
              <p className="mt-1 text-sm text-gray-900">{quote.quoteNumber}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Status</label>
              <div className="mt-1">
                <StatusBadge status={quote.status} />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Premium Amount</label>
              <p className="mt-1 text-sm text-gray-900 font-semibold">{formatCurrency(quote.premiumAmount)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Premium Rate</label>
              <p className="mt-1 text-sm text-gray-900">{quote.premiumRate}%</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Vehicle Value</label>
              <p className="mt-1 text-sm text-gray-900">{formatCurrency(quote.vehicleValue)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Start Date</label>
              <p className="mt-1 text-sm text-gray-900">{formatDate(quote.startDate)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">End Date</label>
              <p className="mt-1 text-sm text-gray-900">{formatDate(quote.endDate)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Created Date</label>
              <p className="mt-1 text-sm text-gray-900">{formatDate(quote.createdAt)}</p>
            </div>
          </div>
        </Card>

        {/* Client Information */}
        <Card title="Client Information">
          {client && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Full Name</label>
                <p className="mt-1 text-sm text-gray-900">{client.firstName} {client.lastName}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Email</label>
                <p className="mt-1 text-sm text-gray-900">{client.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Phone</label>
                <p className="mt-1 text-sm text-gray-900">{client.phone}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">ID Number</label>
                <p className="mt-1 text-sm text-gray-900">{client.idNumber}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Date of Birth</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(client.dateOfBirth)}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Address</label>
                <p className="mt-1 text-sm text-gray-900">{client.address}</p>
                <p className="mt-1 text-sm text-gray-900">{client.city}, {client.postalCode}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Occupation</label>
                <p className="mt-1 text-sm text-gray-900">{client.occupation}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Employer</label>
                <p className="mt-1 text-sm text-gray-900">{client.employer}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Annual Income</label>
                <p className="mt-1 text-sm text-gray-900">{formatCurrency(client.annualIncome)}</p>
              </div>
            </div>
          )}
        </Card>

        {/* Vehicle Information */}
        <Card title="Vehicle Information">
          {vehicle && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700">Registration Number</label>
                <p className="mt-1 text-sm text-gray-900 font-semibold">{vehicle.registrationNumber}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Make & Model</label>
                <p className="mt-1 text-sm text-gray-900">{vehicle.make} {vehicle.model}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Year</label>
                <p className="mt-1 text-sm text-gray-900">{vehicle.year}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Color</label>
                <p className="mt-1 text-sm text-gray-900">{vehicle.color}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Engine Capacity</label>
                <p className="mt-1 text-sm text-gray-900">{vehicle.engineCapacity}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Fuel Type</label>
                <p className="mt-1 text-sm text-gray-900">{vehicle.fuelType}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Transmission</label>
                <p className="mt-1 text-sm text-gray-900">{vehicle.transmission}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Mileage</label>
                <p className="mt-1 text-sm text-gray-900">{vehicle.mileage.toLocaleString()} km</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700">Estimated Value</label>
                <p className="mt-1 text-sm text-gray-900 font-semibold">{formatCurrency(vehicle.estimatedValue)}</p>
              </div>
            </div>
          )}
        </Card>

        {/* Insurance Information */}
        <Card title="Insurance Information">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Insurance Company</label>
              <p className="mt-1 text-sm text-gray-900 font-semibold">{quote.insurerName}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Policy Name</label>
              <p className="mt-1 text-sm text-gray-900">{quote.policyName}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Cover Type</label>
              <p className="mt-1 text-sm text-gray-900">{quote.coverTypeName}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Broker</label>
              <p className="mt-1 text-sm text-gray-900">{quote.brokerName}</p>
            </div>
          </div>
        </Card>

        {/* Policy Details */}
        <Card title="Policy Details">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Policy Type</label>
              <p className="mt-1 text-sm text-gray-900">{quote.coverTypeName}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Premium Rate</label>
              <p className="mt-1 text-sm text-gray-900">{quote.premiumRate}%</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Coverage Period</label>
              <p className="mt-1 text-sm text-gray-900">
                {formatDate(quote.startDate)} - {formatDate(quote.endDate)}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Total Premium</label>
              <p className="mt-1 text-sm text-gray-900 font-semibold text-lg">{formatCurrency(quote.premiumAmount)}</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
