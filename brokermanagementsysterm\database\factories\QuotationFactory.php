<?php

namespace Database\Factories;

use App\Models\Client;
use App\Models\InsuranceCategoryProduct;
use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Quotation>
 */
class QuotationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'quotation_id' => $this->faker->uuid,
            'client_id' => Client::factory(),
            'vehicle_id' => Vehicle::factory(),
            'insurance_category_product_id' => InsuranceCategoryProduct::factory(),
            'total_price' => $this->faker->numberBetween(5000, 20000),
            'status' => 'pending',
            'valid_until' => null,
        ];
    }
}
