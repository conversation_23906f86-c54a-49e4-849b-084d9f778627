<?php

namespace App\Http\Controllers\Insurance;

use App\Http\Controllers\Controller;
use App\Models\Insurance;
use Illuminate\Http\Request;

class InsuranceController extends Controller
{
    public function index()
    {
        return Insurance::all();
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:IBMS_INSURANCES',
            'company_code' => 'required|string|max:255|unique:IBMS_INSURANCES',
            'email' => 'required|email|unique:IBMS_INSURANCES',
            'phone_number' => 'required|string',
            'address' => 'required|string',
            'logo_path' => 'nullable|string',
            'is_active' => 'boolean',
        ]);

        return Insurance::create($validated);
    }

    // In InsuranceController.php
    public function show($id)
    {
        $insurance = Insurance::findOrFail($id);

        return response()->json($insurance); // Remove array wrapping
    }

    //    public function update(Request $request, Insurance $insurance)
    //    {
    //        $validated = $request->validate([
    //            'name' => 'sometimes|required|string|max:255|unique:IBMS_INSURANCES,name,'.$insurance->insurance_id.',insurance_id',
    //            'email' => 'sometimes|nullable|email|unique:IBMS_INSURANCES,email,'.$insurance->insurance_id.',insurance_id',
    //            'phone_number' => 'sometimes|nullable|string',
    //            'address' => 'sometimes|nullable|string',
    //            'logo_path' => 'sometimes|nullable|string',
    //            'is_active' => 'sometimes|boolean'
    //        ]);
    //
    //        $insurance->update($validated);
    //        return $insurance;
    //    }

    // In InsuranceController.php
    public function update(Request $request, $id)
    {
        $insurance = Insurance::findOrFail($id);
        $insurance->update($request->all());

        return response()->json($insurance->fresh()); // Return refreshed model
    }

    public function destroy($id)
    {
        $insurance = Insurance::findOrFail($id);

        \DB::beginTransaction();
        try {
            $result = $insurance->delete();

            \Log::debug('Delete operation result', [
                'result' => $result,
                'deleted_at' => $insurance->deleted_at,
            ]);

            \DB::commit();

            return response()->noContent();
        } catch (\Exception $e) {
            \DB::rollBack();
            \Log::error('Failed to delete insurance', ['error' => $e]);

            return response()->json(['message' => 'Deletion failed'], 500);
        }
    }
}
