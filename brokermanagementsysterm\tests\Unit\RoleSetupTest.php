<?php

namespace Tests\Unit;

use App\Models\IBMSPermissions;
use App\Models\Role;
use App\Models\RoleSetupService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use ReflectionClass;
use Tests\TestCase;

class RoleSetupTest extends TestCase
{
    /**
     * A basic unit test example.
     */
    use RefreshDatabase;

    public function test_example(): void
    {
        $this->assertTrue(true);
    }

    public function test_create_roles(): void
    {
        $rolesetupService = new RoleSetupService;
        $rolesetupService::createRoles();
        $this->assertNotEmpty(Role::all());

    }

    public function test_setup_role(): void
    {
        $rolesetupService = new RoleSetupService;
        $this->assertTrue($rolesetupService->setupRole('TEST_ROLE', (new ReflectionClass(IBMSPermissions::class))->getConstants()));
        $this->assertFalse($rolesetupService->setupRole('TEST_ROLE_WITN_INVALID_PERMISSION', [888888888888888, 888888888888888888888888, 8888888888888888888]));
    }
}
