import axios from '../utils/axios';
import { mockProducts } from './mockData';

// Products API
export const productService = {
  // Get all products
  getProducts: async () => {
    try {
      // For now, return mock data
      // const response = await axios.get('/api/products');
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 400));
      return mockProducts;
    } catch (error) {
      throw error;
    }
  },

  // Get single product
  getProduct: async (id) => {
    try {
      // For now, return mock data
      // const response = await axios.get(`/api/products/${id}`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      const product = mockProducts.find(p => p.id === parseInt(id));
      if (!product) {
        throw new Error('Product not found');
      }
      return product;
    } catch (error) {
      throw error;
    }
  },

  // Create new product
  createProduct: async (productData) => {
    try {
      // For now, simulate creation
      // const response = await axios.post('/api/products', productData);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 600));
      
      const newProduct = {
        ...productData,
        id: Date.now(),
        status: "Active",
        createdAt: new Date().toISOString()
      };
      
      // In a real app, this would be added to the database
      mockProducts.push(newProduct);
      
      return newProduct;
    } catch (error) {
      throw error;
    }
  },

  // Update product
  updateProduct: async (id, productData) => {
    try {
      // For now, simulate update
      // const response = await axios.put(`/api/products/${id}`, productData);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = mockProducts.findIndex(p => p.id === parseInt(id));
      if (index === -1) {
        throw new Error('Product not found');
      }
      
      mockProducts[index] = { ...mockProducts[index], ...productData };
      return mockProducts[index];
    } catch (error) {
      throw error;
    }
  },

  // Delete product
  deleteProduct: async (id) => {
    try {
      // For now, simulate deletion
      // const response = await axios.delete(`/api/products/${id}`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const index = mockProducts.findIndex(p => p.id === parseInt(id));
      if (index === -1) {
        throw new Error('Product not found');
      }
      
      mockProducts.splice(index, 1);
      return { success: true };
    } catch (error) {
      throw error;
    }
  },

  // Get products by company
  getProductsByCompany: async (companyId) => {
    try {
      // For now, return mock data
      // const response = await axios.get(`/api/insurance-companies/${companyId}/products`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      return mockProducts.filter(p => p.companyId === parseInt(companyId));
    } catch (error) {
      throw error;
    }
  }
}; 