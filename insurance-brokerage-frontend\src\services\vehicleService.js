import axios from '../utils/axios';
import { mockVehicles } from './mockData';

// Vehicles API
export const vehicleService = {
  // Get all vehicles
  getVehicles: async () => {
    try {
      // For now, return mock data
      // const response = await axios.get('/api/vehicles');
      // return response.data;
      
      // Simulate API delay
      // await new Promise(resolve => setTimeout(resolve, 400));
      return mockVehicles;
    } catch (error) {
      throw error;
    }
  },

  // Get single vehicle
  getVehicle: async (id) => {
    try {
      // For now, return mock data
      const response = await axios.get(`/api/v1/vehicles/${id}`);
      return response.data;
      
      // Simulate API delay
      // await new Promise(resolve => setTimeout(resolve, 300));
      // const vehicle = mockVehicles.find(v => v.id === parseInt(id));
      // if (!vehicle) {
      //   throw new Error('Vehicle not found');
      // }
      // return vehicle;
    } catch (error) {
      throw error;
    }
  },

  // Create new vehicle
  createVehicle: async (vehicleData) => {
    try {
      const response = await axios.post('/api/v1/vehicles', vehicleData);

      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Update vehicle
  updateVehicle: async (vehicle, vehicleData) => {
    try {
      // Use vehicle_id as the primary key (matches backend Vehicle model)
      const response = await axios.put(`/api/v1/vehicles/${vehicle.vehicle_id}`, vehicleData);
      return response.data;

    } catch (error) {
      throw error;
    }
  },

  // Delete vehicle
  deleteVehicle: async (id) => {
    try {
      // For now, simulate deletion
      const response = await axios.delete(`/api/v1/vehicles/${id}`);
      return response.status;
    } catch (error) {
      throw error;
    }
  },

  // Get vehicles by client
  getVehiclesByClient: async (client) => {
   try{

    const response = await axios.get(`/api/v1/vehicles/client/${client.client_id}`);
    return response.data;

   } catch (error) {
      throw error;
    }
  },

  // Get vehicles by broker
  getVehiclesByBroker: async (brokerId) => {
    try {
      // For now, return mock data
      const response = await axios.get(`/api/v1/vehicles`);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
}; 