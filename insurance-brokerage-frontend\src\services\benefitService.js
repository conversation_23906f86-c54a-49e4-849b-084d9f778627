import axios from '../utils/axios';
import { mockBenefits } from './mockData';

// Benefits API
export const benefitService = {
  // Get all benefits
  getBenefits: async () => {
    try {
      // For now, return mock data
      // const response = await axios.get('/api/benefits');
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 400));
      return mockBenefits;
    } catch (error) {
      throw error;
    }
  },

  // Get single benefit
  getBenefit: async (id) => {
    try {
      // For now, return mock data
      // const response = await axios.get(`/api/benefits/${id}`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      const benefit = mockBenefits.find(b => b.id === parseInt(id));
      if (!benefit) {
        throw new Error('Benefit not found');
      }
      return benefit;
    } catch (error) {
      throw error;
    }
  },

  // Create new benefit
  createBenefit: async (benefitData) => {
    try {
      // For now, simulate creation
      // const response = await axios.post('/api/benefits', benefitData);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 600));
      
      const newBenefit = {
        ...benefitData,
        id: Date.now(),
        status: "Active"
      };
      
      // In a real app, this would be added to the database
      mockBenefits.push(newBenefit);
      
      return newBenefit;
    } catch (error) {
      throw error;
    }
  },

  // Update benefit
  updateBenefit: async (id, benefitData) => {
    try {
      // For now, simulate update
      // const response = await axios.put(`/api/benefits/${id}`, benefitData);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = mockBenefits.findIndex(b => b.id === parseInt(id));
      if (index === -1) {
        throw new Error('Benefit not found');
      }
      
      mockBenefits[index] = { ...mockBenefits[index], ...benefitData };
      return mockBenefits[index];
    } catch (error) {
      throw error;
    }
  },

  // Delete benefit
  deleteBenefit: async (id) => {
    try {
      // For now, simulate deletion
      // const response = await axios.delete(`/api/benefits/${id}`);
      // return response.data;
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const index = mockBenefits.findIndex(b => b.id === parseInt(id));
      if (index === -1) {
        throw new Error('Benefit not found');
      }
      
      mockBenefits.splice(index, 1);
      return { success: true };
    } catch (error) {
      throw error;
    }
  }
}; 