import React, { useState, useEffect } from "react";
import { Card, PageHeader, Button, DataTable, SearchBar, Modal, Alert } from "../../components";
import { useNavigate } from "react-router-dom";
import { benefitService } from "../../services";

export default function BenefitList() {
  const [benefits, setBenefits] = useState([]);
  const [filteredBenefits, setFilteredBenefits] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [benefitToDelete, setBenefitToDelete] = useState(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState("success");
  const [alertMessage, setAlertMessage] = useState("");
  const navigate = useNavigate();

  const showSuccessAlert = (message) => {
    setAlertMessage(message);
    setAlertType("success");
    setShowAlert(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  useEffect(() => {
    async function fetchBenefits() {
      setLoading(true);
      try {
        const benefitsData = await benefitService.getBenefits();
        setBenefits(benefitsData);
        setFilteredBenefits(benefitsData);
      } catch (err) {
        // Optionally handle error
      } finally {
        setLoading(false);
      }
    }
    fetchBenefits();
  }, []);

  useEffect(() => {
    let filtered = benefits;
    if (searchTerm) {
      filtered = filtered.filter(
        (b) =>
          b.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          b.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    setFilteredBenefits(filtered);
  }, [searchTerm, benefits]);

  const columns = [
    {
      key: "name",
      label: "Benefit Name",
      sortable: true,
      render: (value) => <span className="font-semibold text-gray-800">{value}</span>,
    },
    {
      key: "description",
      label: "Description",
      sortable: false,
      render: (value) => <span className="text-gray-600 text-sm">{value}</span>,
    },
    {
      key: "policies",
      label: "Policies",
      sortable: false,
      render: (value) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {value.length}
        </span>
      ),
    },
    {
      key: "status",
      label: "Status",
      sortable: true,
      render: (value) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${value === "Active" ? "bg-green-100 text-green-800" : "bg-gray-200 text-gray-600"}`}>{value}</span>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      render: (_, item) => (
        <div className="flex gap-2">
          <Button size="small" variant="outline" onClick={() => navigate(`/admin/benefits/${item.id}`)}>View</Button>
          <Button size="small" variant="primary" onClick={() => navigate(`/admin/benefits/${item.id}/edit`)}>Edit</Button>
          <Button size="small" variant="danger" onClick={() => { setBenefitToDelete(item); setShowDeleteModal(true); }}>Delete</Button>
        </div>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full mx-auto p-6 space-y-6">
        <PageHeader
          title="Benefits"
          subtitle="Manage all insurance benefits"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Benefits" },
          ]}
          actions={[
            {
              label: "Add Benefit",
              variant: "primary",
              onClick: () => navigate("/admin/benefits/create"),
              icon: "+",
            },
          ]}
        />
        {showAlert && (
            <Alert
              type={alertType}
              title={alertType === "success" ? "Success" : "Error"}
              message={alertMessage}
              onClose={() => setShowAlert(false)}
            />
          )}
        <Card title="Benefits" subtitle={`${filteredBenefits.length} found`}>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-4">
            <div className="flex-1 mt-5">
              <SearchBar
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
                placeholder="Search by Benefit or Description..."
              />
            </div>
          </div>
          {loading ? (
            <div className="text-center py-10 text-gray-500">Loading benefits...</div>
          ) : (
            <DataTable
              data={filteredBenefits}
              columns={columns}
              searchable={false}
              pagination={true}
              itemsPerPage={10}
            />
          )}
          <Modal
            isOpen={showDeleteModal}
            onClose={() => setShowDeleteModal(false)}
            title="Delete Benefit"
            size="small"
          >
            <div className="space-y-4">
              <p>Are you sure you want to delete the <strong>Benefit {benefitToDelete?.name}</strong>?</p>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowDeleteModal(false)}>Cancel</Button>
                <Button variant="danger" onClick={() => { setShowDeleteModal(false); showSuccessAlert(`Benefit ${benefitToDelete?.name} deleted successfully!`); }}>Delete</Button>
              </div>
            </div>
          </Modal>
        </Card>
      </div>
    </div>
  );
}
