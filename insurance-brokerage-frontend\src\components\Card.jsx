import React from "react";
import { cardStyles } from "../Styles/uiTheme";

const Card = ({
  children,
  title,
  subtitle,
  headerAction,
  footer,
  className = "",
  padding = "default",
  shadow = "default",
  border = true,
}) => {
  const { padding: paddingClasses, shadow: shadowClasses } = cardStyles;

  const borderClasses = border ? "border border-gray-200" : "";

  return (
    <div
      className={`bg-white rounded-lg ${borderClasses} ${shadowClasses[shadow]} ${className} w-full`}
    >
      {/* Header */}
      {(title || subtitle || headerAction) && (
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              {title && (
                <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
              )}
              {subtitle && (
                <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
              )}
            </div>
            {headerAction && <div>{headerAction}</div>}
          </div>
        </div>
      )}

      {/* Content */}
      <div className={paddingClasses[padding]}>{children}</div>

      {/* Footer */}
      {footer && (
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          {footer}
        </div>
      )}
    </div>
  );
};

export default Card;
