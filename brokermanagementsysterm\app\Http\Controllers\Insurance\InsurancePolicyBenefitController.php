<?php

namespace App\Http\Controllers;

use App\Models\PolicyBenefit;
use Illuminate\Http\Request;

class PolicyBenefitController extends Controller
{
    public function index()
    {
        return PolicyBenefit::with(['policy'])->get();
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'insurance_product_policy_id' => 'required|exists:IBMS_INSURANCE_PRODUCT_POLICIES,insurance_product_policy_id',
            'deescription' => 'required|string',
        ]);

        return PolicyBenefit::create($validated);
    }

    public function show(PolicyBenefit $benefit)
    {
        return $benefit->load(['policy']);
    }

    public function update(Request $request, PolicyBenefit $benefit)
    {
        $validated = $request->validate([
            'deescription' => 'sometimes|required|string',
        ]);

        $benefit->update($validated);

        return $benefit;
    }

    public function destroy(PolicyBenefit $benefit)
    {
        $benefit->delete();

        return response()->noContent();
    }
}
