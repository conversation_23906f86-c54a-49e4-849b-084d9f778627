<?php

namespace App\Http\Controllers;

use App\Models\InsuranceCategoryProduct;
use Illuminate\Http\Request;

class InsuranceProductController extends Controller
{
    public function index()
    {
        return InsuranceCategoryProduct::with(['insurance', 'category', 'categoryProduct'])->get();
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'insurance_id' => 'required|exists:IBMS_INSURANCES,insurance_id',
            'category_id' => 'required|exists:IBMS_CATEGORIES,category_id',
            'category_product_id' => 'required|exists:IBMS_CATEGORY_PRODUCTS,category_product_id',
            'is_active' => 'boolean',
        ]);

        return InsuranceCategoryProduct::create($validated);
    }

    public function show(InsuranceCategoryProduct $insuranceProduct)
    {
        return $insuranceProduct->load(['insurance', 'category', 'categoryProduct']);
    }

    public function update(Request $request, InsuranceCategoryProduct $insuranceProduct)
    {
        $validated = $request->validate([
            'is_active' => 'sometimes|boolean',
        ]);

        $insuranceProduct->update($validated);

        return $insuranceProduct;
    }
}
