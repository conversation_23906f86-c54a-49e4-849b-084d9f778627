import React from 'react';

const FormField = React.memo(({ 
  label, 
  name, 
  type = 'text', 
  placeholder, 
  helpText, 
  options, 
  validationError, 
  onChange, 
  value, 
  isRequired = false 
}) => {
  const baseInputClasses = "shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500";
  const selectClasses = `${baseInputClasses} pr-8 ${validationError ? 'border-red-500' : 'border-gray-300'}`;
  const inputClasses = `${baseInputClasses} ${validationError ? 'border-red-500' : 'border-gray-300'}`;

  return (
  <div className="mb-4">
    <label htmlFor={name} className="block text-gray-700 text-sm font-bold mb-2">
      {label} {isRequired && <span className="text-red-500">*</span>}
    </label>
    
    {type === 'select' ? (
        <div className="relative">
      <select
        id={name}
        name={name}
        value={value}
        onChange={onChange}
            className={selectClasses}
      >
        <option value="" disabled hidden>{`Select ${label}`}</option>
        {options?.map((option) => (
          <option key={option.value} value={option.value} disabled={option.disabled}>
            {option.label}
          </option>
        ))}
      </select>
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"/>
            </svg>
          </div>
        </div>
    ) : type === 'textarea' ? (
      <textarea
        id={name}
        name={name}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        rows="3"
          className={inputClasses}
      />
    ) : (
      <input
        type={type}
        id={name}
        name={name}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
          className={inputClasses}
      />
    )}
    
    {helpText && <p className="text-gray-600 text-xs italic mt-1">{helpText}</p>}
    {validationError && <p className="text-red-500 text-xs italic mt-1">{validationError}</p>}
  </div>
);
});

FormField.displayName = 'FormField';

export { FormField };
export default FormField;