// Mock data for insurance companies
export const mockInsuranceCompanies = [
  {
    id: 1,
    name: "Britam Insurance",
    registrationNumber: "IRA/2023/001",
    licenseNumber: "LIC/2023/001",
    regulatoryBody: "Insurance Regulatory Authority",
    contactPerson: "<PERSON>",
    email: "<EMAIL>",
    phone: "+254700123456",
    website: "https://www.britam.com",
    address: "Britam Tower, Ragati Road",
    city: "Nairobi",
    state: "Nairobi",
    postalCode: "00100",
    country: "Kenya",
    businessType: "Public Limited Company",
    foundedYear: 1970,
    annualRevenue: 50000000,
    employeeCount: 2500,
    description: "Britam is a leading financial services group listed on the Nairobi Securities Exchange with operations in Kenya, Uganda, Tanzania, Rwanda, South Sudan, Mozambique and Malawi.",
    status: "Active",
    productsCount: 15,
    policiesCount: 12,
    createdAt: "2023-01-15T10:30:00Z"
  },
  {
    id: 2,
    name: "AIG Kenya",
    registrationNumber: "IRA/2023/002",
    licenseNumber: "LIC/2023/002",
    regulatoryBody: "Insurance Regulatory Authority",
    contactPerson: "<PERSON>",
    email: "<EMAIL>",
    phone: "+254700234567",
    website: "https://www.aig.com/kenya",
    address: "AIG House, Westlands",
    city: "Nairobi",
    state: "Nairobi",
    postalCode: "00800",
    country: "Kenya",
    businessType: "Private Limited Company",
    foundedYear: 1985,
    annualRevenue: 35000000,
    employeeCount: 1800,
    description: "AIG Kenya provides a wide range of insurance products and services to individuals and businesses across Kenya.",
    status: "Active",
    productsCount: 8,
    policiesCount: 8,
    createdAt: "2023-01-14T14:20:00Z"
  },
  {
    id: 3,
    name: "CIC Insurance",
    registrationNumber: "IRA/2023/003",
    licenseNumber: "LIC/2023/003",
    regulatoryBody: "Insurance Regulatory Authority",
    contactPerson: "Mike Johnson",
    email: "<EMAIL>",
    phone: "+254700345678",
    website: "https://www.cic.co.ke",
    address: "CIC Plaza, Mara Road",
    city: "Nairobi",
    state: "Nairobi",
    postalCode: "00100",
    country: "Kenya",
    businessType: "Public Limited Company",
    foundedYear: 1978,
    annualRevenue: 42000000,
    employeeCount: 2200,
    description: "CIC Insurance Group is a leading insurance company in Kenya with a strong presence in the East African region.",
    status: "Pending",
    productsCount: 12,
    policiesCount: 9,
    createdAt: "2023-01-13T09:15:00Z"
  },
  {
    id: 4,
    name: "Jubilee Insurance",
    registrationNumber: "IRA/2023/004",
    licenseNumber: "LIC/2023/004",
    regulatoryBody: "Insurance Regulatory Authority",
    contactPerson: "Sarah Wilson",
    email: "<EMAIL>",
    phone: "+254700456789",
    website: "https://www.jubileeinsurance.com",
    address: "Jubilee Insurance House, Wabera Street",
    city: "Nairobi",
    state: "Nairobi",
    postalCode: "00100",
    country: "Kenya",
    businessType: "Public Limited Company",
    foundedYear: 1937,
    annualRevenue: 75000000,
    employeeCount: 3000,
    description: "Jubilee Insurance is one of the oldest and most trusted insurance companies in East Africa.",
    status: "Active",
    productsCount: 20,
    policiesCount: 10,
    createdAt: "2023-01-12T11:45:00Z"
  },
  {
    id: 5,
    name: "UAP Insurance",
    registrationNumber: "IRA/2023/005",
    licenseNumber: "LIC/2023/005",
    regulatoryBody: "Insurance Regulatory Authority",
    contactPerson: "David Brown",
    email: "<EMAIL>",
    phone: "+254700567890",
    website: "https://www.uap.co.ke",
    address: "UAP Old Mutual Tower, Upperhill",
    city: "Nairobi",
    state: "Nairobi",
    postalCode: "00100",
    country: "Kenya",
    businessType: "Public Limited Company",
    foundedYear: 1926,
    annualRevenue: 60000000,
    employeeCount: 2800,
    description: "UAP Insurance is a leading insurance company with a rich history and strong market presence.",
    status: "Active",
    productsCount: 18,
    policiesCount: 15,
    createdAt: "2023-01-11T16:30:00Z"
  }
];

// Mock data for products
export const mockProducts = [
  {
    id: 1,
    name: "Comprehensive Motor Insurance",
    type: "Motor",
    premium: 25000,
    status: "Active",
    companyId: 1,
    companyName: "Britam Insurance",
    description: "Comprehensive motor insurance covering third party, theft, and damage",
    createdAt: "2023-01-15T10:30:00Z"
  },
  {
    id: 2,
    name: "Third Party Motor Insurance",
    type: "Motor",
    premium: 8000,
    status: "Active",
    companyId: 1,
    companyName: "Britam Insurance",
    description: "Basic third party motor insurance coverage",
    createdAt: "2023-01-15T10:30:00Z"
  },
  {
    id: 3,
    name: "Personal Accident Cover",
    type: "Personal",
    premium: 5000,
    status: "Active",
    companyId: 2,
    companyName: "AIG Kenya",
    description: "Personal accident insurance for individuals",
    createdAt: "2023-01-14T14:20:00Z"
  },
  {
    id: 4,
    name: "Home Insurance",
    type: "Property",
    premium: 15000,
    status: "Active",
    companyId: 2,
    companyName: "AIG Kenya",
    description: "Comprehensive home insurance coverage",
    createdAt: "2023-01-14T14:20:00Z"
  },
  {
    id: 5,
    name: "Life Insurance",
    type: "Life",
    premium: 12000,
    status: "Active",
    companyId: 3,
    companyName: "CIC Insurance",
    description: "Term life insurance policy",
    createdAt: "2023-01-13T09:15:00Z"
  }
];

// Mock data for cover types
export const mockCoverTypes = [
  {
    id: 1,
    name: "Comprehensive",
    description: "Full coverage including third party, theft, and damage",
    insurerId: 1,
    category: "Motor",
    status: "Active"
  },
  {
    id: 2,
    name: "Third Party",
    description: "Basic third party coverage only",
    insurerId: 2,
    category: "Motor",
    status: "Active"
  },
  {
    id: 3,
    name: "Third Party Fire & Theft",
    description: "Third party coverage plus fire and theft protection",
    insurerId: 3,
    category: "Motor",
    status: "Active"
  },
  {
    id: 4,
    name: "Term Life",
    description: "Life insurance for a specific term",
    insurerId: 1,
    category: "Life",
    status: "Active"
  },
  {
    id: 5,
    name: "Whole Life",
    description: "Life insurance for the entire life of the insured",
    insurerId: 4,
    category: "Life",
    status: "Active"
  }
];

// Mock data for benefits (replace with provided examples)
export const mockBenefits = [
  {
    id: 1,
    name: "Windscreen Cover",
    description: "Covers windscreen replacement up to KES 30,000",
    status: "Active",
    policies: [1, 2]
  },
  {
    id: 2,
    name: "Road Rescue",
    description: "24/7 breakdown and towing assistance",
    status: "Active",
    policies: [1, 2]
  },
  {
    id: 3,
    name: "Medical Expenses",
    description: "Covers up to KES 100,000 for injuries to insured",
    status: "Active",
    policies: [2]
  },
  {
    id: 4,
    name: "Courtesy Car",
    description: "Temporary vehicle provided during repair",
    status: "Active",
    policies: [2]
  },
  {
    id: 5,
    name: "Theft & Fire Cover",
    description: "Compensation in case of theft or fire damage",
    status: "Active",
    policies: [4]
  },
  // Additional benefits for more options
  {
    id: 6,
    name: "Personal Accident Cover",
    description: "Covers personal accidents for driver and passengers",
    status: "Active",
    policies: []
  },
  {
    id: 7,
    name: "No Claim Bonus",
    description: "Discount for claim-free years",
    status: "Active",
    policies: []
  },
  {
    id: 8,
    name: "Loss of Use",
    description: "Compensation for loss of vehicle use during repairs",
    status: "Active",
    policies: []
  },
  {
    id: 9,
    name: "Emergency Medical Evacuation",
    description: "Covers emergency evacuation costs after an accident",
    status: "Active",
    policies: []
  },
  {
    id: 10,
    name: "Third Party Property Damage",
    description: "Covers damage to third party property",
    status: "Active",
    policies: []
  },
  {
    id: 11,
    name: "Riots & Strikes Cover",
    description: "Covers damages due to riots and strikes",
    status: "Active",
    policies: []
  },
  {
    id: 12,
    name: "Flood & Natural Disaster Cover",
    description: "Covers damages from floods and natural disasters",
    status: "Active",
    policies: []
  }
];

// Mock data for policies (replace with provided examples)
export const mockPolicies = [
  {
    id: 1,
    name: "Standard",
    coverTypeId: 1, // Comprehensive
    insurerId: 1, // APA
    premiumRate: 4.5,
    basePremium: 25000,
    sumInsured: 1000000,
    commissionRate: 10,
    vehicleTypes: ["Private", "Commercial"],
    benefits: "Windscreen cover, roadside rescue, medical expenses",
    description: "Basic comprehensive cover with windscreen + rescue",
    status: "Active",
    createdAt: "2023-02-01T10:00:00Z"
  },
  {
    id: 2,
    name: "Gold",
    coverTypeId: 1, // Comprehensive
    insurerId: 2, // APA
    premiumRate: 5.0,
    basePremium: 30000,
    sumInsured: 1500000,
    commissionRate: 12,
    vehicleTypes: ["Private"],
    benefits: "Medical, courtesy car, personal accident",
    description: "Enhanced cover with medical + courtesy car",
    status: "Active",
    createdAt: "2023-02-05T11:30:00Z"
  },
  {
    id: 3,
    name: "Essential",
    coverTypeId: 2, // Third-Party Only
    insurerId: 3, // APA
    premiumRate: 2.5,
    basePremium: 8000,
    sumInsured: 500000,
    commissionRate: 8,
    vehicleTypes: ["Commercial"],
    benefits: "Third-party liability only",
    description: "Basic third-party cover only",
    status: "Active",
    createdAt: "2023-02-10T09:15:00Z"
  },
  {
    id: 4,
    name: "Silver",
    coverTypeId: 3, // TPFT
    insurerId: 4, // APA
    premiumRate: 3.2,
    basePremium: 12000,
    sumInsured: 700000,
    commissionRate: 9,
    vehicleTypes: ["Special"],
    benefits: "Fire, theft, third-party",
    description: "Covers third-party, fire, and theft",
    status: "Active",
    createdAt: "2023-02-12T14:00:00Z"
  }
];

// Mock company statistics
export const mockCompanyStats = {
  totalProducts: 1,
  totalPolicies: 1200,
  activePolicies: 1150,
  totalRevenue: 25000000,
  monthlyGrowth: 12.5,
  customerSatisfaction: 94,
  claimsRatio: 65,
  renewalRate: 85
};

// Mock data for clients
export const mockClients = [
  {
    id: 1,
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "+254700123456",
    idNumber: "12345678",
    dateOfBirth: "1985-03-15",
    address: "123 Main Street",
    city: "Nairobi",
    postalCode: "00100",
    occupation: "Software Engineer",
    employer: "Tech Solutions Ltd",
    annualIncome: 1200000,
    status: "Active",
    createdAt: "2023-01-10T09:00:00Z"
  },
  {
    id: 2,
    firstName: "Jane",
    lastName: "Smith",
    email: "<EMAIL>",
    phone: "+254700234567",
    idNumber: "23456789",
    dateOfBirth: "1990-07-22",
    address: "456 Oak Avenue",
    city: "Mombasa",
    postalCode: "80100",
    occupation: "Marketing Manager",
    employer: "Global Marketing Co",
    annualIncome: 1800000,
    status: "Active",
    createdAt: "2023-01-12T14:30:00Z"
  },
  {
    id: 3,
    firstName: "Michael",
    lastName: "Johnson",
    email: "<EMAIL>",
    phone: "+254700345678",
    idNumber: "34567890",
    dateOfBirth: "1978-11-08",
    address: "789 Pine Road",
    city: "Kisumu",
    postalCode: "40100",
    occupation: "Business Owner",
    employer: "Johnson Enterprises",
    annualIncome: 3500000,
    status: "Active",
    createdAt: "2023-01-15T11:45:00Z"
  }
];

// Mock data for vehicles
export const mockVehicles = [
  {
    id: 1,
    registrationNumber: "KCA 123A",
    make: "Toyota",
    model: "Corolla",
    year: 2020,
    color: "White",
    engineCapacity: "1.8L",
    fuelType: "Petrol",
    transmission: "Automatic",
    mileage: 45000,
    estimatedValue: 2500000,
    clientId: 1,
    status: "Active",
    createdAt: "2023-01-10T10:00:00Z"
  },
  {
    id: 2,
    registrationNumber: "KCB 456B",
    make: "Honda",
    model: "Civic",
    year: 2019,
    color: "Black",
    engineCapacity: "1.5L",
    fuelType: "Petrol",
    transmission: "Manual",
    mileage: 32000,
    estimatedValue: 1800000,
    clientId: 2,
    status: "Active",
    createdAt: "2023-01-12T15:00:00Z"
  },
  {
    id: 3,
    registrationNumber: "KCC 789C",
    make: "Nissan",
    model: "X-Trail",
    year: 2021,
    color: "Silver",
    engineCapacity: "2.0L",
    fuelType: "Diesel",
    transmission: "Automatic",
    mileage: 28000,
    estimatedValue: 3200000,
    clientId: 3,
    status: "Active",
    createdAt: "2023-01-15T12:00:00Z"
  }
];

// Mock data for quotes
export const mockQuotes = [
  {
    id: 1,
    quoteNumber: "QT-2024-001",
    clientId: 1,
    clientName: "John Doe",
    clientEmail: "<EMAIL>",
    clientPhone: "+254700123456",
    vehicleId: 1,
    vehicleDetails: {
      registrationNumber: "KCA 123A",
      make: "Toyota",
      model: "Corolla",
      year: 2020,
      estimatedValue: 2500000
    },
    insurerId: 1,
    insurerName: "Britam Insurance",
    policyId: 1,
    policyName: "Standard",
    coverTypeId: 1,
    coverTypeName: "Comprehensive",
    premiumAmount: 112500,
    premiumRate: 4.5,
    vehicleValue: 2500000,
    startDate: "2024-01-01",
    endDate: "2024-12-31",
    status: "Pending",
    brokerId: 1,
    brokerName: "Sarah Wilson",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-15T10:30:00Z"
  },
  {
    id: 2,
    quoteNumber: "QT-2024-002",
    clientId: 2,
    clientName: "Jane Smith",
    clientEmail: "<EMAIL>",
    clientPhone: "+254700234567",
    vehicleId: 2,
    vehicleDetails: {
      registrationNumber: "KCB 456B",
      make: "Honda",
      model: "Civic",
      year: 2019,
      estimatedValue: 1800000
    },
    insurerId: 2,
    insurerName: "AIG Kenya",
    policyId: 2,
    policyName: "Gold",
    coverTypeId: 1,
    coverTypeName: "Comprehensive",
    premiumAmount: 90000,
    premiumRate: 5.0,
    vehicleValue: 1800000,
    startDate: "2024-01-01",
    endDate: "2024-12-31",
    status: "Approved",
    brokerId: 2,
    brokerName: "David Brown",
    createdAt: "2024-01-16T14:20:00Z",
    updatedAt: "2024-01-18T09:15:00Z"
  },
  {
    id: 3,
    quoteNumber: "QT-2024-003",
    clientId: 3,
    clientName: "Michael Johnson",
    clientEmail: "<EMAIL>",
    clientPhone: "+254700345678",
    vehicleId: 3,
    vehicleDetails: {
      registrationNumber: "KCC 789C",
      make: "Nissan",
      model: "X-Trail",
      year: 2021,
      estimatedValue: 3200000
    },
    insurerId: 4,
    insurerName: "Jubilee Insurance",
    policyId: 4,
    policyName: "Silver",
    coverTypeId: 3,
    coverTypeName: "Third Party Fire & Theft",
    premiumAmount: 102400,
    premiumRate: 3.2,
    vehicleValue: 3200000,
    startDate: "2024-01-01",
    endDate: "2024-12-31",
    status: "Rejected",
    brokerId: 1,
    brokerName: "Sarah Wilson",
    createdAt: "2024-01-17T11:45:00Z",
    updatedAt: "2024-01-19T16:30:00Z"
  },
  {
    id: 4,
    quoteNumber: "QT-2024-004",
    clientId: 1,
    clientName: "John Doe",
    clientEmail: "<EMAIL>",
    clientPhone: "+254700123456",
    vehicleId: 1,
    vehicleDetails: {
      registrationNumber: "KCA 123A",
      make: "Toyota",
      model: "Corolla",
      year: 2020,
      estimatedValue: 2500000
    },
    insurerId: 5,
    insurerName: "UAP Insurance",
    policyId: 3,
    policyName: "Essential",
    coverTypeId: 2,
    coverTypeName: "Third Party",
    premiumAmount: 62500,
    premiumRate: 2.5,
    vehicleValue: 2500000,
    startDate: "2024-01-01",
    endDate: "2024-12-31",
    status: "Pending",
    brokerId: 2,
    brokerName: "David Brown",
    createdAt: "2024-01-20T08:00:00Z",
    updatedAt: "2024-01-20T08:00:00Z"
  },
  {
    id: 5,
    quoteNumber: "QT-2024-005",
    clientId: 2,
    clientName: "Jane Smith",
    clientEmail: "<EMAIL>",
    clientPhone: "+254700234567",
    vehicleId: 2,
    vehicleDetails: {
      registrationNumber: "KCB 456B",
      make: "Honda",
      model: "Civic",
      year: 2019,
      estimatedValue: 1800000
    },
    insurerId: 3,
    insurerName: "CIC Insurance",
    policyId: 1,
    policyName: "Standard",
    coverTypeId: 1,
    coverTypeName: "Comprehensive",
    premiumAmount: 81000,
    premiumRate: 4.5,
    vehicleValue: 1800000,
    startDate: "2024-01-01",
    endDate: "2024-12-31",
    status: "Approved",
    brokerId: 1,
    brokerName: "Sarah Wilson",
    createdAt: "2024-01-22T13:15:00Z",
    updatedAt: "2024-01-24T10:45:00Z"
  }
];

// Mock data for users
export const mockUsers = [
  {
    id: 1,
    name: "John Doe",
    email: "<EMAIL>",
    role: "broker",
    phone: "+254700123456",
    status: "active",
    createdAt: "2023-01-15T10:30:00Z"
  },
  {
    id: 2,
    name: "Jane Smith",
    email: "<EMAIL>",
    role: "admin",
    phone: "+254700234567",
    status: "active",
    createdAt: "2023-01-14T14:20:00Z"
  }
]; 