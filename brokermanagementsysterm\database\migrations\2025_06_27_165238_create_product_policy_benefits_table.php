<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('IBMS_POLICY_BENEFITS', function (Blueprint $table) {
            $table->uuid('policy_benefit_id')->primary()->unique();

            $table->foreignUuid('insurance_product_policy_id')
                ->constrained('IBMS_INSURANCE_PRODUCT_POLICIES', 'insurance_product_policy_id')
                ->notnull();

            $table->string('deescription');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IBMS_POLICY_BENEFITS');
    }
};
