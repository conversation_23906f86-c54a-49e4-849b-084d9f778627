// src/Styles/uiTheme.js

export const buttonStyles = {
  variants: {
    primary: "bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",
    secondary: "bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500",
    success: "bg-green-600 text-white hover:bg-green-700 focus:ring-green-500",
    danger: "bg-red-600 text-white hover:bg-red-700 focus:ring-red-500",
    warning:
      "bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500",
    outline:
      "border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-blue-500",
    ghost: "text-gray-700 hover:bg-gray-100 focus:ring-gray-500",
  },
  sizes: {
    small: "px-3 py-1.5 text-sm",
    default: "px-4 py-2 text-sm",
    large: "px-6 py-3 text-base",
  },
};

export const statusBadgeStyles = {
  config: {
    Active: {
      bgColor: "bg-green-100",
      textColor: "text-green-800",
      borderColor: "border-green-200",
    },
    Inactive: {
      bgColor: "bg-gray-100",
      textColor: "text-gray-800",
      borderColor: "border-gray-200",
    },
    Pending: {
      bgColor: "bg-yellow-100",
      textColor: "text-yellow-800",
      borderColor: "border-yellow-200",
    },
    Suspended: {
      bgColor: "bg-red-100",
      textColor: "text-red-800",
      borderColor: "border-red-200",
    },
    Approved: {
      bgColor: "bg-blue-100",
      textColor: "text-blue-800",
      borderColor: "border-blue-200",
    },
  },
  sizes: {
    small: "px-2 py-1 text-xs",
    default: "px-2.5 py-0.5 text-xs",
    large: "px-3 py-1 text-sm",
  },
};

export const modalStyles = {
  sizes: {
    small: "max-w-md",
    default: "max-w-lg",
    large: "max-w-2xl",
    xlarge: "max-w-4xl",
  },
};

export const cardStyles = {
  padding: {
    none: "",
    small: "p-4",
    default: "p-6",
    large: "p-8",
  },
  shadow: {
    none: "",
    small: "shadow-sm",
    default: "shadow-md",
    large: "shadow-lg",
    xlarge: "shadow-xl",
  },
};

export const alertStyles = {
  config: {
    info: {
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      textColor: "text-blue-800",
      iconColor: "text-blue-400",
    },
    success: {
      bgColor: "bg-green-50",
      borderColor: "border-green-200",
      textColor: "text-green-800",
      iconColor: "text-green-400",
    },
    warning: {
      bgColor: "bg-yellow-50",
      borderColor: "border-yellow-200",
      textColor: "text-yellow-800",
      iconColor: "text-yellow-400",
    },
    error: {
      bgColor: "bg-red-50",
      borderColor: "border-red-200",
      textColor: "text-red-800",
      iconColor: "text-red-400",
    },
  },
};

export const dashboardStatsStyles = {
  colorMap: {
    blue: "bg-blue-100 text-blue-600 border-blue-200",
    green: "bg-green-100 text-green-600 border-green-200",
    purple: "bg-purple-100 text-purple-600 border-purple-200",
    yellow: "bg-yellow-100 text-yellow-600 border-yellow-200",
    emerald: "bg-emerald-100 text-emerald-600 border-emerald-200",
    orange: "bg-orange-100 text-orange-600 border-orange-200",
    pink: "bg-pink-100 text-pink-600 border-pink-200",
    indigo: "bg-indigo-100 text-indigo-600 border-indigo-200",
  },
};

export const formWizardStyles = {
  step: {
    completed: {
      container: "bg-green-600 border-green-600 text-white",
      label: "text-green-600",
      connector: "bg-green-600",
    },
    current: {
      container: "bg-blue-600 border-blue-600 text-white",
      label: "text-blue-600",
      connector: "bg-gray-300",
    },
    upcoming: {
      container: "bg-white border-gray-300 text-gray-500",
      label: "text-gray-500",
      connector: "bg-gray-300",
    },
  },
};

export const pageHeaderStyles = {
  breadcrumbs: {
    inactive: "text-gray-500 hover:text-gray-700",
    active: "text-gray-900",
  },
};

export const tablesStyles = {
  statusClasses: {
    Active: "bg-green-100 text-green-800",
    Inactive: "bg-gray-100 text-gray-800",
    Pending: "bg-yellow-100 text-yellow-800",
  },
};
