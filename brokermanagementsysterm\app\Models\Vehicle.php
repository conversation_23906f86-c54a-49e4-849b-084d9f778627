<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @OA\Schema(
 *   schema="Vehicle",
 *   type="object",
 *   title="IBMS Vehicle",
 *   description="Vehicle registered in the IBMS system",
 *   required={"vehicle_id", "mileage", "make", "model", "registration_number", "value", "cover_type", "client_id"},
 *
 *   @OA\Property(
 *     property="vehicle_id",
 *     type="integer",
 *     format="int64",
 *     description="Unique identifier of the vehicle"
 *   ),
 *   @OA\Property(
 *     property="mileage",
 *     type="integer",
 *     description="Current mileage reading of the vehicle"
 *   ),
 *   @OA\Property(
 *     property="make",
 *     type="string",
 *     description="Manufacturer of the vehicle (e.g. Toyota)"
 *   ),
 *   @OA\Property(
 *     property="model",
 *     type="string",
 *     description="Vehicle model (e.g. Corolla)"
 *   ),
 *   @OA\Property(
 *     property="registration_number",
 *     type="string",
 *     description="Unique registration plate number"
 *   ),
 *   @OA\Property(
 *     property="value",
 *     type="number",
 *     format="float",
 *     description="Monetary value of the vehicle"
 *   ),
 *   @OA\Property(
 *     property="cover_type",
 *     type="integer",
 *     description="Associated insurance cover type ID"
 *   ),
 *   @OA\Property(
 *     property="client_id",
 *     type="integer",
 *     description="Client who owns this vehicle"
 *   ),
 *   @OA\Property(
 *     property="created_at",
 *     type="string",
 *     format="date-time",
 *     description="Timestamp the vehicle record was created"
 *   ),
 *   @OA\Property(
 *     property="updated_at",
 *     type="string",
 *     format="date-time",
 *     description="Timestamp the vehicle record was last updated"
 *   )
 * )
 */
class Vehicle extends Model
{
    //
    use HasFactory;

    protected $table = 'IBMS_VEHICLES';

    protected $primaryKey = 'vehicle_id';

    public $incrementing = true;

    protected $keyType = 'int';

    protected $fillable = [
        'client_id',
        'make',
        'model',
        'registration_number',
        'mileage',
        'value',
        'cover_type',
        'vehicle_purpose',
        'vehicle_preferences',
    ];

    protected $casts = ['cover_type' => 'int', 'client_id' => 'string', 'vehicle_preferences' => 'int'];

    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function coverType()
    {
        return $this->belongsTo(CategoryProduct::class, 'cover_type');
    }

    public function preferences()
    {
        return $this->hasOne(ClientPreferences::class, 'vehicle_id', 'vehicle_id');
    }
}
