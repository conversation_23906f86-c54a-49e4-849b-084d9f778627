import React from 'react';
import {
  Users, Briefcase, FileText, Clock, DollarSign, Activity, PlusCircle,
  PieChart, BarChart, Car, HeartHandshake, Home, Shield, CheckCircle, XCircle, Clock4,
  UserPlus, FilePlus, BookOpen // Specific icons for broker actions
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const Dashboard = () => { // Renamed from BrokerDashboard to Dashboard
  const navigate = useNavigate();
  // Mock Data for demonstration purposes
  const stats = [
    { name: 'Total Clients', value: '350', icon: Users, color: 'text-blue-500', bgColor: 'bg-blue-100' },
    { name: 'Active Policies', value: '280', icon: Briefcase, color: 'text-green-500', bgColor: 'bg-green-100' },
    { name: 'Pending Quotes', value: '18', icon: FileText, color: 'text-yellow-500', bgColor: 'bg-yellow-100' },
    { name: 'Policies Expiring (30 days)', value: '12', icon: Clock, color: 'text-red-500', bgColor: 'bg-red-100' },
    // Changed from Commissions Earned to Total Vehicles Onboarded
    { name: 'Total Vehicles Onboarded', value: '450', icon: Car, color: 'text-purple-500', bgColor: 'bg-purple-100' },
  ];

  // Removed recentClientActivities as requested

  const quickActions = [
    { name: 'Add New Client', icon: UserPlus, link: '/broker/clients/create', color: 'bg-blue-600' },
    { name: 'Create New Quote', icon: FilePlus, link: '/broker/quotes/create', color: 'bg-indigo-600' },
    { name: 'View Policy Catalog', icon: BookOpen, link: '/broker/policy-catalog', color: 'bg-green-600' },
  ];

  // Mock data for Policy Type Distribution Horizontal Bar Chart
  const policyTypeData = [
    { type: 'Comprehensive', count: 180, color: 'bg-blue-500' },
    { type: 'Third Party Fire & Theft', count: 70, color: 'bg-purple-500' },
    { type: 'Third Party', count: 30, color: 'bg-teal-500' },
  ];
  const maxPolicyTypeCount = Math.max(...policyTypeData.map(item => item.count));


  // Mock data for Quotes by Status Bar Chart
  const quotesByStatusData = [
    { status: 'Approved', count: 65, color: 'bg-green-500' },
    { status: 'Pending', count: 18, color: 'bg-yellow-500' },
    { status: 'Rejected', count: 7, color: 'bg-red-500' },
  ];
  const maxQuoteCount = Math.max(...quotesByStatusData.map(item => item.count));


  return (
    <div className="p-8 bg-gray-100 min-h-screen font-inter">
      <h1 className="text-4xl font-extrabold text-gray-900 mb-8">Broker Dashboard</h1>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 mb-10">
        {stats.map((stat, index) => (
          <div
            key={index}
            className="bg-white rounded-xl shadow-lg p-6 flex items-center space-x-4 transform transition-transform duration-300 hover:scale-105 hover:shadow-xl"
          >
            <div className={`p-3 rounded-full ${stat.bgColor}`}>
              <stat.icon size={24} className={stat.color} />
            </div>
            <div>
              <p className="text-gray-500 text-sm font-medium">{stat.name}</p>
              <h2 className="text-2xl font-bold text-gray-900">{stat.value}</h2>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions - Three buttons on one horizontal line */}
      <div className="bg-white rounded-xl shadow-lg p-6 mb-10">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <PlusCircle size={24} className="mr-3 text-gray-600" /> Quick Actions
        </h2>
        <div className="flex flex-wrap justify-center gap-4"> {/* Changed to justify-center for horizontal alignment */}
          {quickActions.map((action, index) => (
            <button
              key={index}
              onClick={() => navigate(action.link)} // Replace with navigate(action.link)
              className={`flex-1 min-w-[180px] max-w-[300px] flex items-center justify-center py-3 px-4 rounded-lg text-white font-semibold
                         ${action.color} hover:opacity-90 transition-opacity duration-200 shadow-md`}
            >
              <action.icon size={20} className="mr-3" />
              {action.name}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Policy Type Distribution Horizontal Bar Graph */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <BarChart size={24} className="mr-3 text-gray-600" /> Motor Insurance Products Distribution
          </h2>
          <div className="space-y-4 py-4"> {/* Container for horizontal bars */}
            {policyTypeData.map((item, index) => (
              <div key={index} className="flex items-center">
                <span className="w-40 text-right text-sm font-medium text-gray-700 mr-4">{item.type}</span>
                <div className="flex-grow bg-gray-200 rounded-full h-6 relative overflow-hidden">
                  <div
                    className={`h-full rounded-full ${item.color} transition-all duration-500 ease-out`}
                    style={{ width: `${(item.count / maxPolicyTypeCount) * 100}%` }}
                  ></div>
                  <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs font-semibold text-white">
                    {item.count}
                  </span>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-6 text-center text-gray-600">
            <p className="text-lg font-semibold">Total Policies by Type: {policyTypeData.reduce((sum, item) => sum + item.count, 0)}</p>
          </div>
        </div>

        {/* Quotes by Status Horizontal Bar Graph */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <BarChart size={24} className="mr-3 text-gray-600" /> Quotes by Status
          </h2>
          <div className="space-y-4 py-4">
            {quotesByStatusData.map((item, index) => (
              <div key={index} className="flex items-center">
                <span className="w-24 text-right text-sm font-medium text-gray-700 mr-4">{item.status}</span>
                <div className="flex-grow bg-gray-200 rounded-full h-6 relative overflow-hidden">
                  <div
                    className={`h-full rounded-full ${item.color} transition-all duration-500 ease-out`}
                    style={{ width: `${(item.count / maxQuoteCount) * 100}%` }}
                  ></div>
                  <span className="absolute right-2 top-1/2 -translate-y-1/2 text-xs font-semibold text-white">
                    {item.count}
                  </span>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-6 text-center text-gray-600">
            <p className="text-lg font-semibold">Total Quotes: {quotesByStatusData.reduce((sum, item) => sum + item.count, 0)}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard; // Renamed export to Dashboard
