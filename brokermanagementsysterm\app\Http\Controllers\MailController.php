<?PHP

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\QuotationMail;

class MailController extends Controller
{
    /**
     * Write code on Method
     *

     */
    public function index()
    {
        $mailData = [
            'title' => 'Mail from ' . config('app.name'),
            'body' => 'This is for testing email using smtp.'
        ];

        Mail::to('<EMAIL>')->send(new QuotationMail($mailData));

        dd("Email is sent successfully.");
    }
}
