<?php

use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\CategoryProduct\CategoryProductController;
use App\Http\Controllers\Client\ClientController;
use App\Http\Controllers\ClientPreferences\ClientPreferencesController;
use App\Http\Controllers\Insurance\InsuranceController;
use App\Http\Controllers\QuotationController;
use App\Http\Controllers\ServiceControllers\RecommendationController;
use App\Http\Controllers\Vehicle\VehicleController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::prefix('v1')->middleware(['auth:sanctum'])->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // Vehicle routes (protected, broker role)
    Route::middleware(['broker'])->group(function () {
        Route::get('/vehicles', [VehicleController::class, 'index']);
        Route::get('/vehicles/client/{clientid}', [VehicleController::class, 'index_by_client']);
        Route::get('/vehicles/{id}', [VehicleController::class, 'show']);
        Route::post('/vehicles', [VehicleController::class, 'store']);
        Route::put('/vehicles/{id}', [VehicleController::class, 'update']);
        Route::delete('/vehicles/{id}', [VehicleController::class, 'destroy']);
    });
    Route::post('/users/store', [RegisteredUserController::class, 'store'])->name('user.store');

    Route::get('/users/edit/{id}', [RegisteredUserController::class, 'edit'])->name('user.edit');
    // Route::post('/users/store', [RegisteredUserController::class, 'store'])->name('user.store');
    Route::put('/users/update', [RegisteredUserController::class, 'update'])->name('user.update');
    Route::delete('/users/destroy/{id}', [RegisteredUserController::class, 'destroy'])->name('user.destroy');
    Route::get('/users/list', [RegisteredUserController::class, 'index'])->name('user.list');

    Route::get('/users/restore/{id}', [RegisteredUserController::class, 'restore'])->name('user.restore');
    Route::post('/users/change-password', [RegisteredUserController::class, 'changePassword'])->name('user.changePassword');

    // Client routes (protected, broker role)
    Route::middleware(['broker'])->group(function () {
        Route::get('/clients', [ClientController::class, 'index']);
        Route::get('/clients/{id}', [ClientController::class, 'show']);
        Route::post('/clients', [ClientController::class, 'store']);
        Route::put('/clients/{id}', [ClientController::class, 'update']);
        Route::delete('/clients/{id}', [ClientController::class, 'destroy']);
    });
    Route::middleware(['admin'])->group(function () {
        // Cover Type routes (protected, broker role)
        Route::get('/cover-types', [CategoryProductController::class, 'index']);
        Route::get('/cover-types/{id}', [CategoryProductController::class, 'show']);
        Route::post('/cover-types', [CategoryProductController::class, 'store']);
        Route::put('/cover-types/{id}', [CategoryProductController::class, 'update']);
        Route::delete('/cover-types/{id}', [CategoryProductController::class, 'destroy']);
    });

    Route::middleware(['adminorbroker'])->group(function () {
        Route::get('/preferences', [ClientPreferencesController::class, 'index']);
        Route::post('/preferences/{client_id}/{vehicle_id}', [ClientPreferencesController::class, 'store']);
        Route::get('/preferences/client/{client_id}', [ClientPreferencesController::class, 'show_pref_by_client']);
        Route::get('/preferences/vehicle/{vehicle_id}', [ClientPreferencesController::class, 'show_pref_by_vehicle']);
        Route::put('/preferences/{pref_id}', [ClientPreferencesController::class, 'update']);
        Route::delete('/preferences/{pref_id}', [ClientPreferencesController::class, 'destroy']);
    });

    Route::middleware(['adminorbroker'])->group(function () {
        Route::post('/recommend', [RecommendationController::class, 'recommend']);

    });

    Route::middleware(['adminorbroker'])->group(function (): void {
        Route::post('/quote/create', [QuotationController::class, 'store']);
        Route::post('/quote/from-recommendation', [QuotationController::class, 'createFromRecommendation']);
        Route::put('/quote/accept/{quote_id}', [QuotationController::class, 'acceptQuote']);
        Route::delete('/quote/delete/{quote_id}', [QuotationController::class, 'destroy']);
        Route::put('/quote/edit/{quote_id}', [QuotationController::class, 'edit']);
        Route::get('/quote/{quote_id}', [QuotationController::class, 'show']);
        Route::get('/quote', [QuotationController::class, 'index']);
    });

    // insurance routes
    Route::middleware(['adminorbroker'])->prefix('insurance')->group(function () {
        // Create
        Route::post('companies', [InsuranceController::class, 'store']);

        // Read (All)
        Route::get('companies', [InsuranceController::class, 'index']);

        // Read (Single)
        Route::get('companies/{id}', [InsuranceController::class, 'show']);

        // Update
        Route::put('companies/{id}', [InsuranceController::class, 'update']);

        // Delete
        Route::delete('companies/{id}', [InsuranceController::class, 'destroy']);
    });
    // Route::middleware('auth:sanctum')->group(function () {
    //     Route::apiResource('insurance-companies', \App\Http\Controllers\InsuranceCompanyController::class);
});
