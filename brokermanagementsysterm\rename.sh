#!/bin/bash

# Usage: ./rename-var.sh OldName NewName [--dry-run]

set -e

if [ $# -lt 2 ]; then
  echo "Usage: $0 OldName NewName [--dry-run]"
  exit 1
fi

OLD_NAME=$1
NEW_NAME=$2
DRY_RUN=$3

# # Optional backup
# BACKUP_DIR="./__backup_rename_$(date +%s)"
# echo "Creating backup at $BACKUP_DIR..."
# cp -r . "$BACKUP_DIR"
# Optional backup
#BACKUP_DIR="./__backup_rename_$(date +%s)"
#echo "Creating backup at $BACKUP_DIR..."
#mkdir "$BACKUP_DIR"
#rsync -a --exclude="$BACKUP_DIR" . "$BACKUP_DIR"

echo "Finding PHP files excluding vendor/, storage/, and node_modules/..."

FILES=$(find . -type f -name "*.php" \
  -not -path "./vendor/*" \
  -not -path "./storage/*" \
  -not -path "./node_modules/*")

if [ "$DRY_RUN" == "--dry-run" ]; then
  echo "Running in dry-run mode. Changes will be shown but not applied."
  echo "Preview of changes:"
  for file in $FILES; do
    grep -w "$OLD_NAME" "$file" && echo "→ Would replace in: $file"
  done
  echo "Dry-run complete."
  exit 0
fi

echo "Replacing '$OLD_NAME' with '$NEW_NAME'..."

for file in $FILES; do
  sed -i "s/\b$OLD_NAME\b/$NEW_NAME/g" "$file"
done

echo "✔ Done. Replaced all instances of '$OLD_NAME' with '$NEW_NAME'."

