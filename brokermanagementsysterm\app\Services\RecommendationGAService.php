<?php

// namespace App\Services;

// use App\Models\Client;
// use App\Models\ClientPreferences;
// use App\Models\InsuranceCategoryProduct;

// class RecommendationGAService
// {
//     public function recommendForClient(Client $client, int $limit = 10)
//     {
//         $prefs = ClientPreferences::where('client_id', $client->client_id)->first();
//         if (! $prefs) {
//             return collect();
//         }

//         // Load related data to avoid N+1 queries
//         $population = InsuranceCategoryProduct::with(['insurance', 'categoryProduct'])
//             ->where('is_active', true)->get();

//         $scored = $population->map(function ($product) use ($prefs) {
//             $product->fitness = $this->evaluateFitness($product, $prefs);

//             return $product;
//         });
//         // foreach ($scored as $rec) {
//         //     dump($rec);}

//         return $scored->sortByDesc('fitness')->take($limit)->map(function ($product) {
//             return [
//                 'product_id' => $product->insurance_category_product_id,
//                 'cover_type' => $product->categoryProduct->category_product_name ?? 'N/A',
//                 'cover_description' => $product->categoryProduct->category_product_description ?? 'N/A',
//                 'insurer_name' => $product->insurance->name ?? 'MISSING',
//                 'price' => $product->price ?? 0,
//                 'fitness_score' => round($product->fitness, 3),
//             ];

//         });

//     }

//     private function evaluateFitness($product, $prefs): float
//     {
//         $score = 0.0;

//         // Ensure coverType comparison works
//         $coverType = $product->categoryProduct->category_product_name ?? null;

//         // Defensive decode: handle string or JSON cases
//         $preferredCovers = is_string($prefs->preferred_cover_types)
//             ? json_decode($prefs->preferred_cover_types, true)
//             : $prefs->preferred_cover_types;

//         if (is_array($preferredCovers) && $coverType && in_array($coverType, $preferredCovers)) {
//             $score += 0.4;
//         }

//         if (
//             isset($prefs->budget_min, $prefs->budget_max, $product->price) &&
//             is_numeric($product->price)
//         ) {
//             if ($product->price >= $prefs->budget_min && $product->price <= $prefs->budget_max) {
//                 $score += 0.3;
//             } elseif ($product->price <= ($prefs->budget_max * 1.1)) {
//                 $score += 0.1;
//             }
//         }

//         $preferredInsurers = is_string($prefs->preferred_insurers)
//             ? json_decode($prefs->preferred_insurers, true)
//             : ($prefs->preferred_insurers ?? []);

//         if (is_array($preferredInsurers) && in_array($product->insurance_id, $preferredInsurers)) {
//             $score += 0.2;
//         }

//         if (
//             isset($product->vehicle_usage, $prefs->vehicle_usage) &&
//             $product->vehicle_usage === $prefs->vehicle_usage
//         ) {
//             $score += 0.1;
//         }

//         return $score;
//     }
// }

namespace App\Services;

use App\Models\Client;
use App\Models\InsuranceCategoryProduct;
use App\Models\Vehicle;

class RecommendationGAService
{
    public function recommendForClient(Client $client, int $limit = 10)
    {
        // Load all vehicles with preferences for this client
        $vehicles = Vehicle::with('preferences')
            ->where('client_id', $client->client_id)
            ->get();

        if ($vehicles->isEmpty()) {
            return collect();
        }

        $recommendations = collect();

        foreach ($vehicles as $vehicle) {
            $top = $this->recommendForVehicle($vehicle, $limit);
            $recommendations = $recommendations->merge($top);
        }

        return $recommendations->sortByDesc('fitness_score')->take($limit);
    }

    public function recommendForVehicle(Vehicle $vehicle, int $limit = 10)
    {
        $prefs = $vehicle->preferences;

        if (! $prefs) {
            return collect(); // No preferences for this vehicle
        }

        // Load active insurance category products with necessary relationships
        $population = InsuranceCategoryProduct::with(['insurance', 'categoryProduct'])
            ->where('is_active', true)
            ->get();

        // Score each product based on the vehicle's preferences
        $scored = $population->map(function ($product) use ($prefs) {
            $product->fitness = $this->evaluateFitness($product, $prefs);

            return $product;
        });

        // Return top scored recommendations for this vehicle
        return $scored->sortByDesc('fitness')->take($limit)->map(function ($product) use ($vehicle) {
            return [
                'vehicle_id' => $vehicle->vehicle_id,
                'product_id' => $product->insurance_category_product_id,
                'cover_type' => $product->categoryProduct->category_product_name ?? 'N/A',
                'cover_description' => $product->categoryProduct->category_product_description ?? 'N/A',
                'insurer_name' => $product->insurance->name ?? 'MISSING',
                'price' => $product->price ?? 0,
                'fitness_score' => round($product->fitness, 3),
            ];
        });
    }

    private function evaluateFitness($product, $prefs): float
    {
        $score = 0.0;

        $coverType = $product->categoryProduct->category_product_name ?? null;

        $preferredCovers = is_string($prefs->preferred_cover_types)
            ? json_decode($prefs->preferred_cover_types, true)
            : $prefs->preferred_cover_types;

        if (is_array($preferredCovers) && $coverType && in_array($coverType, $preferredCovers)) {
            $score += 0.4;
        }

        if (
            isset($prefs->budget_min, $prefs->budget_max, $product->price) &&
            is_numeric($product->price)
        ) {
            if ($product->price >= $prefs->budget_min && $product->price <= $prefs->budget_max) {
                $score += 0.3;
            } elseif ($product->price <= ($prefs->budget_max * 1.1)) {
                $score += 0.1;
            }
        }

        $preferredInsurers = is_string($prefs->preferred_insurers)
            ? json_decode($prefs->preferred_insurers, true)
            : ($prefs->preferred_insurers ?? []);

        if (is_array($preferredInsurers) && in_array($product->insurance_id, $preferredInsurers)) {
            $score += 0.2;
        }

        if (
            isset($product->vehicle_usage, $prefs->vehicle) &&
            $product->vehicle_usage === $prefs->vehicle->vehicle_purpose
        ) {
            $score += 0.1;
        }

        return $score;
    }
}
