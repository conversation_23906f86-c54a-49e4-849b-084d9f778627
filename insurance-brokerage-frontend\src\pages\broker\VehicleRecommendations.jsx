import React, { useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import PageHeader from "../../components/PageHeader";
import Card from "../../components/Card";
import DataTable from "../../components/DataTable";
import { recommendationService } from "../../services/recommendationService";
import Modal from "../../components/Modal";

export default function VehicleRecommendations() {
  const location = useLocation();
  const navigate = useNavigate();
  const vehicle = location.state?.vehicle;
  const client = location.state?.client;
  const [selectedRec, setSelectedRec] = React.useState(null);
  const [showModal, setShowModal] = React.useState(false);

  // Get mock recommendations using the service
  const recommendations = useMemo(() => {
    if (!vehicle || !client) return [];
    return recommendationService.getRecommendations(vehicle, client);
  }, [vehicle, client]);

  const columns = [
    { key: "cover_type", label: "Cover Type" },
    { key: "cover_description", label: "Description" },
    { key: "insurer_name", label: "Insurer" },
    { key: "price", label: "Price", type: "currency" },
    { key: "fitness_score", label: "Fitness Score" },
  ];

  return (
    <div className="w-full mx-auto">
      <PageHeader
        title="Recommended Policies"
        subtitle={`Top policy recommendations for ${vehicle?.make} ${vehicle?.model} (${vehicle?.registration_number})`}
        breadcrumbs={[
          { label: "Broker Dashboard", href: "/broker/dashboard" },
          { label: "Clients", href: "/broker/clients", onClick: () => navigate("/broker/clients")},
          // { label: "Vehicle List", href: "/broker/vehicles/details", onClick: () => navigate("/broker/vehicles/details", { state: { client } }) },
          { label: `Recommendations for ${vehicle?.make} ${vehicle?.model}` },
        ].filter(Boolean)}
        actions={[
          {
            label: "Back to Vehicle",
            variant: "primary",
            onClick: () => navigate("/broker/vehicles/view", { state: { vehicle, client } }),
          },
        ]}
      />
      <div className="mt-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {recommendations.length === 0 ? (
          <Card title="No Recommendations" subtitle="No policies matched the criteria." />
        ) : (
          recommendations.map((rec, idx) => (
            <div
              key={rec.product_id}
              onClick={() => { setSelectedRec(rec); setShowModal(true); }}
              className="cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 hover:shadow-lg hover:scale-[1.03] hover:brightness-105 transition-all duration-150"
              tabIndex={0}
              onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') { setSelectedRec(rec); setShowModal(true); } }}
            >
              <Card title={rec.cover_type} subtitle={rec.cover_description}>
                <div className="flex flex-col gap-2 text-base">
                  <div><span className="font-medium text-gray-700">Insurer:</span> {rec.insurer_name}</div>
                  <div><span className="font-medium text-gray-700">Premium:</span> KES {rec.price.toLocaleString()}</div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-700">Fitness Score:</span>
                    <span>{rec.fitness_score}</span>
                    {/* Bar representation */}
                    <div className="flex-1 min-w-[60px] max-w-[120px] h-3 bg-gray-200 rounded-full overflow-hidden ml-2">
                      <div
                        className="h-full bg-green-500 rounded-full transition-all"
                        style={{ width: `${rec.fitness_score * 100}%` }}
                      />
                    </div>
                    {/* Star representation */}
                    <span className="ml-2 text-yellow-500">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <span key={i}>
                          {rec.fitness_score >= (i + 1) / 5 ? '★' : '☆'}
                        </span>
                      ))}
                    </span>
                  </div>
                  {/* <div><span className="font-medium text-gray-700">Vehicle ID:</span> {rec.vehicle_id}</div> */}
                </div>
              </Card>
            </div>
          ))
        )}
      </div>
      {/* Modal for quote confirmation */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="Generate Quote?"
        size="small"
      >
        <div className="space-y-4">
          <p>
            Do you want to generate a quote for <strong>{vehicle?.make} {vehicle?.model}</strong> with the <strong>{selectedRec?.cover_type}</strong> policy from <strong>{selectedRec?.insurer_name}</strong>?
          </p>
          <div className="flex justify-end gap-2">
            <button
              className="px-4 py-2 rounded bg-gray-200 text-gray-700 font-semibold"
              onClick={() => setShowModal(false)}
            >
              Cancel
            </button>
            <button
              className="px-4 py-2 rounded bg-blue-600 text-white font-semibold hover:bg-blue-700"
              onClick={() => { setShowModal(false); /* Future: trigger quote generation */ }}
            >
              Generate Quote
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
} 