import React, { createContext, useState } from 'react';

export const InsuranceContext = createContext();

export const InsuranceProvider = ({ children }) => {
  const [insuranceCompanies, setInsuranceCompanies] = useState([]);
  const [productCategories, setProductCategories] = useState([]);
  const [products, setProducts] = useState([]);
  const [policies, setPolicies] = useState([]);

  const addCompany = (company) => {
    setInsuranceCompanies(prev => [...prev, { ...company, id: `comp_${Date.now()}` }]);
  };

  const addCategory = (category) => {
    setProductCategories(prev => [...prev, { ...category, id: `cat_${Date.now()}` }]);
  };

  const addProduct = (product) => {
    setProducts(prev => [...prev, { ...product, id: `prod_${Date.now()}` }]);
  };

  const addPolicy = (policy) => {
    setPolicies(prev => [...prev, { ...policy, id: `pol_${Date.now()}` }]);
  };

  return (
    <InsuranceContext.Provider value={{
      insuranceCompanies,
      productCategories,
      products,
      policies,
      addCompany,
      addCategory,
      addProduct,
      addPolicy
    }}>
      {children}
    </InsuranceContext.Provider>
  );
};