import React from 'react';

const StatsOverview = ({ totalCompanies, totalProducts, totalPolicies, activeCompanies }) => {
    return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Companies Card */}
            <div className="bg-white p-6 rounded-lg shadow-md flex items-center space-x-4">
                <div className="flex-shrink-0 bg-blue-100 p-3 rounded-full">
                    <i className="fas fa-building text-blue-600 text-2xl"></i> {/* Icon */}
                </div>
                <div>
                    <div className="text-3xl font-bold text-gray-900">{totalCompanies}</div>
                    <div className="text-sm text-gray-500">Total Companies</div>
                </div>
            </div>

            {/* Total Products Card */}
            <div className="bg-white p-6 rounded-lg shadow-md flex items-center space-x-4">
                <div className="flex-shrink-0 bg-green-100 p-3 rounded-full">
                    <i className="fas fa-box text-green-600 text-2xl"></i> {/* Icon */}
                </div>
                <div>
                    <div className="text-3xl font-bold text-gray-900">{totalProducts}</div>
                    <div className="text-sm text-gray-500">Total Products</div>
                </div>
            </div>

            {/* Total Active Policies Card */}
            <div className="bg-white p-6 rounded-lg shadow-md flex items-center space-x-4">
                <div className="flex-shrink-0 bg-purple-100 p-3 rounded-full">
                    <i className="fas fa-file-alt text-purple-600 text-2xl"></i> {/* Icon */}
                </div>
                <div>
                    <div className="text-3xl font-bold text-gray-900">{totalPolicies}</div>
                    <div className="text-sm text-gray-500">Total Active Policies</div>
                </div>
            </div>

            {/* Active Insurers Card */}
            <div className="bg-white p-6 rounded-lg shadow-md flex items-center space-x-4">
                <div className="flex-shrink-0 bg-yellow-100 p-3 rounded-full">
                    <i className="fas fa-check-circle text-yellow-600 text-2xl"></i> {/* Icon */}
                </div>
                <div>
                    <div className="text-3xl font-bold text-gray-900">{activeCompanies}</div>
                    <div className="text-sm text-gray-500">Active Insurers</div>
                </div>
            </div>
        </div>
    );
};

export default StatsOverview;