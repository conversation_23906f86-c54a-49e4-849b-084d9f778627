<?php

namespace App\Models;

class UserService
{
    public function isAdmin(User $user): bool
    {
        return $user->role?->role_name === 'ADMIN';
    }

    public function isBroker(User $user): bool
    {
        return $user->role?->role_name === 'BROKER';
    }

    public function hasRequiredPermission(User $user, int $permission): bool
    {
        $rolePermService = new RolePermissionsService;

        return $rolePermService->hasPermission($user->role, $permission);
    }
}
