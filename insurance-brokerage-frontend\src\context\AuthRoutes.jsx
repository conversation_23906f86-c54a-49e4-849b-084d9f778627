import { Outlet, Navigate } from "react-router-dom";
import { useAuth } from "./AuthProvider";


const AuthRoutes = () => {
    const { user } = useAuth();

    return user ? <Outlet />
        :
        <Navigate to="/" />;
};



export const GuestRoutes = () => {
    const { user } = useAuth();
    let url = '/broker/dashboard';
    if(user?.role_id ===1) {
     url  = '/admin/dashboard';
    }
    return user ?  <Navigate to={url} /> : <Outlet />
      ;
};

export const AdminRoutes = () => {
    const { user } = useAuth();
    return user?.role_id === 1 ?  <Outlet /> : <Navigate to={`/broker/dashboard`} />
        ;
};

export const BrokerRoutes = () => {
    const { user } = useAuth();

    return user?.role_id === 2 ?  <Outlet /> : <Navigate to={`/admin/dashboard`} />
        ;
};


export default AuthRoutes;