import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  PageHeader,
  Card,
  Button,
  Alert,
  Modal,
  DataTable,
  DashboardStats,
  FormWizard,
  StatusBadge,
} from "../components";

const ComponentDemo = () => {
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);
  const [showAlert, setShowAlert] = useState(false);
  const [alertType, setAlertType] = useState("info");

  // Sample data for DataTable
  const sampleData = [
    {
      id: 1,
      name: "Britam Insurance",
      contact: "<PERSON>",
      email: "<EMAIL>",
      products: 15,
      policies: 1200,
      status: "Active",
      lastUpdated: "2024-01-15",
    },
    {
      id: 2,
      name: "AIG Kenya",
      contact: "<PERSON>",
      email: "<EMAIL>",
      products: 8,
      policies: 850,
      status: "Active",
      lastUpdated: "2024-01-14",
    },
    {
      id: 3,
      name: "CIC Insurance",
      contact: "<PERSON>",
      email: "<EMAIL>",
      products: 12,
      policies: 950,
      status: "Pending",
      lastUpdated: "2024-01-13",
    },
  ];

  const columns = [
    { key: "name", label: "Company Name", sortable: true },
    { key: "contact", label: "Contact Person", sortable: true },
    { key: "email", label: "Email", sortable: true },
    { key: "products", label: "Products", sortable: true },
    { key: "policies", label: "Policies", sortable: true },
    { key: "status", label: "Status", type: "status", sortable: true },
    { key: "lastUpdated", label: "Last Updated", type: "date", sortable: true },
  ];

  // Sample stats for DashboardStats
  const sampleStats = {
    totalCompanies: 25,
    totalProducts: 150,
    totalPolicies: 8500,
    activeCompanies: 22,
    totalRevenue: 1250000,
    pendingApprovals: 8,
    monthlyGrowth: 12.5,
    customerSatisfaction: 94,
  };

  // Sample steps for FormWizard
  const wizardSteps = [
    {
      title: "Company Information",
      description: "Basic company details",
      fields: [
        {
          name: "companyName",
          label: "Company Name",
          type: "text",
          placeholder: "Enter company name",
          required: true,
        },
        {
          name: "registrationNumber",
          label: "Registration Number",
          type: "text",
          placeholder: "Enter registration number",
          required: true,
        },
        {
          name: "regulatoryBody",
          label: "Regulatory Body",
          type: "select",
          options: [
            { value: "ira", label: "Insurance Regulatory Authority" },
            { value: "cma", label: "Capital Markets Authority" },
          ],
          required: true,
        },
      ],
      validation: (data, errors) => {
        if (!data.companyName) errors.companyName = "Company name is required";
        if (!data.registrationNumber)
          errors.registrationNumber = "Registration number is required";
        if (!data.regulatoryBody)
          errors.regulatoryBody = "Regulatory body is required";
      },
    },
    {
      title: "Contact Information",
      description: "Company contact details",
      fields: [
        {
          name: "contactEmail",
          label: "Contact Email",
          type: "email",
          placeholder: "<EMAIL>",
          required: true,
        },
        {
          name: "contactPhone",
          label: "Contact Phone",
          type: "tel",
          placeholder: "+254700000000",
          required: true,
        },
        {
          name: "address",
          label: "Address",
          type: "textarea",
          placeholder: "Enter company address",
          rows: 3,
        },
      ],
      validation: (data, errors) => {
        if (!data.contactEmail) errors.contactEmail = "Email is required";
        if (!data.contactPhone) errors.contactPhone = "Phone is required";
      },
    },
  ];

  const handleWizardComplete = (formData) => {
    console.log("Wizard completed:", formData);
    setShowAlert(true);
    setAlertType("success");
  };

  const handleTableAction = (action, item) => {
    console.log(`${action} action for:`, item);
    setShowAlert(true);
    setAlertType("info");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader
        title="Component Demo"
        subtitle="Showcase of all available components"
        breadcrumbs={[
          { label: "Dashboard", href: "/insurance" },
          { label: "Component Demo" },
        ]}
        actions={[
          {
            label: "Back to Dashboard",
            variant: "outline",
            onClick: () => navigate("/insurance"),
          },
        ]}
      />

      <div className="p-6 space-y-8">
        {/* Alert Examples */}
        <Card
          title="Alert Components"
          subtitle="Different types of alerts and notifications"
        >
          <div className="space-y-4">
            <Alert
              type="info"
              title="Information"
              message="This is an informational alert with some important details."
              onClose={() => {}}
            />
            <Alert
              type="success"
              title="Success"
              message="Operation completed successfully!"
              onClose={() => {}}
            />
            <Alert
              type="warning"
              title="Warning"
              message="Please review the information before proceeding."
              onClose={() => {}}
            />
            <Alert
              type="error"
              title="Error"
              message="Something went wrong. Please try again."
              onClose={() => {}}
            />
          </div>
        </Card>

        {/* Button Examples */}
        <Card
          title="Button Components"
          subtitle="Different button variants and sizes"
        >
          <div className="space-y-4">
            <div className="flex flex-wrap gap-3">
              <Button variant="primary">Primary Button</Button>
              <Button variant="secondary">Secondary Button</Button>
              <Button variant="success">Success Button</Button>
              <Button variant="danger">Danger Button</Button>
              <Button variant="warning">Warning Button</Button>
              <Button variant="outline">Outline Button</Button>
              <Button variant="ghost">Ghost Button</Button>
            </div>
            <div className="flex flex-wrap gap-3">
              <Button size="small">Small Button</Button>
              <Button size="default">Default Button</Button>
              <Button size="large">Large Button</Button>
            </div>
            <div className="flex flex-wrap gap-3">
              <Button icon="➕" loading>
                Loading Button
              </Button>
              <Button disabled>Disabled Button</Button>
            </div>
          </div>
        </Card>

        {/* Status Badge Examples */}
        <Card
          title="Status Badge Components"
          subtitle="Different status indicators"
        >
          <div className="flex flex-wrap gap-3">
            <StatusBadge status="Active" />
            <StatusBadge status="Inactive" />
            <StatusBadge status="Pending" />
            <StatusBadge status="Suspended" />
            <StatusBadge status="Approved" />
            <StatusBadge status="Active" size="large" />
            <StatusBadge status="Pending" size="small" />
          </div>
        </Card>

        {/* Modal Example */}
        <Card title="Modal Component" subtitle="Interactive modal dialog">
          <Button onClick={() => setShowModal(true)}>Open Modal</Button>

          <Modal
            isOpen={showModal}
            onClose={() => setShowModal(false)}
            title="Sample Modal"
            size="large"
          >
            <div className="space-y-4">
              <p>This is a sample modal with some content.</p>
              <p>
                You can put any content here including forms, tables, or other
                components.
              </p>
              <div className="flex justify-end space-x-3">
                <Button variant="outline" onClick={() => setShowModal(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setShowModal(false)}>Confirm</Button>
              </div>
            </div>
          </Modal>
        </Card>

        {/* DataTable Example */}
        <Card
          title="DataTable Component"
          subtitle="Advanced table with sorting, searching, and pagination"
        >
          <DataTable
            data={sampleData}
            columns={columns}
            onView={(item) => handleTableAction("view", item)}
            onEdit={(item) => handleTableAction("edit", item)}
            onDelete={(item) => handleTableAction("delete", item)}
            searchable={true}
            pagination={true}
            itemsPerPage={5}
          />
        </Card>

        {/* DashboardStats Example */}
        <Card
          title="DashboardStats Component"
          subtitle="Comprehensive dashboard overview"
        >
          <DashboardStats stats={sampleStats} />
        </Card>

        {/* FormWizard Example */}
        <Card title="FormWizard Component" subtitle="Step-by-step form wizard">
          <FormWizard
            steps={wizardSteps}
            onComplete={handleWizardComplete}
            onCancel={() => navigate("/insurance")}
          />
        </Card>
      </div>

      {/* Global Alert */}
      {showAlert && (
        <div className="fixed top-5 right-5 z-50">
          <Alert
            type={alertType}
            title={alertType === "success" ? "Success!" : "Info"}
            message={
              alertType === "success"
                ? "Form completed successfully!"
                : "Action performed successfully!"
            }
            onClose={() => setShowAlert(false)}
          />
        </div>
      )}
    </div>
  );
};

export default ComponentDemo;
