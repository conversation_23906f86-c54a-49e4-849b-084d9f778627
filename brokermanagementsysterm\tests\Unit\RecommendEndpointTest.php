<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\CategoryProduct;
use App\Models\Client;
use App\Models\ClientPreferences;
use App\Models\Insurance;
use App\Models\InsuranceCategoryProduct;
use App\Models\RoleSetupService;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RecommendEndpointTest extends TestCase
{
    use RefreshDatabase;

    public function test_recommendation_endpoint_returns_404_response_on_noprefs()
    {
        // Setup roles
        (new RoleSetupService)->createRoles();

        // Create broker and log in
        $broker = User::factory()->broker()->create();
        $this->actingAs($broker);

        // Create category and product
        $category = Category::factory()->create();
        $product = CategoryProduct::factory()->create([
            'category_id' => $category->category_id,
            'category_product_name' => 'comprehensive',
        ]);

        // Create client and preferences
        $client = Client::factory()->create(['broker_id' => $broker->user_id]);

        // ClientPreferences::factory()->create([
        //     'client_id' => $client->client_id,
        //     'preferred_cover_types' => json_encode(['comprehensive']), // ensure array string if needed
        //     'preferred_insurers' => json_encode([]),
        // ]);

        // Hit endpoint
        $response = $this->postJson('/api/v1/recommend', [
            'client_id' => $client->client_id,
        ]);

        $response->assertStatus(404);
        // $response->assertJsonStructure([
        //     '*' => [
        //         'product_id',
        //         'cover_type',
        //         'cover_description',
        //         'insurer_name',
        //         'price',
        //         'fitness_score',
        //     ],
        // ]);
    }

    public function test_recommendation_endpoint_returns_200_response_on_prefs()
    {
        // Setup roles
        (new RoleSetupService)->createRoles();

        // Create and log in broker
        $broker = User::factory()->broker()->create();
        $this->actingAs($broker);

        // Create multiple insurance companies
        // $insurers = Insurance::factory()->count(3)->create(['insurer_name' => random['MADISON', 'CIC', 'BRITAM']]);
        $names = ['MADISON', 'CIC', 'BRITAM'];

        $insurers = collect($names)->map(function ($name) {
            return Insurance::factory()->create(['name' => $name]);
        });

        // Create a category and multiple category products
        $category = Category::factory()->create(['category_name' => 'MOTOR_INSURANCE']);

        $coverTypes = ['COMPREHENSIVE', 'THIRD_PARTY', 'BASIC'];
        $categoryProducts = collect($coverTypes)->map(function ($type) use ($category) {
            return CategoryProduct::factory()->create([
                'category_id' => $category->category_id,
                'category_product_name' => $type,
            ]);
        });

        // Create InsuranceCategoryProducts with variety
        foreach ($categoryProducts as $product) {
            InsuranceCategoryProduct::factory()->create([
                'insurance_id' => $insurers->random()->insurance_id,
                'category_id' => $category->category_id,
                'category_product_id' => $product->category_product_id,
                'is_active' => true,
                'price' => rand(4500, 12000),
                'vehicle_usage' => 'Commercial',
            ]);
        }

        // Create a client
        $client = Client::factory()->create(['broker_id' => $broker->user_id]);

        // Create client preferences with realistic mix
        $preferredProduct = $categoryProducts->random();
        $preferredInsurer = $insurers->random();

        $vehicle = Vehicle::factory()->create(['client_id' => $client->client_id]);

        ClientPreferences::factory()->create([
            'vehicle_id' => $vehicle->vehicle_id,
            'preferred_cover_types' => json_encode([$preferredProduct->category_product_name]),
            'preferred_insurers' => json_encode([$preferredInsurer->insurance_id]),
            'budget_min' => 5000,
            'budget_max' => 11000,
        ]);

        $this->assertNotEmpty(ClientPreferences::all());

        // Hit the endpoint
        $response = $this->postJson('/api/v1/recommend', [
            'client_id' => $client->client_id,
        ]);

        // Assert basic success and structure
        $response->assertStatus(200);
        $response->assertJsonStructure([
            '*' => [
                'product_id',
                'cover_type',
                'cover_description',
                'insurer_name',
                'price',
                'fitness_score',
            ],
        ]);

        // Check contents are meaningful
        $data = $response->json();
        var_dump(ClientPreferences::first());
        var_dump($data);
        $this->assertNotEmpty($data, 'Recommendations should not be empty');

        foreach ($data as $rec) {
            $this->assertNotNull($rec['product_id']);
            $this->assertNotNull($rec['cover_type']);
            $this->assertNotNull($rec['insurer_name']);
            $this->assertNotNull($rec['price']);
            $this->assertIsNumeric($rec['fitness_score']);
        }
    }

    public function test_recommendation_endpoint_returns_not_found_for_invalid_client()
    {
        $this->actingAs(User::factory()->broker()->create());

        $response = $this->postJson('/api/v1/recommend', [
            'client_id' => 'non-existent-id',
        ]);

        $response->assertStatus(404);
        $response->assertExactJson([
            'message' => 'Client Not Found',
        ]);
    }
}
