<?php

namespace App\Http\Controllers\CategoryProduct;

use App\Http\Controllers\Controller;
// ✅ Fixed: Model should be singular, not ProductCoverTypes
use App\Models\CategoryProduct;
use Illuminate\Http\Request;

class CategoryProductController extends Controller
{
    /**
     * Display a listing of the cover types.
     */
    /* @OA\Get(
     *     path="/api/v1/cover-types",
     *     tags={"Cover Types"},
     *     summary="List all cover types",
     *     description="Returns a list of all product cover types",
     *     operationId="getCoverTypes",
     *     security={{"sanctum": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(type="array", @OA\Items(ref="#/components/schemas/CategoryProduct"))
     *     )
     * )
     */
    public function index(Request $request)
    {
        $current_user = $request->user(); // Optional for now

        $covers = CategoryProduct::all(); // ✅ Fixed missing semicolon and model name

        return response()->json($covers);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/cover-types",
     *     tags={"Cover Types"},
     *     summary="Create a new cover type",
     *     operationId="storeCoverType",
     *     security={{"sanctum": {}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(
     *             required={"category_product_name", "category_product_description", "cover_isactive", "cover_code", "category_id"},
     *
     *             @OA\Property(property="category_product_name", type="string", example="Third Party"),
     *             @OA\Property(property="category_product_description", type="string", example="Third party motor insurance"),
     *             @OA\Property(property="cover_isactive", type="boolean", example=true),
     *             @OA\Property(property="cover_code", type="integer", example=101),
     *             @OA\Property(property="category_id", type="integer", example=1)
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=201,
     *         description="Cover created successfully",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Cover created successfully"),
     *             @OA\Property(
     *                 property="cover",
     *                 ref="#/components/schemas/CategoryProduct"
     *             )
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=422,
     *         description="Validation failed"
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Error creating cover"
     *     )
     * )
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'category_product_name' => 'required|string|unique:IBMS_CATEGORY_PRODUCTS,category_product_name',
            'category_product_description' => 'required|string',
            'cover_isactive' => 'required|boolean',
            'category_id' => 'required|exists:IBMS_CATEGORIES,category_id',
        ]);

        // ✅ Fixed incorrect model (was Vehicle)
        $cover = CategoryProduct::create($validated);

        if ($cover) {
            return response()->json([
                'message' => 'Cover created successfully',
                'cover' => $cover,
            ], 201);
        }

        return response()->json([
            'message' => 'Error creating cover',
        ], 500);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/cover-types/{id}",
     *     tags={"Cover Types"},
     *     summary="Get a specific cover type by ID",
     *     operationId="getCoverTypeById",
     *     security={{"sanctum": {}}},
     *
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="ID of the cover type",
     *         required=true,
     *
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Cover type found",
     *
     *         @OA\JsonContent(ref="#/components/schemas/CategoryProduct")
     *     ),
     *
     *     @OA\Response(
     *         response=404,
     *         description="Cover type not found"
     *     )
     * )
     */
    public function show(string $id)
    {
        $cover = CategoryProduct::findOrFail($id); // ✅ Fixed: variable should be $id, not "id"

        return response()->json($cover);
    }

    /**
     * @OA\Put(
     *     path="/api/v1/cover-types/{id}",
     *     tags={"Cover Types"},
     *     summary="Update an existing cover type",
     *     operationId="updateCoverType",
     *     security={{"sanctum": {}}},
     *
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="ID of the cover type to update",
     *         required=true,
     *
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="category_product_name", type="string", example="Comprehensive"),
     *             @OA\Property(property="category_product_description", type="string", example="Full cover insurance"),
     *             @OA\Property(property="cover_isactive", type="boolean", example=true),
     *             @OA\Property(property="cover_code", type="integer", example=102),
     *             @OA\Property(property="category_id", type="integer", example=1)
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Cover updated successfully",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Cover updated successfully"),
     *             @OA\Property(property="cover", ref="#/components/schemas/CategoryProduct")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=404,
     *         description="Cover type not found"
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error"
     *     )
     * )
     */
    public function update(Request $request, string $id)
    {
        $cover = CategoryProduct::findOrFail($id);

        $validated = $request->validate([
            'category_product_name' => 'sometimes|required|unique:IBMS_CATEGORY_PRODUCTS,category_product_name|string',
            'category_product_description' => 'sometimes|required|string',
            'cover_isactive' => 'sometimes|required|boolean',
            'cover_code' => 'sometimes|required|integer',
            'category_id' => 'sometimes|required|exists:IBMS_CATEGORIES,category_id',
        ]);

        $cover->update($validated);

        return response()->json([
            'message' => 'Cover updated successfully',
            'cover' => $cover,
        ]);
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/cover-types/{id}",
     *     tags={"Cover Types"},
     *     summary="Delete a cover type",
     *     operationId="deleteCoverType",
     *     security={{"sanctum": {}}},
     *
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="ID of the cover type to delete",
     *         required=true,
     *
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Cover deleted successfully",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Cover deleted successfully")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=404,
     *         description="Cover type not found"
     *     )
     * )
     */
    public function destroy(string $id)
    {
        $cover = CategoryProduct::findOrFail($id);

        if ($cover->vehicles()->exists()) {
            return response()->json([
                'message' => 'Cannot delete cover type in use',
            ], 400);
        }

        $cover->delete();

        return response()->json([
            'message' => 'Cover deleted successfully',
        ]);
    }
}
