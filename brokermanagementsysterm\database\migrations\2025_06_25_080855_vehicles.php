<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('IBMS_VEHICLES', function (Blueprint $table) {
            $table->id('vehicle_id');
            $table->integer('mileage')->nullable(false);
            $table->string('make')->nullable(false);
            $table->string('model')->nullable(false);
            $table->string('registration_number')->nullable(false)->unique();
            $table->decimal('value', 15, 2)->nullable(false);
            $table->uuid('cover_type')->nullable(true);
            $table->uuid('client_id')->nullable(false);
            $table->string('vehicle_purpose');
            $table->timestamps();

            $table->foreign('client_id')
                ->references('client_id')
                ->on('IBMS_CLIENTS')
                ->onDelete('restrict');

            // TODO vehicles and covers is 1 to many
            $table->foreign('cover_type')
                ->references('category_product_id')
                ->on('IBMS_CATEGORY_PRODUCTS')
                ->onDelete('restrict');
        });

    }

    public function down(): void
    {
        Schema::dropIfExists('IBMS_VEHICLES');
    }
};
