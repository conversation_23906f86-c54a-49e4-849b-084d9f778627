<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\CategoryProduct;
use App\Models\Client;
use App\Models\RoleSetupService;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VehicleApiTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_vehicle()
    {

        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();
        // var_dump($testBroker);
        $testProduct = Category::factory()->create();
        $testCover = CategoryProduct::factory()->create(['category_id' => $testProduct]);

        $this->assertNotNull($testBroker->user_id);

        $this->actingAs($testBroker);

        $client = Client::factory()->create([
            'broker_id' => $testBroker->user_id,
        ]);

        $faker = \Illuminate\Support\Str::of(\Faker\Factory::create()->bothify('K?? ####'))->upper(); // e.g., KCB 1234
        $carRegNo = (string) $faker;
        $data = [
            'client_id' => $client->client_id,
            'make' => 'Toyota',
            'model' => 'Corolla',
            'registration_number' => $carRegNo,
            'mileage' => 45000,
            'value' => 750000.00,
            'cover_type' => $testCover->category_product_id,
            'vehicle_purpose' => 'Commercial',
        ];

        // var_dump($testBroker);

        $response = $this->postJson('/api/v1/vehicles', $data);

        $response->dump(); // Or use ->dumpSession() or ->dumpHeaders() if needed

        $response->assertStatus(201);

        $this->assertDatabaseHas('IBMS_VEHICLES', ['registration_number' => $carRegNo]);
    }

    public function test_can_list_vehicles(): void
    {
        $rolesetupService = new RoleSetupService;
        $rolesetupService::createRoles();
        $testProduct = Category::factory()->create();
        $testCover = CategoryProduct::factory()->create(['category_id' => $testProduct]);

        $broker = User::factory()->broker()->create();
        $this->actingAs($broker);

        $client = Client::factory()->create([
            'broker_id' => $broker->user_id,
        ]);

        for ($i = 0; $i < 3; $i++) {
            Vehicle::factory()->create([
                'client_id' => $client->client_id,
                'cover_type' => $testCover->category_product_id,
            ]);
        }

        $response = $this->getJson('/api/v1/vehicles');

        $response->assertStatus(200)
            ->assertJsonCount(3);

        $anotherbroker = User::factory()->broker()->create();
        $this->actingAs($anotherbroker);

        $response = $this->getJson('/api/v1/vehicles');

        $response->assertStatus(200)->assertJsonCount(0);

    }

    public function test_can_list_vehicles_by_client(): void
    {
        $rolesetupService = new RoleSetupService;
        $rolesetupService::createRoles();
        $testProduct = Category::factory()->create();
        $testCover = CategoryProduct::factory()->create(['category_id' => $testProduct]);

        $broker = User::factory()->broker()->create();
        $this->actingAs($broker);

        $client = Client::factory()->create([
            'broker_id' => $broker->user_id,
        ]);

        for ($i = 0; $i < 3; $i++) {
            Vehicle::factory()->create([
                'client_id' => $client->client_id,
                'cover_type' => $testCover->category_product_id,
            ]);
        }

        $response = $this->getJson("/api/v1/vehicles/client/{$client->client_id}");

        $response->assertStatus(200)
            ->assertJsonCount(3);

        $anotherbroker = User::factory()->broker()->create();

        $anotherclient = Client::factory()->create([
            'broker_id' => $anotherbroker->user_id,
        ]);

        for ($i = 0; $i < 4; $i++) {
            Vehicle::factory()->create([
                'client_id' => $anotherclient->client_id,
                'cover_type' => $testCover->category_product_id,
            ]);
        }

        $this->actingAs($anotherbroker);

        $response = $this->getJson("/api/v1/vehicles/client/{$anotherclient->client_id}");

        $response->assertStatus(200)->assertJsonCount(4);

        $response = $this->getJson('/api/v1/vehicles');

        $response->assertStatus(200)
            ->assertJsonCount(4);

        $this->actingAs($broker);

        $response = $this->getJson("/api/v1/vehicles/client/{$anotherclient->client_id}");
        $response->assertStatus(403);

    }

    public function test_can_show_vehicle()
    {

        // $this->test_can_create_vehicle();
        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();
        $testProduct = Category::factory()->create();
        $testCover = CategoryProduct::factory()->create(['category_id' => $testProduct]);

        $this->assertNotNull($testBroker->user_id);

        $client = Client::factory()->create([
            'broker_id' => $testBroker->user_id,
        ]);

        $faker = \Illuminate\Support\Str::of(\Faker\Factory::create()->bothify('K?? ####'))->upper(); // e.g., KCB 1234
        $carRegNo = (string) $faker;
        $data = [
            'client_id' => $client->client_id,
            'make' => 'Toyota',
            'model' => 'Corolla',
            'registration_number' => $carRegNo,
            'mileage' => 45000,
            'value' => 750000.00,
            'cover_type' => $testCover->category_product_id,
            'vehicle_purpose' => 'Commercial',
        ];

        $response = $this->getJson('/api/v1/vehicles/1');

        $response->assertStatus(401);
        // $response->assertJsonFragment([
        //     'vehicle_id' => 1, ]);

        $this->actingAs($testBroker);
        $response = $this->postJson('/api/v1/vehicles', $data);

        $response = $this->getJson('/api/v1/vehicles/1');

        $response->assertStatus(200);
        $response->assertJsonFragment([
            'vehicle_id' => 1, ]);
    }

    public function test_vehicle_not_found()
    {
        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();
        $testProduct = Category::factory()->create();
        $testCover = CategoryProduct::factory()->create(['category_id' => $testProduct]);

        $this->assertNotNull($testBroker->user_id);

        $client = Client::factory()->create([
            'broker_id' => $testBroker->user_id,
        ]);

        $faker = \Illuminate\Support\Str::of(\Faker\Factory::create()->bothify('K?? ####'))->upper(); // e.g., KCB 1234
        $carRegNo = (string) $faker;
        $data = [
            'client_id' => $client->client_id,
            'make' => 'Toyota',
            'model' => 'Corolla',
            'registration_number' => $carRegNo,
            'mileage' => 45000,
            'value' => 750000.00,
            'cover_type' => $testCover->id,
            'vehicle_purpose' => 'Commercial',
        ];
        $this->actingAs($testBroker);
        $response = $this->postJson('/api/v1/vehicles', $data);
        $response = $this->getJson('/api/v1/vehicles/999');

        $response->assertStatus(404)
            ->assertJson(['message' => 'Vehicle not found or unauthorized']);
    }

    public function test_can_delete_vehicle(): void
    {
        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();
        $testProduct = Category::factory()->create();
        $testCover = CategoryProduct::factory()->create(['category_id' => $testProduct->category_id]);

        $this->assertNotNull($testBroker->user_id);

        $client = Client::factory()->create([
            'broker_id' => $testBroker->user_id,
        ]);

        $faker = \Illuminate\Support\Str::of(\Faker\Factory::create()->bothify('K?? ####'))->upper(); // e.g., KCB 1234
        $carRegNo = (string) $faker;
        $data = [
            'client_id' => $client->client_id,
            'make' => 'Toyota',
            'model' => 'Corolla',
            'registration_number' => $carRegNo,
            'mileage' => 45000,
            'value' => 750000.00,
            'cover_type' => $testCover->category_product_id,
            'vehicle_purpose' => 'Commercial',
        ];
        $this->actingAs($testBroker);
        for ($i = 0; $i <= 5; $i++) {
            $response = $this->postJson('/api/v1/vehicles', $data);
        }

        $del_response = $this->deleteJson('/api/v1/vehicles/1');
        $del_response->assertStatus(200);

        $response = $this->getJson('/api/v1/vehicles/1');

        $response->assertStatus(404);
    }

    public function test_can_update_vehicle(): void
    {
        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();
        $testProduct = Category::factory()->create();
        $testCover = CategoryProduct::factory()->create(['category_id' => $testProduct]);

        $this->assertNotNull($testBroker->user_id);

        $client = Client::factory()->create([
            'broker_id' => $testBroker->user_id,
        ]);

        $faker = \Illuminate\Support\Str::of(\Faker\Factory::create()->bothify('K?? ####'))->upper(); // e.g., KCB 1234
        $carRegNo = (string) $faker;
        $data = [
            'client_id' => $client->client_id,
            'make' => 'Toyota',
            'model' => 'Corolla',
            'registration_number' => $carRegNo,
            'mileage' => 45000,
            'value' => 750000.00,
            'cover_type' => $testCover->category_product_id,
            'vehicle_purpose' => 'Commercial',
        ];

        $this->actingAs($testBroker);
        for ($i = 0; $i <= 5; $i++) {
            $response = $this->postJson('/api/v1/vehicles', $data);
        }

        $updateData = [
            'model' => 'Hilux',
            'mileage' => 60000,
        ];

        $response = $this->getJson('/api/v1/vehicles/1');

        $response->assertStatus(200)
            ->assertJsonFragment([
                'model' => 'Corolla',
                'mileage' => 45000,
            ]);

        $response = $this->putJson('/api/v1/vehicles/1', $updateData);

        // Assert
        $response->assertStatus(200)
            ->assertJsonFragment([
                'model' => 'Hilux',
                'mileage' => 60000,
            ]);

        $this->assertDatabaseHas('IBMS_VEHICLES', [
            'vehicle_id' => 1,
            'model' => 'Hilux',
            'mileage' => 60000,
        ]);
    }
}
