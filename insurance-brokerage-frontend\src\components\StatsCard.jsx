import React from 'react';

const StatsCard = ({ title, value, icon, trend, trendValue }) => {
  const trendColor = trend === 'up' ? 'text-green-600' : 'text-red-600';
  const trendIcon = trend === 'up' ? '↑' : '↓';

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border-l-4 border-blue-500">
      <div className="flex justify-between items-start">
        <div>
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className="text-3xl font-bold mt-1">{value}</p>
        </div>
        <span className="text-2xl">{icon}</span>
      </div>
      <div className={`mt-2 text-sm ${trendColor}`}>
        <span>{trendIcon} {trendValue}</span>
        <span className="ml-1">from last month</span>
      </div>
    </div>
  );
};

export default StatsCard;