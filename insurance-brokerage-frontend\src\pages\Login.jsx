import React, { useState } from "react";
import axios from "../utils/axios";
import {useAuth} from "../context/AuthProvider.jsx";

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState("");
  const { login } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();
    // Placeholder: Add authentication logic here
    if (!email || !password) {
      setError("Please enter both email and password.");
      return;
    }
    setError("");

    await login({email, password});
  /*  try {
      // Step 1: Get CSRF cookie
      await axios.get('/sanctum/csrf-cookie');

      // Step 2: Login
      const loginRes = await axios.post('/login', {
        email,
        password,
      });

      // Step 3: Fetch user
      const userRes = await axios.get('/api/v1/user'); // or /api/user depending on your route
      //  const userData = userRes.data;
      console.log(userRes?.data?.role_id);
      if(userRes?.data?.role_id ===1) {
        window.location.href = '/admin/dashboard';
      }else {
        window.location.href = '/broker/dashboard';
      }
      // onLogin(userData);
    }
    catch (err) {
      console.error(err);
      const message = err.response?.data?.message || "Login failed.";
      setError(message);
    }*/
    // Redirect or authenticate
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-blue-50 via-white to-blue-100">
      <div className="bg-white p-8 rounded-2xl shadow-xl w-full max-w-md border border-gray-100 flex flex-col justify-center">
        <div className="flex flex-col items-center mb-6">
          <div className="bg-blue-100 rounded-full p-3 mb-2">
            <svg className="h-8 w-8 text-blue-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 11c1.657 0 3-1.343 3-3S13.657 5 12 5s-3 1.343-3 3 1.343 3 3 3zm0 2c-2.21 0-4 1.79-4 4v1a1 1 0 001 1h6a1 1 0 001-1v-1c0-2.21-1.79-4-4-4z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-800">Sign in to your account</h2>
          <p className="text-gray-500 mt-1 text-sm">Welcome back! Please enter your details.</p>
        </div>
        <form onSubmit={handleSubmit} className="space-y-5">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
             // required
              autoComplete="email"
            />
          </div>
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <input
              id="password"
              type={showPassword ? "text" : "password"}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
              //required
              autoComplete="current-password"
            />
            <div className="flex items-center mt-2">
              <input
                id="show-password"
                type="checkbox"
                checked={showPassword}
                onChange={() => setShowPassword((v) => !v)}
                className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="show-password" className="ml-2 block text-sm text-gray-700 cursor-pointer">
                Show password
              </label>
            </div>
            <div className="flex justify-end mt-1">
              <a href="#" className="text-xs text-blue-600 hover:underline">Forgot password?</a>
            </div>
          </div>
          {error && <div className="text-red-600 text-sm text-center">{error}</div>}
          <button
            type="submit"
            className="w-full bg-blue-700 hover:bg-blue-800 text-white font-semibold py-2.5 px-4 rounded-lg transition-colors shadow-sm mt-2"
          >
            Sign In
          </button>
        </form>
      </div>
    </div>
  );
};

export default Login;