<?php

namespace App\Http\Controllers\ClientPreferences;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\ClientPreferences;
use App\Models\Vehicle;
use Illuminate\Http\Request;

/**
 * @OA\Tag(
 *     name="Client Preferences",
 *     description="Manage insurance preferences for a client"
 * )
 */
class ClientPreferencesController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/v1/preferences",
     *     summary="List all client preferences",
     *     tags={"Client Preferences"},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation"
     *     )
     * )
     */
    public function index(Request $request)
    {
        $current_user = $request->user();

        if ($current_user->isAdmin()) {
            $preferences = ClientPreferences::all();

            return response->json($preferences);
        } elseif ($current_user->isBroker()) {
            // Get client IDs under this broker
            $clientIds = \App\Models\Client::where('broker_id', $broker->id)->pluck('client_id');

            // Return only preferences for those clients
            $prefs = ClientPreferences::whereIn('client_id', $clientIds)->get();

            return response()->json($prefs);
        } else {
            return response()->json([]);
        }

    }

    /**
     * @OA\Post(
     *     path="/api/v1/clients/{id}/preferences",
     *     summary="Store client preferences",
     *     tags={"Client Preferences"},
     *
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Client UUID",
     *
     *         @OA\Schema(type="string")
     *     ),
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(
     *             required={"client_id"},
     *
     *             @OA\Property(property="client_id", type="string", example="123e4567-e89b-12d3-a456-************"),
     *             @OA\Property(property="preferred_insurers", type="array", @OA\Items(type="string")),
     *             @OA\Property(property="preferred_cover_types", type="array", @OA\Items(type="string", enum={"comprehensive", "third_party"})),
     *             @OA\Property(property="preferred_channel", type="string", enum={"email", "phone", "whatsapp"}),
     *             @OA\Property(property="renewal_reminder_enabled", type="boolean"),
     *             @OA\Property(property="budget_min", type="number", format="float"),
     *             @OA\Property(property="budget_max", type="number", format="float")
     *         )
     *     ),
     *
     *     @OA\Response(response=201, description="Preferences saved successfully"),
     *     @OA\Response(response=422, description="Validation failed")
     * )
     */
    public function store(Request $request, $client_id, $vehicle_id)
    {

        $current_user = $request->user();

        if ($current_user->isBroker()) {
            $request->validate([
                'preferred_insurers' => 'nullable|array',
                'preferred_insurers.*' => 'string',
                'preferred_cover_types' => 'nullable|array',
                'preferred_cover_types.*' => 'string|in:comprehensive,third_party',
                'preferred_channel' => 'nullable|string|in:email,phone,whatsapp',
                'renewal_reminder_enabled' => 'boolean',
                'budget_min' => 'nullable|numeric|min:0',
                'budget_max' => 'nullable|numeric|gte:budget_min',
            ]);

            $brokerId = $current_user->user_id;

            // Confirm client belongs to broker
            $client = \App\Models\Client::where('client_id', $client_id)
                ->where('broker_id', $brokerId)
                ->first();

            if (! $client) {
                return response()->json([
                    'message' => 'Unauthorized: Client does not belong to broker.',
                ], 403);
            }
            $vehicle = Vehicle::where('vehicle_id', $vehicle_id)
                ->where('client_id', $client->client_id)
                ->first();

            if (! $vehicle) {
                return response()->json([
                    'message' => 'Vehicle Not Found',
                ], 404);
            }

            $existing = ClientPreferences::where('vehicle_id', $vehicle_id)->first();
            if ($existing) {
                return response()->json([
                    'message' => 'Vehicle already has preferences saved.',
                ], 409);
            }

            $prefs = ClientPreferences::create(array_merge(
                $request->only([
                    'preferred_insurers',
                    'preferred_cover_types',
                    'preferred_channel',
                    'renewal_reminder_enabled',
                    'budget_min',
                    'budget_max',
                ]),
                ['vehicle_id' => $vehicle->vehicle_id]
            ));

            return response()->json([
                'message' => 'Preferences saved successfully',
                'preferences' => $prefs,
            ], 201);

        } elseif ($current_user->isAdmin()) {
            $request->validate([
                'preferred_insurers' => 'nullable|array',
                'preferred_insurers.*' => 'string',
                'preferred_cover_types' => 'nullable|array',
                'preferred_cover_types.*' => 'string|in:comprehensive,third_party',
                'preferred_channel' => 'nullable|string|in:email,phone,whatsapp',
                'renewal_reminder_enabled' => 'boolean',
                'budget_min' => 'nullable|numeric|min:0',
                'budget_max' => 'nullable|numeric|gte:budget_min',
            ]);

            // Confirm client belongs to broker
            $client = Client::where('client_id', $client_id)->first();

            if (! $client) {
                return response()->json([
                    'message' => 'Client Not Found',
                ], 404);
            }
            $vehicle = Vehicle::where('vehicle_id', $vehicle_id)
                ->where('client_id', $client->client_id)
                ->first();

            if (! $vehicle) {
                return response()->json([
                    'message' => 'Vehicle Not Found',
                ], 404);
            }

            $existing = ClientPreferences::where('vehicle_id', $vehicle_id)->first();
            if ($existing) {
                return response()->json([
                    'message' => 'Vehicle already has preferences saved.',
                ], 409);
            }

            $prefs = ClientPreferences::create(array_merge(
                $request->only([
                    'preferred_insurers',
                    'preferred_cover_types',
                    'preferred_channel',
                    'renewal_reminder_enabled',
                    'budget_min',
                    'budget_max',
                ]),
                ['vehicle_id' => $vehicle->vehicle_id]
            ));

            return response()->json([
                'message' => 'Preferences saved successfully',
                'preferences' => $prefs,
            ], 201);
        } else {
            return response()->json(['message' => 'Unauthorized'], 403);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/clients/{id}/preferences",
     *     summary="Get a client's preferences",
     *     tags={"Client Preferences"},
     *
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Client Preferences ID",
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\Response(response=200, description="Client preferences returned")
     * )
     */
    public function show_pref_by_client($client_id, Request $request): mixed
    {

        $current_user = $request->user();

        if ($current_user->isBroker()) {
            $brokerId = $current_user->user_id;
            // Ensure broker is allowed to access this client
            $client = Client::where('client_id', $client_id)
                ->where('broker_id', $brokerId)
                ->first();

            $preferences = ClientPreferences::whereHas('vehicle', function ($query) use ($client) {
                $query->where('client_id', $client->client_id);
            })->get();

            if (! $preferences) {
                return response()->json(['message' => 'Preferences not found'], 404);
            }

            return response()->json($preferences);
        } elseif ($current_user->isAdmin()) {

            $client = Client::where('client_id', $client_id)
                ->first();

            if ($client == null) {
                return response()->json(['message' => 'Preferences not found'], 404);
            }

            $preferences = ClientPreferences::whereHas('vehicle', function ($query) use ($client) {
                $query->where('client_id', $client->client_id);
            })->get();

            return response()->json($preferences);
        } else {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

    }

    public function show_pref_by_vehicle($vehicle_id, Request $request): mixed
    {
        $current_user = $request->user();

        // Find the vehicle
        $vehicle = Vehicle::where('vehicle_id', $vehicle_id)->first();

        if (! $vehicle) {
            return response()->json(['message' => 'Vehicle not found'], 404);
        }

        // Get the client who owns the vehicle
        $client = $vehicle->client;

        if (! $client) {
            return response()->json(['message' => 'Client not found for this vehicle'], 404);
        }

        // Auth checks
        if ($current_user->isBroker()) {
            if ($client->broker_id !== $current_user->user_id) {
                return response()->json(['message' => 'Unauthorized: Not your client'], 403);
            }
        } elseif (! $current_user->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Get the preferences for the vehicle
        $preference = $vehicle->preferences;

        if (! $preference) {
            return response()->json(['message' => 'Preferences not found for this vehicle'], 404);
        }

        return response()->json($preference);
    }

    /**
     * @OA\Put(
     *     path="/api/v1/clients/{client_id}/preferences/{pref_id}",
     *     summary="Update client preferences",
     *     tags={"Client Preferences"},
     *
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Client Preferences ID",
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\RequestBody(
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="preferred_insurers", type="array", @OA\Items(type="string")),
     *             @OA\Property(property="preferred_cover_types", type="array", @OA\Items(type="string", enum={"comprehensive", "third_party"})),
     *             @OA\Property(property="preferred_channel", type="string", enum={"email", "phone", "whatsapp"}),
     *             @OA\Property(property="renewal_reminder_enabled", type="boolean"),
     *             @OA\Property(property="budget_min", type="number", format="float"),
     *             @OA\Property(property="budget_max", type="number", format="float")
     *         )
     *     ),
     *
     *     @OA\Response(response=200, description="Preferences updated successfully"),
     *     @OA\Response(response=422, description="Validation failed")
     * )
     */
    public function update(Request $request, int $preference_id)
    {

        $current_user = $request->user();

        if ($current_user->isBroker()) {
            $request->validate([
                'preferred_insurers' => 'nullable|array',
                'preferred_insurers.*' => 'string',
                'preferred_cover_types' => 'nullable|array',
                'preferred_cover_types.*' => 'string|in:comprehensive,third_party',
                'preferred_channel' => 'nullable|string|in:email,phone,whatsapp',
                'renewal_reminder_enabled' => 'boolean',
                'budget_min' => 'nullable|numeric|min:0',
                'budget_max' => 'nullable|numeric|gte:budget_min',
            ]);

            $preference = ClientPreferences::where('id', $preference_id)
                ->whereHas('vehicle', function ($query) use ($current_user) {
                    $query->whereHas('client', function ($clientQuery) use ($current_user) {
                        $clientQuery->where('broker_id', $current_user->user_id);
                    });
                })
                ->first();

            if ($preference == null) {
                return response()->json(['message' => 'Preference not found'], 404);
            }

            $preference->update($request->only([
                'preferred_insurers',
                'preferred_cover_types',
                'preferred_channel',
                'renewal_reminder_enabled',
                'budget_min',
                'budget_max',
            ]));

            return response()->json([
                'message' => 'Preferences updated successfully',
                'preferences' => $preference,
            ]);
        } elseif ($current_user->isAdmin()) {
            $request->validate([
                'preferred_insurers' => 'nullable|array',
                'preferred_insurers.*' => 'string',
                'preferred_cover_types' => 'nullable|array',
                'preferred_cover_types.*' => 'string|in:comprehensive,third_party',
                'preferred_channel' => 'nullable|string|in:email,phone,whatsapp',
                'renewal_reminder_enabled' => 'boolean',
                'budget_min' => 'nullable|numeric|min:0',
                'budget_max' => 'nullable|numeric|gte:budget_min',
            ]);

            $preference = \App\Models\ClientPreferences::where('id', $preference_id)
                ->first();

            if ($preference == null) {
                return response()->json(['message' => 'Preference not found'], 404);
            }

            $preference->update($request->only([
                'preferred_insurers',
                'preferred_cover_types',
                'preferred_channel',
                'renewal_reminder_enabled',
                'budget_min',
                'budget_max',
            ]));

            return response()->json([
                'message' => 'Preferences updated successfully',
                'preferences' => $preference,
            ]);
        } else {
            return response()->json(['message' => 'Unauthorized'], 403);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/clients/{client_id}/preferences/{pref_id}",
     *     summary="Delete client preferences",
     *     tags={"Client Preferences"},
     *
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Client Preferences ID",
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\Response(response=200, description="Preferences deleted")
     * )
     */
    public function destroy($preference_id, Request $request)
    {

        $current_user = $request->user();

        if ($current_user->isBroker()) {
            $brokerId = $current_user->user_id;

            $preference = ClientPreferences::where('id', $preference_id)
                ->whereHas('vehicle', function ($query) use ($current_user) {
                    $query->whereHas('client', function ($clientQuery) use ($current_user) {
                        $clientQuery->where('broker_id', $current_user->user_id);
                    });
                })
                ->first();

            if ($preference == null) {
                return response()->json(['message' => 'Preference not found'], 404);
            }

            $preference->delete();

            return response()->json(['message' => 'Preference deleted successfully']);
        } elseif ($current_user->isAdmin()) {

            $preference = ClientPreferences::where('id', $preference_id)
                ->first();

            if ($preference == null) {
                return response()->json(['message' => 'Preference not found'], 404);
            }

            $preference->delete();

            return response()->json(['message' => 'Preference deleted successfully']);

        } else {
            return response()->json(['message' => 'Unauthorized'], 403);

        }
    }
}
