import React, { useState, useEffect } from "react";
import { Card, PageHeader, Button, Alert, DataTable } from "../../components";
import SearchBar from "../../components/searchbar";
import { policyService, coverTypeService, insuranceService, benefitService } from "../../services";
import { useNavigate } from "react-router-dom";

export default function PolicyCatalog() {
  const [policies, setPolicies] = useState([]);
  const [filteredPolicies, setFilteredPolicies] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedInsurer, setSelectedInsurer] = useState("all");
  const [selectedCoverType, setSelectedCoverType] = useState("all");
  const [loading, setLoading] = useState(true);
  const [coverTypes, setCoverTypes] = useState([]);
  const [insurers, setInsurers] = useState([]);
  const [benefits, setBenefits] = useState([]);
  const navigate = useNavigate();

  // Fetch all data using service layer
  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        const [fetchedPolicies, fetchedCoverTypes, fetchedInsurers, fetchedBenefits] = await Promise.all([
          policyService.getPolicies(),
          coverTypeService.getCoverTypes(),
          insuranceService.getCompanies(),
          benefitService.getBenefits(),
        ]);
        setCoverTypes(fetchedCoverTypes);
        setInsurers(fetchedInsurers);
        setBenefits(fetchedBenefits);
        // Join for display
        const joined = fetchedPolicies.map((policy) => {
          const coverType = fetchedCoverTypes.find((ct) => String(ct.id) === String(policy.coverTypeId));
          const insurer = fetchedInsurers.find((ins) => String(ins.id) === String(policy.insurerId));
          return {
            ...policy,
            coverTypeName: coverType ? coverType.name : "Unknown",
            insurerName: insurer ? insurer.name : "Unknown",
            coverTypeDescription: coverType ? coverType.description : "",
            insurerEmail: insurer ? insurer.email : "",
            insurerPhone: insurer ? insurer.phone : "",
          };
        });
        setPolicies(joined);
        setFilteredPolicies(joined);
      } catch (error) {
        // Optionally handle error
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, []);

  // Filter and search
  useEffect(() => {
    let filtered = policies;
    if (selectedInsurer !== "all") {
      filtered = filtered.filter((p) => String(p.insurerId) === String(selectedInsurer));
    }
    if (selectedCoverType !== "all") {
      filtered = filtered.filter((p) => String(p.coverTypeId) === String(selectedCoverType));
    }
    if (searchTerm) {
      filtered = filtered.filter(
        (p) =>
          p.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          p.coverTypeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          p.insurerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          p.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    setFilteredPolicies(filtered);
  }, [searchTerm, policies, selectedInsurer, selectedCoverType]);

  const handleViewPolicy = (policy) => {
    navigate(`/broker/policies/${policy.id}`);
  };

  const getPolicyBenefits = (policyId) => {
    return benefits.filter(benefit => benefit.policies.includes(policyId));
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "KES",
    }).format(amount);
  };

  const columns = [
    {
      key: "name",
      label: "Policy Name",
      sortable: true,
      render: (value, item) => (
        <div className="flex items-center">
          <div className="flex-shrink-0 h-10 w-10">
            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-blue-600 font-semibold text-sm">
                {value.charAt(0).toUpperCase()}
              </span>
            </div>
          </div>
          <div className="ml-4">
            <div className="text-sm font-medium text-gray-900">{value}</div>
            <div className="text-sm text-gray-500">{item.coverTypeName}</div>
          </div>
        </div>
      ),
    },
    {
      key: "insurerName",
      label: "Insurance Company",
      sortable: true,
      render: (value) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {value}
        </span>
      ),
    },
    {
      key: "premiumRate",
      label: "Premium Rate",
      sortable: true,
      render: (value) => (
        <span className="text-green-700 font-semibold">{value}%</span>
      ),
    },
    {
      key: "description",
      label: "Description",
      sortable: false,
      render: (value) => (
        <span className="text-gray-600 text-sm line-clamp-2">{value}</span>
      ),
    },
    {
      key: "status",
      label: "Status",
      sortable: true,
      render: (value) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          value === "Active" ? "bg-green-100 text-green-800" : "bg-gray-200 text-gray-600"
        }`}>
          {value}
        </span>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      render: (_, item) => (
        <div className="flex gap-2">
          <Button 
            size="small" 
            variant="outline" 
            onClick={() => handleViewPolicy(item)}
          >
            View Details
          </Button>
          <Button 
            size="small" 
            variant="primary"
            onClick={() => window.open(`/broker/quotes/create?policy=${item.id}`, '_blank')}
          >
            Get Quote
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full mx-auto p-6 space-y-6">
        <PageHeader
          title="Policy Catalog"
          subtitle="Browse and compare insurance policies from different providers"
          breadcrumbs={[
            { label: "Dashboard", href: "/broker/dashboard" },
            { label: "Policy Catalog" },
          ]}
        />
        <Card title="Available Policies" subtitle={`${filteredPolicies.length} policies found`}>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Insurer</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-700"
                  value={selectedInsurer}
                  onChange={(e) => setSelectedInsurer(e.target.value)}
                >
                  <option value="all">All Insurers</option>
                  {insurers.map((insurer) => (
                    <option key={insurer.id} value={insurer.id}>{insurer.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Cover Type</label>
                <select
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-700"
                  value={selectedCoverType}
                  onChange={(e) => setSelectedCoverType(e.target.value)}
                >
                  <option value="all">All Cover Types</option>
                  {coverTypes.filter(ct => ct.category === "Motor").map((ct) => (
                    <option key={ct.id} value={ct.id}>{ct.name}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="flex-1">
              <SearchBar 
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
              />
            </div>
          </div>
          {loading ? (
            <div className="text-center py-10 text-gray-500">Loading policies...</div>
          ) : (
            <DataTable
              data={filteredPolicies}
              columns={columns}
              searchable={false}
              pagination={true}
              itemsPerPage={10}
            />
          )}
        </Card>
      </div>
    </div>
  );
}
