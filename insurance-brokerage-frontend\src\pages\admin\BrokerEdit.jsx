import React, {useEffect, useState} from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import PageHeader from "../../components/PageHeader";
import Card from "../../components/Card";
import FormWizard from "../../components/FormWizard";
import {userService} from "../../services/index.js";

// Use the same mockBrokers as in BrokerList for now
/*const mockBrokers = [
  {
    id: 1,
    firstName: "<PERSON>",
    first_name: "<PERSON><PERSON>",
    id_number: "123456",
    phone_number: "0712345678",
    email: "<EMAIL>",
    role: "broker",
  },
  {
    id: 2,
    firstName: "<PERSON>",
    first_name: "<PERSON>",
    id_number: "654321",
    phone_number: "0723456789",
    email: "<EMAIL>",
    role: "broker",
  },
  {
    id: 3,
    firstName: "<PERSON>",
    first_name: "<PERSON>",
    id_number: "789012",
    phone_number: "0734567890",
    email: "<EMAIL>",
    role: "broker",
  },
];*/

const wizardSteps = [
  {
    title: "Personal Information",
    description: "Edit broker's personal details",
    fields: [
      {
        name: "first_name",
        label: "First Name",
        value: "first_name",
        type: "text",
        placeholder: "Enter first name",
        required: true,
      },
      {
        name: "last_name",
        label: "Last Name",
        type: "text",
        placeholder: "Enter last name",
        required: true,
      },
      {
        name: "id_number",
        label: "ID Number",
        type: "text",
        placeholder: "Enter ID number",
        required: true,
      },
    ],
    validation: (data, errors) => {
      if (!data.first_name) errors.first_name = "First name is required";
      if (!data.last_name) errors.last_name = "Last name is required";
      if (!data.id_number) errors.id_number = "ID number is required";
    },
  },
  {
    title: "Contact Information",
    description: "Edit broker's contact details",
    fields: [
      {
        name: "phone_number",
        label: "Phone Number",
        type: "text",
        placeholder: "Enter phone_number number",
        required: true,
      },
      {
        name: "email",
        label: "Email",
        type: "email",
        placeholder: "Enter email",
        required: true,
      },
      {
        name: "role_id",
        label: "Role",
        type: "select",
        options: [
          { value: 2, label: "Broker" },
          { value: 1, label: "Admin" },
        ],
        required: true,
      },
    ],
    validation: (data, errors) => {
      if (!data.phone_number) errors.phone_number = "Phone number is required";
      if (!data.email) errors.email = "Email is required";
      if (!data.role_id) errors.role_id = "Role is required";
    },
  },
];

const BrokerEdit = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [broker, setBrokerData ] = useState({});
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const user = await userService.getUser(id); // 'me'
        setBrokerData(user);
        console.log('user data',JSON.stringify(user)); // Logs 'me'
      } catch (error) {
        console.error('Failed to fetch user', error);
      }
    };

    fetchUser();
  }, []);

  useEffect(() => {
    // if (userRes) {
    if (Object.keys(broker).length === 0 && broker.constructor === Object) {
      setLoading(true);
    }
    else {
      setLoading(false);

    }
  }, [broker]);
  if (loading) {
    return <div>Loading...</div>;
  }

  if (!broker) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-gray-500 text-lg">Broker not found.</div>
      </div>
    );
  }

  const handleWizardComplete = async (values) => {

    const user = await userService.updateUser(values);

    navigate("/admin/brokers", {
      state: {
        alert: {
          show: true,
          type: "success",
          title: "Broker Updated",
          message: `Broker ${broker.first_name} ${broker.last_name} updated successfully!`,
        },
      },
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center py-8 px-2 sm:py-12 sm:px-4">
      <div className="w-full max-w-4xl mx-auto">
        <PageHeader
          title="Edit Broker"
          subtitle={`Editing ${broker.first_name} ${broker.last_name}`}
          breadcrumbs={[
            { label: "User Registration", href: "/admin/user-registration" },
            { label: "Broker List", href: "/admin/brokers" },
            { label: `Edit Broker: ${broker.first_name} ${broker.last_name}` },
          ]}
          actions={[
            {
              label: "Back to List",
              variant: "primary",
              onClick: () => navigate("/admin/brokers"),
            },
          ]}
        />
        <Card title="FormWizard Component" subtitle="Step-by-step form wizard" className="mt-8 relative z-0">
          {/* Ensure Card is not blocked by overlays */}
          {broker && Object.keys(broker).length > 0 && (
              <>

                <FormWizard
                    steps={wizardSteps}
                    initialValues={broker}
                    onComplete={handleWizardComplete}
                    onCancel={() => navigate("/admin/brokers")}
                />
              </>
          )}

        </Card>
      </div>
    </div>
  );
};

export default BrokerEdit;
