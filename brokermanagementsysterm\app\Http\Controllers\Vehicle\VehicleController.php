<?php

namespace App\Http\Controllers\Vehicle;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\Vehicle;
use Illuminate\Http\Request;

class VehicleController extends Controller
{
    /**
     * Display a listing of the resource.
     */

    /**
     * @OA\Get(
     *     path="/api/v1/vehicles",
     *     summary="List all vehicles",
     *     tags={"Vehicles"},
     *     security={{"sanctum":{}}},
     *
     *     @OA\Response(
     *         response=200,
     *         description="Successful response"
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthenticated"
     *     )
     * )
     */
    public function index(Request $request)
    {
        $current_user = $request->user();

        if ($current_user->isAdmin()) {
            $query = Vehicle::all();

            return $response()->json($query);
        } elseif ($current_user->isBroker()) {
            $brokerId = $current_user->user_id;
            $query = Vehicle::whereHas('client', function ($query) use ($brokerId) {
                $query->where('broker_id', $brokerId);
            });
            $vehicles = $query->get();

            return response()->json($vehicles);
        } else {
            return response()->json(['message' => 'Unauthorized'], 401);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/vehicles/client/{clientid}",
     *     summary="Get vehicles by client",
     *     description="Returns a list of vehicles for a specific client, with optional filters for registration number, make, and model.",
     *     operationId="getVehiclesByClient",
     *     tags={"Vehicles"},
     *     security={{"bearerAuth":{}}},
     *
     *     @OA\Parameter(
     *         name="clientId",
     *         in="path",
     *         description="ID of the client",
     *         required=true,
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\Parameter(
     *         name="registration_number",
     *         in="query",
     *         description="Filter by registration number",
     *         required=false,
     *
     *         @OA\Schema(type="string")
     *     ),
     *
     *     @OA\Parameter(
     *         name="make",
     *         in="query",
     *         description="Filter by vehicle make",
     *         required=false,
     *
     *         @OA\Schema(type="string")
     *     ),
     *
     *     @OA\Parameter(
     *         name="model",
     *         in="query",
     *         description="Filter by vehicle model",
     *         required=false,
     *
     *         @OA\Schema(type="string")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="List of vehicles",
     *
     *         @OA\JsonContent(
     *             type="array",
     *
     *             @OA\Items(ref="#/components/schemas/Vehicle")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=403,
     *         description="Unauthorized or Client not found",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Unauthorized or Client not found")
     *         )
     *     )
     * )
     */
    // public function index_by_client(Request $request, string $clientId)
    // {
    //     $current_user = $request->user();

    //     if($current_user->isAdmin())
    //     {
    //         $client = Client::where('client_id', $clientId)->first();

    //         if (client == NULL)
    //         {
    //             return response()->json(['message'=>'Client not Found']);
    //         }

    //         $query = $client->vehicles();

    //         $filters = [
    //             'registration_number' => fn($value) => $query->where('registration_number', $value),
    //             'make' => fn($value) => $query->where('make', 'like', "%$value%"),
    //             'model' => fn($value) => $query->where('model', 'like', "%$value%"),
    //         ];

    //         foreach ($filters as $field => $callback)
    //         {
    //             if ($request->filled($field))
    //             {
    //                 $callback($request->input($field));
    //             }
    //         }

    //         $vehicles = $query->get();

    //         return response()->json($vehicles);
    //     }
    //     elseif($current_user->isBroker())
    //     {
    //         $brokerId = $current_user->user_id;
    //         $client = Client::where('broker_id', $brokerId)
    //             ->where('client_id', $clientId)->first();

    //         if($client == NULL)
    //         {
    //             return response()->json(['message'=>'Client Not Found']);
    //         }

    //         $query = $client->vehicles();

    //         $filters = [
    //             'registration_number' => fn($value) => $query->where('registration_number', $value),
    //             'make' => fn($value) => $query->where('make', 'like', "%$value%"),
    //             'model' => fn($value) => $query->where('model', 'like', "%$value%"),
    //         ];

    //         foreach ($filters as $field => $callback)
    //         {
    //             if ($request->filled($field))
    //             {
    //                 $callback($request->input($field));
    //             }
    //         }

    //         $vehicles = $query->get();

    //         return response()->json($vehicles);
    //     }
    //     else
    //     {
    //         return response()->json(['message'=>'Unauthorised']);
    //     }
    // }
    public function index_by_client(Request $request, string $clientId)
    {
        $user = $request->user();

        // Admin: can access any client
        // Broker: can only access their own clients
        $clientQuery = Client::query()->where('client_id', $clientId);

        if ($user->isBroker()) {
            $clientQuery->where('broker_id', $user->user_id);
        }

        $client = $clientQuery->first();

        // if (! $client) {
        //     return response()->json(['message' => 'Client not found'], 404);
        // }
        if (! $client) {
            return $user->isBroker()
                ? response()->json(['message' => 'Forbidden'], 403)
                : response()->json(['message' => 'Client not found'], 404);
        }

        // Get vehicles for the client
        $query = $client->vehicles();

        // Apply optional filters
        if ($request->filled('registration_number')) {
            $query->where('registration_number', $request->registration_number);
        }

        if ($request->filled('make')) {
            $query->where('make', 'like', '%'.$request->make.'%');
        }

        if ($request->filled('model')) {
            $query->where('model', 'like', '%'.$request->model.'%');
        }

        return response()->json($query->get());
    }

    /**
     * @OA\Post(
     *     path="api/v1/vehicles",
     *     summary="Create a new vehicle",
     *     description="Stores a new vehicle in the system.",
     *     operationId="createVehicle",
     *     tags={"Vehicles"},
     *     security={{"bearerAuth":{}}},
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(
     *             required={"client_id", "make", "model", "registration_number", "mileage", "value", "cover_type"},
     *
     *             @OA\Property(property="client_id", type="integer", example=101),
     *             @OA\Property(property="make", type="string", example="Toyota"),
     *             @OA\Property(property="model", type="string", example="Corolla"),
     *             @OA\Property(property="registration_number", type="string", example="KDA123A"),
     *             @OA\Property(property="mileage", type="integer", example=45000),
     *             @OA\Property(property="value", type="number", format="float", example=1200000.50),
     *             @OA\Property(property="cover_type", type="string", example="Comprehensive")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=201,
     *         description="Vehicle created successfully",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="vehicle create success"),
     *             @OA\Property(property="vehicle", ref="#/components/schemas/Vehicle")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=500,
     *         description="Error creating vehicle",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Error creating vehicle")
     *         )
     *     )
     * )
     */
    public function store(Request $request)
    {
        $current_user = $request->user();

        $validated = $request->validate([
            'client_id' => 'required|exists:IBMS_CLIENTS,client_id',
            'make' => 'required|string',
            'model' => 'required|string',
            'registration_number' => 'required|string|unique:IBMS_VEHICLES,registration_number',
            'mileage' => 'required|integer',
            'value' => 'required|numeric',
            'vehicle_purpose' => 'required|string',
            'cover_type' => 'sometimes|string:IBMS_PRODUCT_COVER_TYPES,category_product_name',
        ]);

        if ($current_user->isAdmin()) {
            if ($vehicle = Vehicle::create($validated)) {
                return response()->json([
                    'message' => 'vehicle create success',
                    'vehicle' => $vehicle,
                ], 201);
            }
        } elseif ($current_user->isBroker()) {
            if (Client::where('broker_id', $current_user->user_id)->where('client_id', $request->client_id)->exists()) {
                if ($vehicle = Vehicle::create($validated)) {
                    return response()->json([
                        'message' => 'vehicle create success',
                        'vehicle' => $vehicle,
                    ], 201);
                }
            }
        } else {
            return response()->json([
                'message' => 'Error creating vehicle or unauthorised',
            ], 500);
        }

    }

    /**
     * @OA\Get(
     *     path="api/v1/vehicles/{id}",
     *     summary="Get a vehicle by ID",
     *     description="Returns a single vehicle by its ID.",
     *     operationId="getVehicleById",
     *     tags={"Vehicles"},
     *     security={{"bearerAuth":{}}},
     *
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="ID of the vehicle",
     *         required=true,
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Vehicle found",
     *
     *         @OA\JsonContent(ref="#/components/schemas/Vehicle")
     *     ),
     *
     *     @OA\Response(
     *         response=404,
     *         description="Vehicle not found",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Vehicle not found")
     *         )
     *     )
     * )
     */
    public function show(Request $request, int $id)
    {
        $current_user = $request->user();

        if ($current_user->isBroker()) {
            $brokerId = $current_user->user_id;
            $vehicle = Vehicle::where('vehicle_id', $id)
                ->whereHas('client', function ($query) use ($brokerId) {
                    $query->where('broker_id', $brokerId);
                })
                ->first();

            if (! $vehicle) {
                return response()->json(['message' => 'Vehicle not found or unauthorized'], 404);
            }

            return response()->json($vehicle);
        } elseif ($current_user->isAdmin()) {
            $vehicle = Vehicle::where('vehicle_id', $id)
                ->first();

            if (! $vehicle) {
                return response()->json(['message' => 'Vehicle not found or unauthorized'], 404);
            }

            return response()->json($vehicle);
        } else {
            return response()->json(['message' => 'Vehicle not found or unauthorized'], 404);
        }

    }

    /**
     * @OA\Put(
     *     path="/api/v1/vehicles/{id}",
     *     summary="Update a vehicle",
     *     description="Updates the details of a vehicle by its ID. Only the provided fields will be updated.",
     *     operationId="updateVehicle",
     *     tags={"Vehicles"},
     *     security={{"bearerAuth":{}}},
     *
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="ID of the vehicle to update",
     *         required=true,
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\RequestBody(
     *         required=true,
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="make", type="string", example="Toyota"),
     *             @OA\Property(property="model", type="string", example="Corolla"),
     *             @OA\Property(property="IBMS_VEHICLE_REG_NUMBER", type="string", example="KDA123A"),
     *             @OA\Property(property="mileage", type="integer", example=45000),
     *             @OA\Property(property="value", type="number", format="float", example=1200000.50),
     *             @OA\Property(property="cover_type", type="string", example="Comprehensive")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Vehicle updated successfully",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Vehicle updated successfully"),
     *             @OA\Property(property="vehicle", ref="#/components/schemas/Vehicle")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=404,
     *         description="Vehicle not found",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Vehicle not found")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=500,
     *         description="Error updating vehicle",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Error updating vehicle")
     *         )
     *     )
     * )
     */
    public function update(Request $request, int $id)
    {

        $current_user = $request->user();

        if ($current_user->isBroker()) {
            $brokerId = $current_user->user_id;
            $vehicle = Vehicle::where('vehicle_id', $id)
                ->whereHas('client', function ($query) use ($brokerId) {
                    $query->where('broker_id', $brokerId);
                })
                ->first();

            if (! $vehicle) {
                return response()->json(['message' => 'Vehicle not found or unauthorized'], 404);
            }

            $validated = $request->validate([
                'make' => 'sometimes|string',
                'model' => 'sometimes|string',
                'registration_number' => 'sometimes|string|unique:IBMS_VEHICLES,registration_number,'.$id.',vehicle_id',
                'mileage' => 'sometimes|integer',
                'value' => 'sometimes|numeric',
                'cover_type' => 'sometimes|string',
            ]);

            if ($vehicle->update($validated)) {
                return response()->json([
                    'message' => 'Vehicle updated successfully',
                    'vehicle' => $vehicle,
                ]);
            }
        } elseif ($current_user->isAdmin()) {
            $vehicle = Vehicle::where('vehicle_id', $id)
                ->first();

            if (! $vehicle) {
                return response()->json(['message' => 'Vehicle not found or unauthorized'], 404);
            }

            $validated = $request->validate([
                'make' => 'sometimes|string',
                'model' => 'sometimes|string',
                'registration_number' => 'sometimes|string|unique:IBMS_VEHICLES,registration_number,'.$id.',vehicle_id',
                'mileage' => 'sometimes|integer',
                'value' => 'sometimes|numeric',
                'cover_type' => 'sometimes|string',
            ]);

            if ($vehicle->update($validated)) {
                return response()->json([
                    'message' => 'Vehicle updated successfully',
                    'vehicle' => $vehicle,
                ]);
            }
        } else {
            return response()->json([
                'message' => 'Error updating vehicle',
            ], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/vehicles/{id}",
     *     summary="Delete a vehicle",
     *     description="Deletes a vehicle by its ID.",
     *     operationId="deleteVehicle",
     *     tags={"Vehicles"},
     *     security={{"bearerAuth":{}}},
     *
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="ID of the vehicle to delete",
     *         required=true,
     *
     *         @OA\Schema(type="integer")
     *     ),
     *
     *     @OA\Response(
     *         response=200,
     *         description="Vehicle deleted successfully",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Vehicle deleted successfully")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=404,
     *         description="Vehicle not found",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Vehicle not found")
     *         )
     *     ),
     *
     *     @OA\Response(
     *         response=500,
     *         description="Error deleting vehicle",
     *
     *         @OA\JsonContent(
     *
     *             @OA\Property(property="message", type="string", example="Error deleting vehicle")
     *         )
     *     )
     * )
     */
    public function destroy(Request $request, int $id)
    {
        $current_user = $request->user();

        if ($current_user->isBroker()) {
            $brokerId = $current_user->user_id;
            $vehicle = Vehicle::where('vehicle_id', $id)
                ->whereHas('client', function ($query) use ($brokerId) {
                    $query->where('broker_id', $brokerId);
                })
                ->first();

            if (! $vehicle) {
                return response()->json(['message' => 'Vehicle not found or Unauthorised'], 404);
            }

            if ($vehicle->delete()) {
                return response()->json(['message' => 'Vehicle deleted successfully'], 200);
            }
        } elseif ($current_user->isAdmin()) {
            $vehicle = Vehicle::where('vehicle_id', $id)
                ->first();

            if (! $vehicle) {
                return response()->json(['message' => 'Vehicle not found or Unauthorised'], 404);
            }

            if ($vehicle->delete()) {
                return response()->json(['message' => 'Vehicle deleted successfully'], 200);
            }
        } else {
            return response()->json(['message' => ' Error deleting vehicle deleted'], 500);
        }

    }
}
