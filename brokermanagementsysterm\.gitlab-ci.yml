stages:
  - quality
  - test

variables:
  COMPOSER_ALLOW_SUPERUSER: "1"
  DB_CONNECTION: sqlite
  DB_DATABASE: ":memory:"

# Linting with Laravel Pint
quality:
  image: composer:latest
  stage: quality
  script:
    - composer install --no-interaction --prefer-dist --no-progress
    - ./vendor/bin/pint --test
  only:
    - merge_requests
    - main

# PHPUnit Tests
test:
  image: gethomesafe/gitlab-ci-pipeline-php:alpine
  services:
    - mysql:latest
  stage: test
  before_script:
    - cp .env.example .env
    - composer install --no-interaction --prefer-dist --no-progress
    - php artisan key:generate
    - php artisan migrate --force
  script:
    - php artisan test
  only:
    - merge_requests
    - main

