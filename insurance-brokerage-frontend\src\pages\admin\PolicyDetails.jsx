import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { <PERSON>, PageHeader, Button } from "../../components";
import { policyService, coverTypeService, insuranceService } from "../../services";

export default function PolicyDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [policy, setPolicy] = useState(null);
  const [coverType, setCoverType] = useState(null);
  const [insurer, setInsurer] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      try {
        const policyData = await policyService.getPolicy(id);
        setPolicy(policyData);
        const [coverTypeData, insurerData] = await Promise.all([
          coverTypeService.getCoverType(policyData.coverTypeId),
          insuranceService.getCompany(policyData.insurerId),
        ]);
        setCoverType(coverTypeData);
        setInsurer(insurerData);
      } catch (err) {
        setError("The requested policy does not exist.");
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading policy details...</p>
        </div>
      </div>
    );
  }

  if (error || !policy) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card title="Policy Not Found">
          <p className="text-gray-600">{error}</p>
          <div className="mt-4 flex justify-end">
            <Button variant="outline" onClick={() => navigate("/admin/policies")}>Back to List</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
        <PageHeader
          title={policy.name}
          subtitle="Policy Details"
          breadcrumbs={[
            { label: "Dashboard", href: "/admin/dashboard" },
            { label: "Insurers", href: "/admin/insurers" },
            { label: insurer.name, href: "/admin/insurers/" + policy.insurerId },
            { label: policy.name },
          ]}
          actions={[
            {
              label: "Edit",
              variant: "primary",
              onClick: () => navigate(`/admin/policies/${policy.id}/edit`),
            },
          ]}
        />
        <Card title="Policy Information">
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Policy Name</label>
              <p className="text-sm text-gray-900">{policy.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Cover Type</label>
              <p className="text-sm text-gray-900">{coverType ? coverType.name : "Unknown"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Insurer</label>
              <p className="text-sm text-gray-900">{insurer ? insurer.name : "Unknown"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Base Premium</label>
              <p className="text-sm text-green-700 font-semibold">{policy.basePremium ? policy.basePremium.toLocaleString() : "-"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Sum Insured</label>
              <p className="text-sm text-blue-700 font-semibold">{policy.sumInsured ? policy.sumInsured.toLocaleString() : "-"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Commission Rate</label>
              <p className="text-sm text-purple-700 font-semibold">{policy.commissionRate ? policy.commissionRate + "%" : "-"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Vehicle Types</label>
              <p className="text-sm text-gray-900">{Array.isArray(policy.vehicleTypes) ? policy.vehicleTypes.join(", ") : "-"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Benefits</label>
              <p className="text-sm text-gray-900">{policy.benefits || "-"}</p>
            </div>
            {/* <div>
              <label className="text-sm font-medium text-gray-500">Premium Rate</label>
              <p className="text-sm text-green-700 font-semibold">{policy.premiumRate ? policy.premiumRate.toLocaleString() + "%" : "-"}</p>
            </div> */}
            <div>
              <label className="text-sm font-medium text-gray-500">Status</label>
              <p className={`text-sm font-semibold ${policy.status === "Active" ? "text-green-700" : "text-gray-600"}`}>{policy.status}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Created At</label>
              <p className="text-sm text-gray-900">{new Date(policy.createdAt).toLocaleString()}</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
