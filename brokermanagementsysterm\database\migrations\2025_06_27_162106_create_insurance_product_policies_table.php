<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('IBMS_INSURANCE_PRODUCT_POLICIES', function (Blueprint $table) {
            $table->uuid('insurance_product_policy_id')->primary()->unique();

            $table->foreignUuid('insurance_category_product_id')
                ->constrained('IBMS_INSURANCE_CATEGORY_PRODUCTS', 'insurance_category_product_id')
                ->notnull();
            $table->string('policy_code');
            $table->decimal('base_premium');
            $table->decimal('sum_insured');
            $table->decimal('commission_rate');
            $table->boolean('vehicle_commercial')->nullable();
            $table->boolean('vehicle_private')->nullable();
            $table->boolean('vehicle_motorcycle')->nullable();
            $table->boolean('vehicle_special')->nullable();
            $table->boolean('is_active')->default('false');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('IBMS_INSURANCE_PRODUCT_POLICIES');
    }
};
