<?php

// database/factories/InsuranceProductPolicyFactory.php

namespace Database\Factories;

use App\Models\InsuranceProductPolicy;
use Illuminate\Database\Eloquent\Factories\Factory;

class InsuranceProductPolicyFactory extends Factory
{
    protected $model = InsuranceProductPolicy::class;

    public function definition()
    {
        return [
            'insurance_product_policy_id' => fake()->uuid(),
            'insurance_category_product_id' => \App\Models\InsuranceCategoryProduct::factory(),
            'policy_code' => 'POL-'.fake()->unique()->numberBetween(1000, 9999),
            'base_premium' => fake()->randomFloat(2, 100, 10000),
            'sum_insured' => fake()->randomFloat(2, 1000, 100000),
            'commission_rate' => fake()->randomFloat(2, 0.01, 0.3),
            'is_active' => true,
        ];
    }
}
