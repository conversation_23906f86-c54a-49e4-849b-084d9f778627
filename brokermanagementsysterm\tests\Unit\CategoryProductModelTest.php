<?php

namespace Tests\Unit;

use App\Models\Category;
use App\Models\CategoryProduct;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CategoryProductModelTest extends TestCase
{
    /**
     * A basic unit test example.
     */
    use RefreshDatabase;

    public function test_example(): void
    {
        $this->assertTrue(true);
    }

    public function test_create_cover_type(): void
    {

        Category::factory()->create();
        $cover = CategoryProduct::factory()->create([
            'category_product_name' => 'Third Party',
            'category_product_description' => 'Basic third party cover',
            'category_product_isactive' => true,
            'category_product_code' => 101,
            'category_id' => Category::first()->category_id,
        ]);

        $this->assertDatabaseHas('IBMS_CATEGORY_PRODUCTS', [
            'category_product_name' => 'Third Party',
        ]);

    }

    public function test_category_attr(): void
    {
        $this->test_create_cover_type();

        $cover = CategoryProduct::first();

        $this->assertNotNull($cover->category());
        $this->assertTrue($cover->category->is(Category::first()));

    }
}
