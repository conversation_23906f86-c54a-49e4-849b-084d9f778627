import React, { useState, useEffect } from "react";
import { <PERSON>H<PERSON>er, Card, DataTable, Button, Alert } from "../../components";
import { useNavigate, useLocation } from "react-router-dom";
import { vehicleService } from "../../services/vehicleService";
import Modal from "../../components/Modal";

const VehicleDetails = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const client = location.state?.client || null;
  const [vehicles, setVehicles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [vehicleToDelete, setVehicleToDelete] = useState(null);

  // Defensive check: If client is null, show error and prevent vehicle list rendering
  if (!client) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center py-6 px-2 sm:py-10 sm:px-4 w-full">
        <Alert
          type="error"
          title="No Client Selected"
          message="No client selected. Please select a client first."
        />
      </div>
    );
  }

  console.log('client data', client);
  console.log('vehicles data', vehicles);
  

  // Alert state
  const [alert, setAlert] = useState({
    show: false,
    type: "info",
    title: "",
    message: "",
  });

  // Table columns
  const columns = [
    { key: "make", label: "Make", sortable: true },
    { key: "model", label: "Model", sortable: true },
    { key: "registration_number", label: "Registration No.", sortable: true },
    { key: "vehicle_purpose", label: "Usage", sortable: true },
    { key: "value", label: "Value", type: "currency", sortable: true },
    {
      key: "actions",
      label: "Action",
      sortable: false,
      render: (value, item) => (
        <div className="flex gap-2">
          <Button size="small" variant="outline" onClick={() => handleTableAction("view", item)}>View</Button>
          <Button size="small" variant="primary" onClick={() => handleTableAction("edit", item)}>Edit</Button>
          <Button size="small" variant="danger" onClick={() => { setVehicleToDelete(item); setShowDeleteModal(true); }}>Delete</Button>
        </div>
      ),
      truncate: false,
    },
  ];
  useEffect(() => {
        const fetchVehicles = async () => {
          try {
            const vehicles_res = await vehicleService.getVehiclesByClient(client);
            setVehicles(vehicles_res);
            console.log('vehicles data', vehicles_res);
          } catch (error) {
            console.error('Failed to fetch vehicles', error);
          }
        };

        fetchVehicles();
      }, []);
    
       useEffect(() => {
        // if (userRes) {
         if (Object.keys(vehicles).length === 0 && vehicles.constructor === Object) {
           setLoading(true);
         }
         else {
           setLoading(false);
    
         }
       }, [vehicles]);

  // Action handlers
  const handleTableAction = async (action, item) => {
    if (action === "view") {
      navigate(`/broker/vehicles/${item.vehicle_id || item.id}`, { state: { vehicle: item, client } });
      return;
    }
    if (action === "edit") {
      navigate("/broker/vehicles/edit", { state: { vehicle: item, client } });
      return;
    }
    let type = "info",
      title = "",
      message = "";
    if (action === "delete") {
      type = "error";
      // Call delete service
      const response = await vehicleService.deleteVehicle(item.vehicle_id);
      if (response === 200 || response === 204) {
        // Remove item from state
        setVehicles((prev) => prev.filter((v) => v.vehicle_id !== item.vehicle_id));
        title = "Deleted";
        message = `Deleted ${item.make} ${item.model} (${item.registration_number})`;
      }
      else {
        title = "Error";
        message = "Failed to delete vehicle";
      }
      
    }
    setAlert({ show: true, type, title, message });
  };

  const handleDeleteVehicle = async () => {
    if (!vehicleToDelete) return;
    const response = await vehicleService.deleteVehicle(vehicleToDelete.vehicle_id);
    if (response === 200 || response === 204) {
      setVehicles((prev) => prev.filter((v) => v.vehicle_id !== vehicleToDelete.vehicle_id));
      setAlert({ show: true, type: "error", title: "Deleted", message: `Deleted ${vehicleToDelete.make} ${vehicleToDelete.model} (${vehicleToDelete.registration_number})` });
    } else {
      setAlert({ show: true, type: "error", title: "Error", message: "Failed to delete vehicle" });
    }
    setShowDeleteModal(false);
    setVehicleToDelete(null);
  };

  // Action button with dropdown
  const VehicleActions = ({ item }) => {
    const [open, setOpen] = useState(false);
    // Close dropdown on click outside only (not on scroll)
    React.useEffect(() => {
      if (!open) return;
      const close = (e) => {
        if (!e.target.closest(`#dropdown-actions-${item.id}`)) setOpen(false);
      };
      window.addEventListener("click", close, true);
      return () => {
        window.removeEventListener("click", close, true);
      };
    }, [open, item.id]);
    return (
      <div
        className="relative inline-block text-left"
        id={`dropdown-actions-${item.id}`}
      >
        <Button
          variant="warning"
          size="small"
          className="flex items-center gap-1"
          onClick={(e) => {
            e.stopPropagation();
            setOpen((v) => !v);
          }}
        >
          Actions
          <svg
            className="w-4 h-4 ml-1 text-white"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </Button>
        {open && (
          <div
            className="absolute right-0 mt-2 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-50"
            style={{ minWidth: '8rem' }}
          >
            <button
              className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
              onClick={(e) => {
                e.stopPropagation();
                setOpen(false);
                handleTableAction("view", item);
              }}
            >
              View
            </button>
            <button
              className="w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
              onClick={(e) => {
                e.stopPropagation();
                setOpen(false);
                handleTableAction("edit", item);
              }}
            >
              Edit
            </button>
            <button
              className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
              onClick={(e) => {
                e.stopPropagation();
                setOpen(false);
                handleTableAction("delete", item);
              }}
            >
              Delete
            </button>
          </div>
        )}
      </div>
    );
  };

  // Add new vehicle handler
  // Removed unused handleAddVehicle function

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col sm:flex-row">
      {/* Sidebar is fixed at w-64, so add margin-left on desktop */}
      {/* <div className="flex-1 flex flex-col items-center justify-center py-6 px-2 w-full"> */}
        <div className="w-full max-auto">
          <PageHeader
            title={client ? `${client.first_name} ${client.last_name} - Vehicle List` : "Vehicle List"}
            subtitle={client ? `List of vehicles for ${client.first_name} ${client.last_name}` : "List of client vehicles and their details"}
            breadcrumbs={[
              { label: "Broker Dashboard", href: "/broker/dashboard" },
              { label: 'Clients', href: "/broker/clients" },
              // client && { label: `${client.firstName} ${client.lastName}`, href: `/broker/clients/${client.id}` },
              { label: "Vehicle List" },
            ].filter(Boolean)}
            actions={[
              client && {
                label: "Add New Vehicle",
                variant: "primary",
                onClick: () => navigate("/broker/vehicles/create", { state: { client } }),
              },
            ].filter(Boolean)}
          />
          <div className="mt-8">
            <Card
              title="Vehicle List"
              subtitle="Advanced table with sorting, searching, and pagination"
            >
              <div className="overflow-x-auto" style={{ maxHeight: '60vh', overflowY: 'auto' }}>
                <DataTable
                  data={vehicles}
                 
                  columns={columns}
                  searchable={true}
                  pagination={true}
                  itemsPerPage={5}
                  // Remove default actions, handled by custom column
                />
              </div>
            </Card>
          </div>
        </div>
        {/* Alert */}
        {alert.show && (
          <div className="fixed top-5 right-2 sm:right-5 z-[2000] min-w-[250px] max-w-xs w-auto">
            <Alert
              type={alert.type}
              title={alert.title}
              message={alert.message}
              onClose={() => setAlert({ ...alert, show: false })}
            />
          </div>
        )}
      
      {/* Delete Modal */}
      <div>
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete Vehicle"
        size="small"
      >
        <div className="space-y-4">
          <p>Are you sure you want to delete <strong>Vehicle {vehicleToDelete?.make} {vehicleToDelete?.model} ({vehicleToDelete?.registration_number})</strong>?</p>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setShowDeleteModal(false)}>Cancel</Button>
            <Button variant="danger" onClick={handleDeleteVehicle}>Delete</Button>
          </div>
        </div>
      </Modal>
      </div>
    </div>
  );
};

export default VehicleDetails;
