<?php

namespace Tests\Unit;

use App\Models\Client;
use App\Models\ClientPreferences;
use App\Models\RoleSetupService;
use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ClientPreferencesModelTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_client_preferences()
    {
        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();

        $this->assertNotNull($testBroker->user_id);

        $client = Client::factory()->create([
            'broker_id' => $testBroker->user_id,
        ]);

        $this->assertTrue(($client != null));
        $vehicle = Vehicle::factory()->create(['client_id' => $client->client_id]);
        $preferences = ClientPreferences::factory()->create(['vehicle_id' => $vehicle->vehicle_id]);

        $this->assertDatabaseHas('IBMS_CLIENT_PREFERENCES', [
            'vehicle_id' => $vehicle->vehicle_id,
        ]);
    }

    public function test_preferences_have_correct_default_structure()
    {
        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();

        $this->assertNotNull($testBroker->user_id);

        $client = Client::factory()->create([
            'broker_id' => $testBroker->user_id,
        ]);
        $vehicle = Vehicle::factory()->create(['client_id' => $client->client_id]);

        $preferences = ClientPreferences::factory()->create([
            'vehicle_id' => $vehicle->vehicle_id,
            'preferred_cover_types' => ['comprehensive'], // now a JSON array
            'preferred_channel' => 'email',
            'renewal_reminder_enabled' => true,
            'budget_min' => 1000,
            'budget_max' => 5000,
        ]);

        $this->assertIsArray($preferences->preferred_cover_types);
        $this->assertContains('comprehensive', $preferences->preferred_cover_types);
        $this->assertEquals('email', $preferences->preferred_channel);
        $this->assertTrue($preferences->renewal_reminder_enabled);
        $this->assertGreaterThanOrEqual(1000, $preferences->budget_min);
        $this->assertLessThanOrEqual(5000, $preferences->budget_max);
    }

    public function test_preferences_belongs_to_vehicle()
    {
        $rolesetupService = new RoleSetupService;

        $rolesetupService::createRoles();
        $testBroker = User::factory()->broker()->create();

        $this->assertNotNull($testBroker->user_id);

        $client = Client::factory()->create([
            'broker_id' => $testBroker->user_id,
        ]);
        $vehicle = Vehicle::factory()->create(['client_id' => $client->client_id]);
        $preferences = ClientPreferences::factory()->create([
            'vehicle_id' => $vehicle->vehicle_id,
        ]);

        $this->assertNotNull($preferences->vehicle);
        $this->assertInstanceOf(Vehicle::class, $preferences->vehicle);
    }
}
