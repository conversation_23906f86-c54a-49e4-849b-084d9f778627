<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\CategoryProduct;
use Illuminate\Database\Seeder;

class CategoryProductSeeder extends Seeder
{
    public function run()
    {
        $motorCategory = Category::where('category_name', 'MOTOR_INSURANCE')->first();
        $healthCategory = Category::where('category_name', 'HEALTH_INSURANCE')->first();

        $products = [
            // Motor Insurance Products
            [
               // 'category_name' => 'COMPREHENSIVE_MOTOR',
                'category_product_name' => 'COMPREHENSIVE_MOTOR',
                'category_product_description' => 'Full coverage for private vehicles',
                'category_product_code' => 1001,
                'category_id' => $motorCategory->id,
            ],
            [
                'category_product_name' => 'THIRD_PARTY_MOTOR',
                'category_product_description' => 'Basic third-party liability coverage',
                'category_product_code' => 1002,
                'category_id' => $motorCategory->id,
            ],

            // Health Insurance Products
            [
                'category_name' => 'FAMILY_HEALTH',
                'category_description' => 'Comprehensive health coverage for families',
                'category_product_code' => 2001,
                'category_id' => $healthCategory->id,
            ],
            [
                'category_name' => 'INDIVIDUAL_HEALTH',
                'category_description' => 'Personal health insurance plan',
                'category_product_code' => 2002,
                'category_id' => $healthCategory->id,
            ],
        ];

        foreach ($products as $product) {
            CategoryProduct::firstOrCreate(
                ['category_name' => $product['category_name']],
                $product
            );
        }
    }
}
