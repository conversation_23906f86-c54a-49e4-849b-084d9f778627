<?php

namespace App\Http\Controllers;

use App\Models\Policy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PolicyController extends Controller
{
    public function store(Request $request): JsonResponse
    {

        $validated = Validator::make($request->all(), [

            'product_cover_type_id' => ['required', 'string', 'max:255'],
            'insurer_id' => ['required', 'string', 'max:255'],
            'details' => ['required', 'string', 'max:255'],
            'premium_rate' => ['required', 'string', 'max:255'],

        ]);

        if ($validated->fails()) {
            return response()->json(['status' => 'error', 'errors' => $validated->errors()]);
        }
        try {
            $policy = Policy::create([
                'product_cover_type_id' => $request->input('name'),
                'insurer_id' => $request->input('description'),
                'details' => $request->input('details'),
                'premium_rate' => $request->input('premium_rate'),
            ]);

            return response()->json(['status' => 'success', 'message' => ' created successfully']);
        } catch (\Exception $e) {
            Log::debug($e);

            return response()->json([
                'status' => 'error',
                'message' => 'An Error Occured. Contact Adminstrator',
                'errors' => [$e->getMessage()],
            ], 422);
        }

    }

    public function edit($policy_id): JsonResponse
    {
        $data = Policy::findOrFail($policy_id);

        return response()->json($data);
    }

    public function show($policy_id): JsonResponse
    {
        $data = Policy::findOrFail($policy_id);

        return response()->json($data);
    }

    public function update(Request $request): JsonResponse
    {
        $validated_details = $request->all();
        // return response()->json($validated_details);
        $validated = Validator::make($request->all(), [
            'product_cover_type_id' => ['required', 'string', 'max:255'],
            'insurer_id' => ['required', 'string', 'max:255'],
            'details' => ['required', 'string', 'max:255'],
            'premium_rate' => ['required', 'string', 'max:255'],

        ]);

        if ($validated->fails()) {
            return response()->json(['status' => 'error', 'errors' => $validated->errors()]);
        }

        try {
            $policy = Policy::findOrFail($request->input('policy_id'));

            $updated_policy = $policy->update([
                'product_cover_type_id' => $request->input('name'),
                'insurer_id' => $request->input('description'),
                'details' => $request->input('details'),
                'premium_rate' => $request->input('premium_rate'),

            ]);

            // event(new Registered($policy));

            return response()->json(['status' => 'success', 'policy_id' => $request->input('policy_id'), 'message' => $policy['name'].' updated successfully']);
        } catch (\Exception $e) {
            Log::debug($e);

            return response()->json([
                'status' => 'error',
                'message' => 'An Error Occured. Contact Adminstrator',
                'errors' => [$e->getMessage()],
            ], 422);
        }
    }

    public function destroy($policy_id)
    {
        try {
            $policy = Policy::withoutTrashed()->find($policy_id);

            if (! $policy) {

                return response()->json(['status' => 'error', 'message' => 'Policy already deactivated']);
            }
            $policy->delete();

            return response()->json(['status' => 'success', 'message' => 'Policy deleted successfully']);
        } catch (\Exception $e) {
            Log::debug($e);

            return response()->json(['status' => 'error', 'message' => 'An error occurred', 'errors' => [$e->getMessage()]]);
        }
    }

    public function restore($policy_id)
    {

        try {
            $policy = Policy::onlyTrashed()->find($policy_id);
            if (! $policy) {
                return response()->json(['status' => 'error', 'message' => 'Policy already active']);
            }
            $policy->restore();

            return response()->json(['status' => 'success', 'message' => $policy['name'].' restored successfully']);
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => 'An error occurred', 'errors' => [$e->getMessage()]]);
        }
    }
}
