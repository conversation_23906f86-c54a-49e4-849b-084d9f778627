<?php

namespace Database\Factories;

use App\Models\ClientPreferences;
use App\Models\Insurance;
use App\Models\RoleSetupService;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClientPreferencesFactory extends Factory
{
    protected $model = ClientPreferences::class;

    public function definition(): array
    {
        RoleSetupService::createRoles();

        $insurers = Insurance::inRandomOrder()->take(2)->pluck('insurance_id')->toArray();
        $blacklisted = Insurance::inRandomOrder()->take(1)->pluck('insurance_id')->toArray();

        return [
            'preferred_cover_types' => $this->faker->randomElements(['comprehensive', 'third_party', 'health', 'life'], rand(1, 2)),
            'preferred_insurers' => $insurers,
            'blacklisted_insurers' => $blacklisted,
            'preferred_channel' => $this->faker->randomElement(['email', 'phone', 'whatsapp']),
            'preferred_contact_time' => $this->faker->randomElement(['morning', 'afternoon', 'evening']),
            'allow_marketing_emails' => $this->faker->boolean(),
            'auto_renewal_enabled' => $this->faker->boolean(),
            'renewal_reminder_enabled' => $this->faker->boolean(),
            'days_before_renewal_reminder' => $this->faker->numberBetween(1, 30),
            'budget_min' => $this->faker->randomFloat(2, 1000, 10000),
            'budget_max' => $this->faker->randomFloat(2, 15000, 50000),
            'vehicle_usage' => $this->faker->randomElement(['personal', 'commercial']),
            'preferred_workshops' => [$this->faker->company, $this->faker->company],
            'digital_document_delivery' => $this->faker->boolean(),
            'physical_document_delivery' => $this->faker->boolean(),
            'require_detailed_explanation' => $this->faker->boolean(),
            'profile_score' => $this->faker->numberBetween(0, 100),
            'last_updated_by_broker' => $this->faker->name,
        ];
    }
}
